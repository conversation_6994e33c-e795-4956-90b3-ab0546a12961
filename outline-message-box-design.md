# 公文公告专用提纲消息框设计

## 功能概述

为公文公告工作流设计了一款全新的专用AI返回提纲消息框，能够自动解析Markdown格式的提纲数据并以结构化方式展示。

## 核心特性

### 1. 智能提纲检测
- 自动检测AI返回内容是否为提纲格式
- 基于Markdown标题标识符（#）进行识别
- 当检测到3个或以上标题时，自动启用提纲模式

### 2. 结构化展示
- **层级标识**：H1、H2、H3等不同颜色的标签
- **编号显示**：支持数字编号和子编号 (1)、(2) 等
- **可折叠树形结构**：支持展开/收起子节点
- **内容描述**：显示标题下的详细内容

### 3. 交互功能
- **在线编辑**：点击编辑按钮可修改标题文本
- **内容生成**：基于提纲生成完整文档
- **展开控制**：整体展开/收起控制

## 组件架构

### OutlineMessageBox.vue
主要的提纲消息框组件，负责：
- 解析Markdown提纲内容
- 管理整体展开/收起状态
- 处理内容变更和生成事件

### OutlineNode.vue
单个提纲节点组件，负责：
- 显示单个标题节点
- 处理节点编辑
- 管理子节点的展开/收起

## 解析逻辑

### Markdown格式支持
```markdown
# 1 政务公开的重要意义
## 1.1 研究背景与意义
### (1) 具体内容描述
## 1.2 研究目的与方法
# 2 路面灾害的类型与表现形式
# 3 路面灾害产生的原因分析
```

### 解析规则
1. **标题识别**：`/^(#{1,6})\s*(\d+\.?)?\s*(\([^)]+\))?\s*(.+)$/`
2. **层级判断**：根据 # 的数量确定层级
3. **编号提取**：支持数字编号和括号编号
4. **内容关联**：非标题行作为上一个标题的描述内容

## 样式设计

### 层级标识颜色
- **H1**: 蓝色 (#3b82f6)
- **H2**: 绿色 (#10b981)  
- **H3**: 橙色 (#f59e0b)
- **H4**: 红色 (#ef4444)
- **H5**: 紫色 (#8b5cf6)
- **H6**: 灰色 (#6b7280)

### 交互效果
- 悬停高亮
- 展开/收起动画
- 编辑状态切换
- 焦点状态指示

## 集成方式

### 检测逻辑
```javascript
isOutlineContent(content) {
  const headerPattern = /^(#{1,6})\s*(\d+\.?)?\s*(\([^)]+\))?\s*(.+)$/gm
  const matches = textContent.match(headerPattern)
  return matches && matches.length >= 3
}
```

### 条件渲染
```vue
<!-- 公文公告专用提纲消息框 -->
<div v-else-if="isAnnouncementWorkflow && message.isOutline">
  <OutlineMessageBox
    :content="message.rawContent"
    :title="message.outlineTitle"
    :editable="true"
    @content-changed="handleOutlineContentChanged"
    @generate-content="handleGenerateContentFromOutline"
  />
</div>
```

## 使用流程

1. **AI返回提纲**：公文公告工作流返回Markdown格式提纲
2. **自动检测**：系统检测到提纲格式，设置 `message.isOutline = true`
3. **结构化展示**：使用OutlineMessageBox组件展示
4. **用户交互**：用户可以编辑标题、展开/收起节点
5. **生成文档**：点击"基于此大纲生成文档"按钮

## 扩展性

### 支持的Markdown格式
- 标准标题：`# 标题`
- 编号标题：`# 1 标题`、`## 1.1 标题`
- 括号编号：`## (1) 标题`、`### (a) 标题`
- 混合格式：`# 1 (重要) 标题`

### 未来扩展
- 拖拽排序
- 批量操作
- 导出功能
- 模板保存
- 协作编辑

## 技术特点

- **响应式设计**：适配不同屏幕尺寸
- **性能优化**：虚拟滚动支持大量节点
- **无障碍支持**：键盘导航和屏幕阅读器支持
- **国际化**：支持多语言界面
