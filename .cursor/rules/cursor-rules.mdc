---
description: 
globs: 
alwaysApply: false
---

# 语⾔和响应格式：
请⽤中⽂回复
<!-- 每次对话结束后进⾏总结，并将总结追加到 README.md ⽂件中 -->

# 项⽬规范：
前端技术栈规范:
- 使⽤ Vue 3 + JavaScript
- 使⽤ Composition API ⽽不是 Options API
- 使⽤ Tailwind CSS 进⾏样式设计
- 组件命名使⽤ PascalCase
- Props 命名使⽤ camelCase
- 事件命名使⽤ kebab-case

# 代码注释规范:
- 所有函数必须有⽂档字符串说明功能和参数
- 复杂的业务逻辑需要添加必要的注释
- 注释使⽤中⽂，便于团队理解

# ⽂件组织规范:
- 前端组件按功能模块分类存放
- 配置⽂件统⼀放在 config ⽬录
- 静态资源放在 public ⽬录

# 版本控制规范:
- 使⽤ Git 进⾏版本控制

# 项⽬索引规则
忽略以下⽂件和⽬录:
- node_modules/
- __pycache__/
- .venv/
- dist/
- .env
- *.pyc
- *.log
- .DS_Store



