# 提纲删除功能实现

## 功能概述

为公文公告提纲消息框新增了删除功能，用户可以删除任意一行的标题来调整文章结构，为后续生成文章提供更精确的大纲。

## 核心特性

### 1. 删除按钮
- 每个提纲节点都有独立的删除按钮
- 悬停时显示，平时隐藏，保持界面简洁
- 红色删除图标，直观易识别

### 2. 确认机制
- 点击删除按钮弹出确认对话框
- 显示要删除的标题内容
- 如果有子节点，会提示一并删除
- 用户可以取消操作

### 3. 级联删除
- 删除父节点时，所有子节点自动删除
- 保持提纲结构的完整性
- 避免出现孤立的子节点

## 技术实现

### 1. UI组件修改

#### OutlineNode.vue - 节点操作区域
```vue
<div class="node-actions" v-if="editable">
  <el-button 
    size="small" 
    type="text" 
    class="action-btn edit-btn"
    @click.stop="startEdit"
    title="编辑"
  >
    <el-icon><Edit /></el-icon>
  </el-button>
  <el-button 
    size="small" 
    type="text" 
    class="action-btn delete-btn"
    @click.stop="confirmDelete"
    title="删除"
  >
    <el-icon><Delete /></el-icon>
  </el-button>
</div>
```

### 2. 删除确认逻辑

#### 确认对话框
```javascript
confirmDelete() {
  this.$confirm(
    `确定要删除"${this.node.text}"吗？${this.hasChildren ? '删除后其子节点也会一并删除。' : ''}`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      customClass: 'outline-delete-confirm'
    }
  ).then(() => {
    this.deleteNode()
  }).catch(() => {
    // 用户取消删除
  })
}
```

### 3. 删除处理逻辑

#### 递归删除实现
```javascript
deleteNodeById(nodes, nodeId) {
  for (let i = 0; i < nodes.length; i++) {
    if (nodes[i].id === nodeId) {
      // 找到目标节点，删除它
      nodes.splice(i, 1)
      return true
    }
    // 递归查找子节点
    if (nodes[i].children && this.deleteNodeById(nodes[i].children, nodeId)) {
      return true
    }
  }
  return false
}
```

### 4. 事件传递机制

#### 从子组件到父组件
```vue
<!-- OutlineNode.vue 中的子节点 -->
<OutlineNode
  @delete-node="$emit('delete-node', $event)"
/>

<!-- OutlineMessageBox.vue 中的处理 -->
<OutlineNode
  @delete-node="handleDeleteNode"
/>
```

## 样式设计

### 1. 操作按钮样式
```css
.node-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: 8px;
}

.node-content:hover .node-actions {
  opacity: 1;
}

.delete-btn:hover {
  background-color: #fef2f2;
  color: #dc2626;
}
```

### 2. 确认对话框样式
```css
:deep(.outline-delete-confirm .el-message-box__title) {
  color: #dc2626;
  font-weight: 600;
}

:deep(.outline-delete-confirm .el-button--primary) {
  background-color: #dc2626;
  border-color: #dc2626;
}
```

## 使用场景

### 1. 结构调整
- 删除不需要的章节
- 简化提纲结构
- 重新组织内容层次

### 2. 内容优化
- 移除重复的标题
- 删除不相关的内容
- 精简文章大纲

### 3. 个性化定制
- 根据实际需求调整
- 适应不同文档类型
- 满足特定格式要求

## 操作流程

1. **查看提纲**：AI生成提纲后，用户查看结构
2. **识别问题**：发现不需要的标题或章节
3. **悬停显示**：鼠标悬停在目标节点上
4. **点击删除**：点击红色删除按钮
5. **确认操作**：在弹出的确认框中点击"确定"
6. **自动更新**：提纲结构自动更新，Markdown内容同步

## 安全机制

### 1. 确认机制
- 防止误删操作
- 清晰显示删除内容
- 提示级联删除影响

### 2. 可撤销性
- 删除后可以重新编辑添加
- 保持操作的可逆性

### 3. 结构完整性
- 自动处理子节点
- 保持提纲层级关系
- 避免结构破坏

## 扩展性

### 未来可扩展功能
- **批量删除**：选择多个节点批量删除
- **撤销操作**：提供撤销/重做功能
- **拖拽删除**：拖拽到删除区域进行删除
- **快捷键**：支持键盘快捷键删除
- **回收站**：删除的内容可以恢复

### 集成可能性
- 与编辑功能联动
- 与生成功能配合
- 支持模板保存
- 历史版本管理

这个删除功能让用户能够灵活调整提纲结构，为生成高质量的文章内容提供了更好的基础。
