/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      screens: {
        'sm': '640px',    // 小屏幕
        'md': '768px',    // 中等屏幕
        'lg': '1024px',   // 大屏幕
        'xl': '1280px',   // 超大屏幕
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],        // 12px at 16px base
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],    // 14px
        'base': ['1rem', { lineHeight: '1.5rem' }],       // 16px
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],    // 18px
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],     // 20px
        '2xl': ['1.5rem', { lineHeight: '2rem' }],        // 24px
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],   // 30px
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],     // 36px
      },
      colors: {
        // 示例
        'primary': '#596DF4',
        'primary-light': 'rgba(89, 109, 244, 0.14)',
        'primary-middle': 'rgba(89, 109, 244, 0.50)',
        'txt-primary': '#333',
        'txt-dark': '#101010',
        'txt-middle': '#666',
      }
    }
  },
  plugins: [],
}