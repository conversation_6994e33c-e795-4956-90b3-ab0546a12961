# 编辑框布局问题修复

## 问题描述
用户反馈点击编辑按钮时：
1. 宽度会被减小
2. 高度变得很高

## 问题分析

### 原因1：autosize冲突
- `autosize` 属性与手动设置高度冲突
- 导致高度计算不准确

### 原因2：样式继承问题
- Element Plus的textarea有默认样式
- 与原内容区域的样式不一致

### 原因3：布局计算错误
- 直接设置像素高度不够灵活
- 没有考虑行高和内容长度的关系

## 解决方案

### 1. 改用行数控制
```vue
<el-input
  v-model="message.editableContent"
  type="textarea"
  placeholder="请编辑内容..."
  class="edit-textarea"
  :ref="`editTextarea_${index}`"
  :autosize="false"
  :rows="message.calculatedRows || 8"
/>
```

### 2. 智能行数计算
```javascript
// 根据内容高度计算行数
const lineHeight = 24
const contentHeight = element.offsetHeight
calculatedRows = Math.max(Math.ceil(contentHeight / lineHeight), 6)

// 根据文本内容长度调整
const textLines = message.editableContent.split('\n').length
const estimatedLines = Math.ceil(message.editableContent.length / 80)
calculatedRows = Math.max(calculatedRows, textLines, estimatedLines)

// 限制最大行数，避免过高
calculatedRows = Math.min(calculatedRows, 20)
```

### 3. CSS样式优化
```css
/* 确保宽度一致 */
.edit-content-wrapper {
  width: 100%;
  box-sizing: border-box;
}

.edit-textarea {
  width: 100%;
  box-sizing: border-box;
}

/* 移除默认样式，保持与原内容一致 */
.edit-textarea :deep(.el-textarea__inner) {
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  border: none;
  padding: 0;
  resize: vertical;
  box-shadow: none;
  background: transparent;
  width: 100%;
  box-sizing: border-box;
}
```

## 技术改进

### 1. 移除冲突属性
- 移除 `autosize` 属性
- 移除手动高度设置
- 使用 `rows` 属性控制高度

### 2. 优化计算逻辑
- 基于内容高度计算合适的行数
- 考虑文本换行和长度
- 设置合理的最小值和最大值

### 3. 样式重置
- 移除Element Plus的默认边框和内边距
- 确保与原内容区域的字体和行高一致
- 使用透明背景保持视觉连续性

## 预期效果
- ✅ 编辑框宽度与原内容区域完全一致
- ✅ 高度根据内容智能调整，不会过高
- ✅ 视觉上与原内容无缝衔接
- ✅ 保持良好的编辑体验
