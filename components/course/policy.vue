<template>
  <div class="px-4 py-8 mx-auto max-w-[1680px] p-4 sm:px-6 lg:px-8">
    <!-- Title -->
    <div class="flex items-center mb-6 space-x-2">
      <div class="w-1 h-6 bg-blue-600"></div>
      <h2 class="text-xl font-bold">厅长、处长讲政策</h2>
    </div>

    <!-- Top Content -->
    <div class="flex flex-col gap-8 mb-8 lg:flex-row">
      <!-- Live Training Card -->
      <div class="flex-1 p-6 bg-white rounded-lg shadow-sm">
        <div class="flex gap-6">
          <div class="w-2/5">
            <img 
              src="/src/assets/image/image3.png" 
              alt="Training Banner"
              class="w-full rounded-lg"
            />
          </div>
          <div class="flex-1">
            <div class="inline-block px-3 py-1 mb-4 text-sm text-green-800 bg-green-100 rounded-full">
              在线直播
            </div>
            <h3 class="mb-4 text-xl font-bold">
              🔥 全区制造业企业培优育强服务券工作培训会
            </h3>
            <div class="space-y-2 text-gray-600">
              <p>各场主题:</p>
              <p>9:00-10:00 《管理办法》解读</p>
              <p>10:00-11:00 服务券系统实操及疑难点解答</p>
              <div class="mt-4">
                <p>主讲人：秦嘉耘</p>
                <p>自治区工业和信息化厅中小企业发展处处长</p>
              </div>
              <p class="mt-4">直播时间：2024年9月14日 09:00-11:00</p>
            </div>
            <el-button type="warning" class="mt-6">在线直播</el-button>
          </div>
        </div>
      </div>

      <!-- Calendar -->
      <div class="flex flex-col w-96 h-[400px]">
        <el-calendar v-model="currentDate" class="flex-1">
          <template #header="{ date }">
            <div class="flex items-center justify-between">
              <span class="text-lg font-bold">直播日历</span>
              <div class="text-sm text-gray-500">
                <span>{{ formatDate(date) }}</span>
                <span class="ml-4">当前活动共25场</span>
              </div>
            </div>
          </template>
          <template #date-cell="{ data }">
            <div class="relative flex items-center justify-center h-full">
              <p>{{ data.day.split('-')[2] }}</p>
              <div v-if="hasEvent(data.day)" class="absolute w-1 h-1 bg-orange-500 rounded-full bottom-1 right-1"></div>
            </div>
          </template>
        </el-calendar>
      </div>
    </div>

    <!-- Category Filter -->
    <div class="p-4 mb-8 bg-white rounded-lg shadow-sm">
      <div class="mb-2 text-gray-600">服务分类：</div>
      <div class="flex flex-wrap gap-4">
        <el-tag type="info" effect="plain">不限</el-tag>
        <el-tag>厅长、处长讲政策</el-tag>
        <el-tag>政策解读</el-tag>
        <el-tag>企业培训</el-tag>
        <el-tag>技术分享</el-tag>
        <el-tag>项目路演</el-tag>
        <el-tag>其他</el-tag>
      </div>
    </div>

    <!-- Course Grid -->
    <div class="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-5">
      <div 
        v-for="i in 10" 
        :key="i" 
        class="overflow-hidden transition-shadow bg-white rounded-lg shadow-sm cursor-pointer hover:shadow-md"
        @click="navigateToDetail"
      >
        <div class="relative h-40">
          <img 
            src="/src/assets/image/image4.png" 
            alt="Course Image"
            class="object-cover w-full h-full"
          />
        </div>
        <div class="p-4">
          <h4 class="mb-2 text-base font-bold line-clamp-2">企业融资中的法律问题和解决方案</h4>
          <div class="space-y-1 text-xs text-gray-500">
            <p class="truncate">培训讲师：大湾区湾桥总会某市场部...</p>
            <p class="truncate">讲师单位：大湾区湾桥总会某市场部...</p>
            <p>课程类型：企业课程</p>
            <p>发布时间：2024-09-30 10:30</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const currentDate = ref(new Date())

const navigateToDetail = () => {
  console.log('Navigating to policy detail page')
}

const formatDate = (date) => {
  const dateObj = new Date(date)
  return `<${dateObj.getFullYear()}年${dateObj.getMonth() + 1}月>`
}

const hasEvent = (day) => {
  const events = ['2024-10-12', '2024-10-15', '2024-10-20']
  return events.includes(day)
}
</script>

<style scoped>
/* Keep all the calendar-related styles from the original file */
</style> 