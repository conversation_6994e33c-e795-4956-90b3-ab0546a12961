import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/home/<USER>'
import NewDialog from '../views/new/NewDialog.vue'
import HistoryDialog from '../views/history/HistoryDialog.vue'
import workCenter from '../views/center/WorkCenter.vue'
import Agent from '../views/agent/Agent.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/new-dialog/:agentId?/:agentName?',
    name: 'NewDialog',
    component: () => import('@/views/new/NewDialogPro.vue'),
    props: route => ({
      agentId: route.params.agentId || '9bad24bea86d3d7b2a1eff108ed7e7ec',//之前默认的id是1902218668492697602
      agentName: route.params.agentName || '科宝'
    })
  },
  {
    path: '/document-office',
    name: 'DocumentOffice',
    component: () => import('@/views/new/DocumentOffice.vue')
  },
  {
    path: '/official-new-dialog/:agentId?/:agentName?',
    name: 'OfficialNewDialog',
    component: () => import('@/views/new/OfficialNewDialog.vue'),
    props: route => ({
      agentId: route.params.agentId || '799592ca740609d28525d878d539fdc1',
      agentName: route.params.agentName || '公文办公'
    })
  },
  {
    path: '/announcement-new-dialog/:agentId?/:agentName?',
    name: 'AnnouncementNewDialog',
    component: () => import('@/views/new/AnnouncementNewDialog.vue'),
    props: route => ({
      agentId: route.params.agentId || '799592ca740609d28525d878d539fdc1',
      agentName: route.params.agentName || '公文公告'
    })
  },
  {
    path: '/history-dialog',
    name: 'HistoryDialog',
    component: HistoryDialog
  },
  {
    path: '/work-center',
    name: 'WorkCenter',
    component: workCenter
  },
  {
    path: '/agent',
    name: 'Agent',
    component: Agent
  },
  {
    path: '/mobile-dialog',
    name: 'MobileDialog',
    component: () => import('@/views/mobile/MobileDialog.vue')
  },
  // 知识库相关路由
  {
    path: '/personal-database',
    name: 'PersonalDatabase',
    component: () => import('@/views/database/personalDatabase.vue')
  },
  {
    path: '/public-database',
    name: 'PublicDatabase',
    component: () => import('@/views/database/Database.vue')
  },
  // 知识库详情页面
  {
    path: '/database-detail',
    name: 'DatabaseDetail',
    component: () => import('@/views/database/databaseDetail.vue')
  },
  // 文件详情页面
  {
    path: '/file-detail',
    name: 'FileDetail',
    component: () => import('@/views/database/fileDetail.vue')
  },
  // 上传文件步骤页面
  {
    path: '/upload-file-step',
    name: 'UploadFileStep',
    component: () => import('@/views/database/uploadFileStep.vue')
  },
  // 钉钉外部免密登录页面
  {
    path: '/external-bot/:id',
    name: 'ExternalBotFreeLogin', 
    component: () => import('@/views/freelogin/ExternalBotfreelogin.vue'),
    props: route => ({
      id: route.params.id,
      agentId: route.query.agentId || route.params.id,
      loginfree: route.query.loginfree
    })
  },
  // SSO 单点登录页面
  {
    path: '/sso',
    name: 'SsoLogin',
    component: () => import('@/views/sso/index.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router