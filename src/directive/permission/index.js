
/**
 * 操作权限处理
 */
export const hasPermi = {
  mounted(el, binding) {
    const permissions = JSON.parse(localStorage.getItem('userPermit'))?.permissions || [];
    // 「其他角色」按钮权限校验
    const { value } = binding;
    if (value && value instanceof Array && value.length > 0) {
      const hasPermission = permissions.some((permi) => {
        return permi === '*:*:*' || value.includes(permi);
      });
      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el);
        return false;
      }
    } else {
      throw new Error("check perms! Like v-has-permi=\"['system:user:add','system:user:edit']\"");
    }
  }
};

/**
 * 角色权限处理
 */
export const hasRoles= {
  mounted(el, binding) {
    const { value } = binding;
    const roles = JSON.parse(localStorage.getItem('userInfo'))?.roles || [];
    if (value && value instanceof Array && value.length > 0) {
      const hasRole = roles.some((role) => {
        return role === 'superadmin' || role === 'admin' || value.includes(role);
      });
      if (!hasRole) {
        el.parentNode && el.parentNode.removeChild(el);
        return false;
      }
    } else {
      throw new Error("check roles! Like v-has-roles=\"['admin','test']\"");
    }
  }
};
