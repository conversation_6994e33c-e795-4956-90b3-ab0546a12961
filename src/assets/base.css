
/* 响应式根字体大小 */
@media (min-width: 960px) {
  html {
    font-size: 10px;
  }
}

@media (min-width: 1266px) {
  html {
    font-size: 12px;
  }
}

@media (min-width: 1440px) {
  :root{
    font-size: 14px !important;
  }
}

@media screen and (min-width: 1920px) {
  :root{
    font-size: 16px !important;
  }
}

@media screen and (min-width: 2560px) {
  :root {
    font-size: 18px !important;
  }
}

/* 透明按钮样式 */
.btn-transparent {
  cursor: pointer;
  transition: background-color 0.1s ease;
}
.btn-transparent:active {
  color: #596DF4;
  background-color: rgba(89, 109, 244, 0.3);
}

/* 透明按钮样式 */
.btn-primary {
  color: #596DF4;
  background-color: rgba(89, 109, 244, 0.14);
  cursor: pointer;
  transition: background-color 0.1s ease;
}
.btn-primary:active {
  color: #FFF;
  background-color: rgba(89, 109, 244, 0.3);
}