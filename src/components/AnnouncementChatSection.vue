<template>
  <div class="chat-section">
    <div class="chat-wrapper">
      <chat-container
        :agent-id="agentId"
        :agent-name="agentName"
        :user-id="userId"
        :initial-messages="initialMessages"
        :conversation-id="conversationId"
        :workflow-id="workflowId"
        @update:conversation-id="$emit('update:conversation-id', $event)"
        @error="$emit('error', $event)"
        @message-sent="$emit('message-sent', $event)"
        @message-received="$emit('message-received', $event)"
        @node-execution="$emit('node-execution', $event)"
        @process-update="$emit('process-update', $event)"
        ref="chatContainer"
      />
    </div>
  </div>
</template>

<script>
import ChatContainer from '@/components/AnnouncementChatContainer.vue'

export default {
  name: 'AnnouncementChatSection',
  components: {
    ChatContainer
  },
  props: {
    agentId: {
      type: String,
      required: true
    },
    agentName: {
      type: String,
      default: '公文公告'
    },
    userId: {
      type: String,
      required: false
    },
    initialMessages: {
      type: Array,
      default: () => []
    },
    conversationId: {
      type: String,
      default: ''
    },
    workflowId: {
      type: String,
      default: '1947848702138769410' // 公文公告工作流ID
    }
  },
  emits: [
    'update:conversation-id',
    'error',
    'message-sent',
    'message-received',
    'node-execution',
    'process-update'
  ],
  methods: {
    // 暴露 ChatContainer 的方法给父组件使用
    sendMessage(message) {
      return this.$refs.chatContainer?.sendMessage(message)
    },
    
    // 专门用于发送公文公告表单数据的方法
    sendAnnouncementMessageWithFormData(formData) {
      return this.$refs.chatContainer?.sendAnnouncementMessage(formData)
    },
    
    setUploadedFiles(files) {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.uploadedFiles = files
      }
    },
    
    setActiveModes(modes) {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.activeModes = modes
      }
    },
    
    setSelectedKnowledgeList(knowledgeList) {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.selectedKnowledgeList = knowledgeList
      }
    },
    
    setSelectedKnowledgeIds(knowledgeIds) {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.selectedKnowledgeIds = knowledgeIds
      }
    },
    
    setSelectedDocumentIds(documentIds) {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.selectedDocumentIds = documentIds
      }
    }
  }
}
</script>

<style scoped>
.chat-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.chat-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
