<template>
  <el-dialog
    v-model="dialogVisible"
    title=""
    width="80%"
    :before-close="handleClose"
    class="database-selection-dialog"
    :show-close="false"
  >
    <div class="knowledge-browser">
      <!-- 第1行：标题和搜索 -->
      <div class="row-1 header-row">
        <div class="title-section">
          <h2 class="dialog-title">知识库选择</h2>
        </div>
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索知识库..."
            clearable
            @input="handleSearch"
            class="search-input"
            style="width: 300px;"
            :disabled="currentNavigation.level === 'root'"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 第2行：导航和全选 -->
      <div class="row-2 navigation-row">
        <div class="nav-buttons">
          <el-button
            :disabled="navigationHistory.length <= 1"
            @click="goBack"
            size="small"
            :icon="ArrowLeft"
          >
            后退
          </el-button>
          <el-button
            :disabled="forwardHistory.length === 0"
            @click="goForward"
            size="small"
            :icon="ArrowRight"
          >
            前进
          </el-button>
        </div>
        
        <div class="breadcrumb-section">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item 
              v-for="(item, index) in breadcrumbs" 
              :key="index"
              :class="{ 'is-link': index < breadcrumbs.length - 1 }"
              @click="navigateToBreadcrumb(index)"
            >
              {{ item.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="select-all-section">
          <el-checkbox
            v-model="selectAll"
            @change="handleSelectAll"
            :indeterminate="isIndeterminate"
          >
            全选
          </el-checkbox>
        </div>
      </div>

      <!-- 第3-8行：知识库列表 -->
      <div class="row-3-8 content-area" v-loading="loading">
        <div v-if="filteredKnowledgeList.length === 0 && folderList.length === 0" class="empty-state">
          <el-empty description="暂无知识库数据" />
        </div>
        
        <div v-else class="knowledge-grid">
          <!-- 文件夹类型（部门知识库入口） -->
          <div 
            v-for="folder in folderList" 
            :key="folder.id"
            class="knowledge-item folder-item"
            :class="{ 'selected': selectedKnowledgeIds.includes(folder.id) }"
          >
            <div class="item-checkbox">
              <el-checkbox 
                :model-value="selectedKnowledgeIds.includes(folder.id)"
                @change="toggleSelect(folder)"
                @click.stop
              />
            </div>
            <div class="item-icon">
              <el-icon size="32" color="#f4b942"><Folder /></el-icon>
            </div>
            <div class="item-content">
              <div class="item-title">{{ folder.name }}</div>
              <div class="item-meta">{{ folder.description || '部门知识库' }}</div>
            </div>
            <div class="item-actions">
              <el-button 
                type="primary" 
                size="small" 
                @click="enterFolder(folder)"
                :icon="ArrowRight"
              >
                进入
              </el-button>
            </div>
          </div>

          <!-- 知识库文件夹 -->
          <div 
            v-for="knowledge in filteredKnowledgeList.filter(item => item.type === 'knowledge_folder')" 
            :key="knowledge.id"
            class="knowledge-item folder-item"
            :class="{ 'selected': selectedKnowledgeIds.includes(knowledge.id) }"
          >
            <div class="item-checkbox">
              <el-checkbox 
                :model-value="selectedKnowledgeIds.includes(knowledge.id)"
                @change="toggleSelect(knowledge)"
                @click.stop
              />
            </div>
            <div class="item-icon">
              <el-icon size="32" color="#f4b942"><Folder /></el-icon>
            </div>
            <div class="item-content">
              <div class="item-title">{{ knowledge.name }}</div>
              <div class="item-meta">
                <span class="meta-item">
                  <el-icon><Document /></el-icon>
                  {{ knowledge.documentCount || 0 }} 个文档
                </span>
                <span class="meta-item">
                  <el-icon><Clock /></el-icon>
                  {{ formatDate(knowledge.updateTime) }}
                </span>
                <span class="meta-item" v-if="knowledge.description">
                  {{ knowledge.description }}
                </span>
              </div>
            </div>
            <div class="item-actions">
              <el-button 
                type="primary" 
                size="small" 
                @click="enterFolder(knowledge)"
                :icon="ArrowRight"
              >
                进入
              </el-button>
            </div>
          </div>

          <!-- 知识库文件 -->
          <div 
            v-for="knowledge in filteredKnowledgeList.filter(item => item.type === 'file')" 
            :key="knowledge.id"
            class="knowledge-item file-item"
            :class="{ 'selected': selectedKnowledgeIds.includes(knowledge.id) }"
            @click="toggleSelect(knowledge)"
          >
            <div class="item-checkbox">
              <el-checkbox 
                :model-value="selectedKnowledgeIds.includes(knowledge.id)"
                @change="toggleSelect(knowledge)"
                @click.stop
              />
            </div>
            <div class="item-icon">
              <el-icon size="24" color="#409eff"><Document /></el-icon>
            </div>
            <div class="item-content">
              <div class="item-title">{{ knowledge.name }}</div>
              <div class="item-meta">
                <span class="meta-item">
                  <el-icon><Document /></el-icon>
                  {{ knowledge.chunkCount || 0 }} 个分段
                </span>
                <span class="meta-item">
                  <el-icon><Clock /></el-icon>
                  {{ formatDate(knowledge.updateTime) }}
                </span>
                <span class="meta-item" v-if="knowledge.size">
                  {{ formatFileSize(knowledge.size) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-section" v-if="total > pageSize">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 48]"
            layout="sizes, prev, pager, next, total"
            :total="total"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>

      <!-- 第9行：底部操作栏 -->
      <div class="row-9 footer-row">
        <div class="selected-count">
          {{ selectedKnowledgeList.length === 0 ? '未选择任何项目' : `已选择 ${selectedKnowledgeList.length} 个项目` }}
        </div>
        <div class="footer-buttons">
          <el-button @click="handleClose">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirm"
          >
            {{ selectedKnowledgeList.length === 0 ? '确定(清空选择)' : '确定选择' }}
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Document, Clock, ArrowLeft, ArrowRight, Folder } from '@element-plus/icons-vue'
import { getKnowledgeModelList, getKnowledgeFileList, personKnowledge } from '@/api/knowledge'
import { loginUserInfo } from '@/api/auth'
import { useUserStore } from '@/stores/user'

export default {
  name: 'DatabaseSelection',
  components: {
    Search,
    Document,
    Clock,
    ArrowLeft,
    ArrowRight,
    Folder
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    selectedIds: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:modelValue', 'confirm'],
  setup(props, { emit }) {
    const userStore = useUserStore()
    
    // 响应式数据
    const dialogVisible = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      }
    })
    
    const loading = ref(false)
    const searchKeyword = ref('')
    const knowledgeList = ref([]) // 存储知识库文件夹或具体文件
    const selectedKnowledgeIds = ref([...props.selectedIds])
    const currentPage = ref(1)
    const pageSize = ref(12)
    const total = ref(0)
    const selectAll = ref(false)
    const isIndeterminate = ref(false)
    const currentDatasetId = ref('') // 当前选中的知识库ID
    const personalKnowledgeData = ref(null) // 存储个人知识库数据
    
    // 导航相关
    const navigationHistory = ref([{ 
      name: '全部知识库', 
      permission: '', 
      deptId: null,
      datasetId: null,
      level: 'root' // root, knowledge, file
    }])
    const forwardHistory = ref([])
    const currentNavigation = ref({
      name: '全部知识库',
      permission: '',
      deptId: null,
      datasetId: null,
      level: 'root'
    })
    
    // 用户信息
    const userInfo = ref(null)
    
    // 计算属性
    const breadcrumbs = computed(() => {
      return navigationHistory.value
    })
    
    const filteredKnowledgeList = computed(() => {
      let list = knowledgeList.value
      if (searchKeyword.value) {
        list = list.filter(item => 
          item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
          (item.description && item.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
        )
      }
      return list
    })
    
    const selectedKnowledgeList = computed(() => {
      // 合并文件夹列表和知识库列表
      const allItems = [...folderList.value, ...knowledgeList.value]
      return allItems.filter(item => 
        selectedKnowledgeIds.value.includes(item.id) && item.isSelectable
      )
    })
    
    // 文件夹列表（固定显示三个知识库入口）
    const folderList = computed(() => {
      const folders = []
      
      // 如果在根目录，显示三个固定的知识库入口
      if (currentNavigation.value.level === 'root') {
        folders.push({
          id: 'personal_knowledge',
          name: '个人知识库',
          description: '个人创建和管理的知识库',
          type: 'folder',
          permission: 'me',
          isSelectable: true
        })
        
        folders.push({
          id: 'dept_knowledge',
          name: '部门知识库',
          description: '部门团队共享的知识库',
          type: 'folder',
          permission: 'team',
          isSelectable: true
        })
        
        folders.push({
          id: 'public_knowledge',
          name: '公共知识库',
          description: '所有用户可访问的知识库',
          type: 'folder',
          permission: 'public',
          isSelectable: true
        })
      }
      
      return folders
    })
    
    // 方法
    const fetchUserInfo = async () => {
      try {
        const response = await loginUserInfo({
          userId: userStore.userInfo?.userId
        })
        if (response && response.data) {
          userInfo.value = response.data
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    }
    
    const fetchKnowledgeList = async () => {
      try {
        loading.value = true
        
        // 如果在根目录（permission为空），不查询API，只显示文件夹
        if (currentNavigation.value.level === 'root') {
          knowledgeList.value = []
          total.value = 0
          loading.value = false
          return
        }
        
        // 如果在知识库层级，获取知识库列表（显示为文件夹）
        if (currentNavigation.value.level === 'knowledge') {
          // 如果是个人知识库，使用personalKnowledgeData数据
          if (currentNavigation.value.permission === 'me') {
            if (personalKnowledgeData.value && personalKnowledgeData.value.files) {
              // 将个人知识库的文件数据转换为统一格式
              knowledgeList.value = personalKnowledgeData.value.files.map(file => ({
                id: file.id,
                name: file.name,
                type: 'file',
                isSelectable: true,
                size: file.size,
                chunkCount: file.chunkCount,
                updateTime: file.updateTime,
                datasetId: personalKnowledgeData.value.datasetId // 添加所属知识库ID
              }))
              total.value = personalKnowledgeData.value.files.length
              console.log('个人知识库文件列表:', knowledgeList.value)
            } else {
              knowledgeList.value = []
              total.value = 0
              console.log('个人知识库：没有文件数据')
            }
          } else {
            const params = {
              pageNo: currentPage.value.toString(),
              pageSize: pageSize.value.toString()
            }
            
            // 根据当前导航状态设置查询参数
            if (currentNavigation.value.permission) {
              params.permission = currentNavigation.value.permission
            }
            
            if (currentNavigation.value.deptId) {
              params.deptId = currentNavigation.value.deptId
            }
            
            if (searchKeyword.value) {
              params.name = searchKeyword.value
            }
            
            const response = await getKnowledgeModelList(params)
            
            if (response) {
              // 将知识库数据标记为文件夹类型
              knowledgeList.value = (response.rows || []).map(item => ({
                ...item,
                type: 'knowledge_folder',
                isSelectable: true
              }))
              total.value = response.total || 0
            }
          }
        }
        // 如果在文件层级，获取文件列表
        else if (currentNavigation.value.level === 'file' && currentNavigation.value.datasetId) {
          const params = {
            datasetId: currentNavigation.value.datasetId,
            pageNo: currentPage.value.toString(),
            pageSize: pageSize.value.toString()
          }
          
          if (searchKeyword.value) {
            params.keywords = searchKeyword.value
          }
          
          const response = await getKnowledgeFileList(params)
          
          if (response && response.code === 200) {
            // 将文件数据标记为可选择类型，并添加所属知识库ID
            knowledgeList.value = (response.rows || []).map(item => ({
              ...item,
              type: 'file',
              isSelectable: true,
              datasetId: currentNavigation.value.datasetId // 添加所属知识库ID
            }))
            total.value = response.total || 0
          }
        }
      } catch (error) {
        console.error('获取知识库列表失败:', error)
        ElMessage.error('获取知识库列表失败')
      } finally {
        loading.value = false
      }
    }
    
    const toggleSelect = async (knowledge) => {
      // 只有可选择的项目才能被选中
      if (!knowledge.isSelectable) return
      
      const index = selectedKnowledgeIds.value.indexOf(knowledge.id)
      
      if (props.multiple) {
        if (index > -1) {
          selectedKnowledgeIds.value.splice(index, 1)
        } else {
          selectedKnowledgeIds.value.push(knowledge.id)
        }
      } else {
        if (index > -1) {
          selectedKnowledgeIds.value = []
        } else {
          selectedKnowledgeIds.value = [knowledge.id]
        }
      }
      
      await updateSelectAllState()
    }
    
    // 获取所有页面的数据
    const fetchAllPageData = async () => {
      try {
        // 如果在根目录，只返回文件夹列表
        if (currentNavigation.value.level === 'root') {
          return folderList.value.filter(item => item.isSelectable)
        }
        
        // 如果在知识库层级，获取所有知识库
        if (currentNavigation.value.level === 'knowledge') {
          // 如果是个人知识库，使用personalKnowledgeData数据
          if (currentNavigation.value.permission === 'me') {
            if (personalKnowledgeData.value && personalKnowledgeData.value.files) {
              const personalFiles = personalKnowledgeData.value.files.map(file => ({
                id: file.id,
                name: file.name,
                type: 'file',
                isSelectable: true,
                size: file.size,
                chunkCount: file.chunkCount,
                updateTime: file.updateTime,
                datasetId: personalKnowledgeData.value.datasetId
              }))
              return [...folderList.value, ...personalFiles].filter(item => item.isSelectable)
            } else {
              return folderList.value.filter(item => item.isSelectable)
            }
          } else {
            const params = {
              pageNo: '1',
              pageSize: total.value > 0 ? total.value.toString() : '1000' // 使用总数作为页面大小来获取所有数据，如果没有总数则使用较大的数字
            }
            
            if (currentNavigation.value.permission) {
              params.permission = currentNavigation.value.permission
            }
            
            if (currentNavigation.value.deptId) {
              params.deptId = currentNavigation.value.deptId
            }
            
            if (searchKeyword.value) {
              params.name = searchKeyword.value
            }
            
            const response = await getKnowledgeModelList(params)
            
            if (response) {
              const allKnowledgeItems = (response.rows || []).map(item => ({
                ...item,
                type: 'knowledge_folder',
                isSelectable: true
              }))
              
              // 合并文件夹和知识库
              return [...folderList.value, ...allKnowledgeItems].filter(item => item.isSelectable)
            }
          }
        }
        // 如果在文件层级，获取所有文件
        else if (currentNavigation.value.level === 'file' && currentNavigation.value.datasetId) {
          const params = {
            datasetId: currentNavigation.value.datasetId,
            pageNo: '1',
            pageSize: total.value > 0 ? total.value.toString() : '1000' // 使用总数作为页面大小来获取所有数据，如果没有总数则使用较大的数字
          }
          
          if (searchKeyword.value) {
            params.keywords = searchKeyword.value
          }
          
          const response = await getKnowledgeFileList(params)
          
          if (response && response.code === 200) {
            const allFileItems = (response.rows || []).map(item => ({
              ...item,
              type: 'file',
              isSelectable: true,
              datasetId: currentNavigation.value.datasetId // 添加所属知识库ID
            }))
            
            return [...folderList.value, ...allFileItems].filter(item => item.isSelectable)
          }
        }
        
        return []
      } catch (error) {
        console.error('获取所有页面数据失败:', error)
        return []
      }
    }

    const handleSelectAll = async (checked) => {
      try {
        loading.value = true
        
        if (checked) {
          // 获取所有页面的数据
          const allSelectableItems = await fetchAllPageData()
          const allIds = allSelectableItems.map(item => item.id)
          selectedKnowledgeIds.value = [...new Set([...selectedKnowledgeIds.value, ...allIds])]
          
          ElMessage.success(`已选择当前路径下所有 ${allSelectableItems.length} 个项目`)
        } else {
          // 取消选择所有项目
          const allSelectableItems = await fetchAllPageData()
          const currentIds = allSelectableItems.map(item => item.id)
          selectedKnowledgeIds.value = selectedKnowledgeIds.value.filter(id => !currentIds.includes(id))
          
          ElMessage.info('已取消选择当前路径下的所有项目')
        }
        await updateSelectAllState()
      } catch (error) {
        console.error('全选操作失败:', error)
        ElMessage.error('全选操作失败，请重试')
      } finally {
        loading.value = false
      }
    }
    
    const updateSelectAllState = async () => {
      try {
        // 获取所有页面的可选择项目
        const allSelectableItems = await fetchAllPageData()
        const allIds = allSelectableItems.map(item => item.id)
        const selectedCount = allIds.filter(id => selectedKnowledgeIds.value.includes(id)).length
        
        selectAll.value = selectedCount === allIds.length && allIds.length > 0
        isIndeterminate.value = selectedCount > 0 && selectedCount < allIds.length
      } catch (error) {
        // 如果获取失败，回退到当前页面的逻辑
        const currentPageItems = [...folderList.value, ...filteredKnowledgeList.value]
          .filter(item => item.isSelectable)
        const currentPageIds = currentPageItems.map(item => item.id)
        const selectedCount = currentPageIds.filter(id => selectedKnowledgeIds.value.includes(id)).length
        
        selectAll.value = selectedCount === currentPageIds.length && currentPageIds.length > 0
        isIndeterminate.value = selectedCount > 0 && selectedCount < currentPageIds.length
      }
    }
    
    const enterFolder = async (folder) => {
      // 保存前进历史
      forwardHistory.value = []
      
      let newNav = {}
      
      // 如果是根目录的知识库类型文件夹
      if (folder.type === 'folder') {
        // 如果是个人知识库，先调用personKnowledge接口
        if (folder.id === 'personal_knowledge') {
          try {
            loading.value = true
            console.log('调用个人知识库接口...')
            
            // 调用个人知识库接口
            const response = await personKnowledge({
              name: '个人知识库',
              description: '个人创建和管理的知识库'
            })
            
            console.log('个人知识库接口响应:', response)
            
            if (response && response.code === 200) {
              // 保存个人知识库数据
              personalKnowledgeData.value = response.data
              console.log('个人知识库数据:', personalKnowledgeData.value)
              ElMessage.success('个人知识库查询成功')
            } else {
              personalKnowledgeData.value = null
              ElMessage.warning('个人知识库查询失败')
            }
          } catch (error) {
            console.error('调用个人知识库接口失败:', error)
            personalKnowledgeData.value = null
            ElMessage.error('个人知识库查询失败')
          } finally {
            loading.value = false
          }
        }
        
        newNav = {
          name: folder.name,
          permission: folder.permission,
          deptId: null,
          datasetId: null,
          level: 'knowledge'
        }
        
        // 如果是部门知识库，需要传入部门ID
        if (folder.permission === 'team' && userInfo.value && userInfo.value.deptId) {
          newNav.deptId = userInfo.value.deptId
        }
      }
      // 如果是知识库文件夹，进入文件列表
      else if (folder.type === 'knowledge_folder') {
        newNav = {
          name: folder.name,
          permission: currentNavigation.value.permission,
          deptId: currentNavigation.value.deptId,
          datasetId: folder.id,
          level: 'file'
        }
      }
      
      navigationHistory.value.push(newNav)
      currentNavigation.value = newNav
      
      currentPage.value = 1
      fetchKnowledgeList()
    }
    
    const goBack = () => {
      if (navigationHistory.value.length > 1) {
        const current = navigationHistory.value.pop()
        forwardHistory.value.unshift(current)
        currentNavigation.value = navigationHistory.value[navigationHistory.value.length - 1]
        currentPage.value = 1
        fetchKnowledgeList()
      }
    }
    
    const goForward = () => {
      if (forwardHistory.value.length > 0) {
        const next = forwardHistory.value.shift()
        navigationHistory.value.push(next)
        currentNavigation.value = next
        currentPage.value = 1
        fetchKnowledgeList()
      }
    }
    
    const navigateToBreadcrumb = (index) => {
      if (index < navigationHistory.value.length - 1) {
        const targetNav = navigationHistory.value[index]
        navigationHistory.value = navigationHistory.value.slice(0, index + 1)
        forwardHistory.value = []
        currentNavigation.value = targetNav
        currentPage.value = 1
        fetchKnowledgeList()
      }
    }
    
    const handleSearch = () => {
      currentPage.value = 1
      // 只有在非根目录时才进行搜索
      if (currentNavigation.value.level !== 'root') {
        fetchKnowledgeList()
      }
    }
    
    const handlePageChange = (page) => {
      currentPage.value = page
      fetchKnowledgeList()
    }
    
    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
      fetchKnowledgeList()
    }
    
    const handleClose = () => {
      dialogVisible.value = false
      selectedKnowledgeIds.value = [...props.selectedIds]
    }
    
    const handleConfirm = async () => {
      const selectedList = selectedKnowledgeList.value
      
      // 分离知识库ID和文档ID
      const knowledgeIds = []
      const documentIds = []
      
      // 检查是否选择了根目录的知识库类型
      const rootSelections = selectedList.filter(item => 
        item.type === 'folder' && ['personal_knowledge', 'dept_knowledge', 'public_knowledge'].includes(item.id)
      )
      
      // 处理根目录选择
      for (const rootSelection of rootSelections) {
        try {
                      if (rootSelection.id === 'personal_knowledge') {
              // 1. 个人知识库：传入personKnowledge接口返回的data.datasetId
              console.log('personalKnowledgeData', personalKnowledgeData.value)
              
              // 如果没有个人知识库数据，主动调用接口获取
              if (!personalKnowledgeData.value || !personalKnowledgeData.value.datasetId) {
                console.log('个人知识库 - 开始调用personKnowledge接口获取数据')
                try {
                  loading.value = true
                  const response = await personKnowledge({
                    name: '个人知识库',
                    description: '个人创建和管理的知识库'
                  })
                  
                  console.log('personKnowledge接口响应:', response)
                  
                  if (response && response.code === 200 && response.data) {
                    personalKnowledgeData.value = response.data
                    console.log('个人知识库 - 成功获取数据:', personalKnowledgeData.value)
                  } else {
                    console.warn('个人知识库 - 接口返回异常:', response)
                    ElMessage.warning('获取个人知识库数据失败')
                  }
                } catch (error) {
                  console.error('调用personKnowledge接口失败:', error)
                  ElMessage.error('获取个人知识库数据失败')
                } finally {
                  loading.value = false
                }
              }
              
              // 添加个人知识库的datasetId
              if (personalKnowledgeData.value && personalKnowledgeData.value.datasetId) {
                knowledgeIds.push(personalKnowledgeData.value.datasetId)
                console.log('个人知识库 - 添加datasetId:', personalKnowledgeData.value.datasetId)
              } else {
                console.warn('个人知识库 - 最终仍然没有找到datasetId')
                ElMessage.warning('个人知识库数据获取失败，无法添加到选择列表')
              }
          } else if (rootSelection.id === 'dept_knowledge') {
            // 2. 部门知识库：传入所有子知识库的所有ID
            console.log('部门知识库 - 开始获取所有子知识库ID')
            const deptParams = {
              pageNo: '1',
              pageSize: '1000', // 获取所有数据
              permission: 'team'
            }
            
            if (userInfo.value && userInfo.value.deptId) {
              deptParams.deptId = userInfo.value.deptId
            }
            
            const deptResponse = await getKnowledgeModelList(deptParams)
            if (deptResponse && deptResponse.rows) {
              const deptKnowledgeIds = deptResponse.rows.map(item => item.id)
              knowledgeIds.push(...deptKnowledgeIds)
              console.log('部门知识库 - 添加子知识库IDs:', deptKnowledgeIds)
            } else {
              console.warn('部门知识库 - 没有找到子知识库')
            }
          } else if (rootSelection.id === 'public_knowledge') {
            // 3. 公共知识库：传入所有子知识库的所有ID
            console.log('公共知识库 - 开始获取所有子知识库ID')
            const publicParams = {
              pageNo: '1',
              pageSize: '1000', // 获取所有数据
              permission: 'public'
            }
            
            const publicResponse = await getKnowledgeModelList(publicParams)
            if (publicResponse && publicResponse.rows) {
              const publicKnowledgeIds = publicResponse.rows.map(item => item.id)
              knowledgeIds.push(...publicKnowledgeIds)
              console.log('公共知识库 - 添加子知识库IDs:', publicKnowledgeIds)
            } else {
              console.warn('公共知识库 - 没有找到子知识库')
            }
          }
        } catch (error) {
          console.error(`处理${rootSelection.name}时发生错误:`, error)
          ElMessage.error(`获取${rootSelection.name}数据失败`)
        }
      }
      
      // 处理其他正常选择
      selectedList.forEach(item => {
        if (item.type === 'knowledge_folder') {
          // 知识库类型放入knowledgeIds
          knowledgeIds.push(item.id)
        } else if (item.type === 'file') {
          // 文档类型放入documentIds
          documentIds.push(item.id)
          // 同时将该文件所属的知识库ID也放入knowledgeIds
          if (item.datasetId && !knowledgeIds.includes(item.datasetId)) {
            knowledgeIds.push(item.datasetId)
          }
        }
        // folder类型（分类文件夹）已经在上面处理过了
      })
      
      // 去重
      const uniqueKnowledgeIds = [...new Set(knowledgeIds)]
      const uniqueDocumentIds = [...new Set(documentIds)]
      
      console.log('最终知识库IDs:', uniqueKnowledgeIds)
      console.log('最终文档IDs:', uniqueDocumentIds)
      
      emit('confirm', {
        ids: [...selectedKnowledgeIds.value],
        list: selectedList,
        knowledgeIds: uniqueKnowledgeIds,
        documentIds: uniqueDocumentIds
      })
      dialogVisible.value = false
      
      if (selectedList.length === 0) {
        ElMessage.info('已清空所有选择')
      } else {
        const knowledgeCount = uniqueKnowledgeIds.length
        const documentCount = uniqueDocumentIds.length
        let message = `已选择 ${selectedList.length} 个项目`
        if (knowledgeCount > 0 || documentCount > 0) {
          const details = []
          if (knowledgeCount > 0) details.push(`${knowledgeCount} 个知识库`)
          if (documentCount > 0) details.push(`${documentCount} 个文档`)
          message += `（${details.join('、')}）`
        }
        ElMessage.success(message)
      }
    }
    
    const formatDate = (timestamp) => {
      if (!timestamp) return '未知时间'
      
      // 处理字符串格式的时间戳
      const timeValue = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp
      const date = new Date(timeValue)
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '未知时间'
      }
      
      // 格式化为年-月-日
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      
      return `${year}-${month}-${day}`
    }

    const formatFileSize = (size) => {
      if (!size) return '0 B'
      const num = parseInt(size)
      if (num < 1024) return num + ' B'
      if (num < 1024 * 1024) return (num / 1024).toFixed(1) + ' KB'
      if (num < 1024 * 1024 * 1024) return (num / (1024 * 1024)).toFixed(1) + ' MB'
      return (num / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
    }
    
    // 监听弹框打开
    watch(dialogVisible, async (newVal) => {
      if (newVal) {
        await fetchUserInfo()
        fetchKnowledgeList()
        selectedKnowledgeIds.value = [...props.selectedIds]
        await updateSelectAllState()
      }
    })
    
    // 监听外部传入的selectedIds变化
    watch(() => props.selectedIds, async (newIds) => {
      selectedKnowledgeIds.value = [...newIds]
      await updateSelectAllState()
    })
    
    // 监听知识库列表变化，更新全选状态
    watch(filteredKnowledgeList, async () => {
      await updateSelectAllState()
    })
    
    return {
      // 响应式数据
      dialogVisible,
      loading,
      searchKeyword,
      knowledgeList,
      selectedKnowledgeIds,
      currentPage,
      pageSize,
      total,
      selectAll,
      isIndeterminate,
      navigationHistory,
      forwardHistory,
      currentNavigation,
      currentDatasetId,
      personalKnowledgeData,
      userInfo,
      
      // 计算属性
      breadcrumbs,
      filteredKnowledgeList,
      selectedKnowledgeList,
      folderList,
      
      // 方法
      fetchUserInfo,
      fetchKnowledgeList,
      fetchAllPageData,
      toggleSelect,
      handleSelectAll,
      updateSelectAllState,
      enterFolder,
      goBack,
      goForward,
      navigateToBreadcrumb,
      handleSearch,
      handlePageChange,
      handleSizeChange,
      handleClose,
      handleConfirm,
      formatDate,
      formatFileSize
    }
  }
}
</script>

<style scoped>
.database-selection-dialog {
  --el-dialog-padding-primary: 0;
}

.knowledge-browser {
  display: flex;
  flex-direction: column;
  height: 70vh;
  min-height: 600px;
}

/* 第1行：标题和搜索 */
.row-1.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.title-section .dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.search-section {
  display: flex;
  align-items: center;
}

/* 第2行：导航和全选 */
.row-2.navigation-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.nav-buttons {
  display: flex;
  gap: 8px;
}

.breadcrumb-section {
  flex: 1;
  margin: 0 20px;
}

.breadcrumb-section :deep(.el-breadcrumb__item) {
  cursor: pointer;
}

.breadcrumb-section :deep(.el-breadcrumb__item.is-link:hover) {
  color: #409eff;
}

.select-all-section {
  display: flex;
  align-items: center;
}

/* 第3-8行：内容区域 */
.row-3-8.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px 24px;
  overflow: auto;
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
  flex: 1;
}

.knowledge-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #fff;
}

.knowledge-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.knowledge-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.folder-item {
  border-color: #3b82f6;
  background: #DCEFFB;
}

.folder-item:hover {
  border-color: #2563eb;
  background: #bfdbfe;
}

.item-checkbox {
  margin-right: 12px;
}

.item-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* 第9行：底部操作栏 */
.row-9.footer-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.selected-count {
  color: #6b7280;
  font-size: 14px;
}

.footer-buttons {
  display: flex;
  gap: 12px;
}

.item-actions {
  margin-left: 12px;
  flex-shrink: 0;
}
</style>
