<template>
  <div class="office-chat-template">
    <!-- 公文办公区域 -->
    <div class="office-area">
      <el-form :model="formData" class="office-form" label-position="top">
        <!-- 第一行：写作类型 + 标题 -->
        <el-row :gutter="12" class="office-row">
          <el-col :span="5">
            <el-form-item label="写作类型" class="compact-form-item">
              <el-select
                v-model="formData.subjectContent"
                placeholder="请选择"
                size="small"
                class="compact-select"
              >
                <el-option label="公告" value="公告" />
                <el-option label="通知" value="通知" />
                <el-option label="通报" value="通报" />
                <el-option label="演讲稿" value="演讲稿" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="19">
            <el-form-item label="标题" class="compact-form-item">
              <el-input
                v-model="formData.title"
                placeholder="输入标题"
                size="small"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：发文字号 + 发文单位 + 主送机关 -->
        <el-row :gutter="12" class="office-row">
          <el-col :span="8">
            <el-form-item label="发文字号" class="compact-form-item">
              <el-input
                v-model="formData.documentNumber"
                placeholder="输入发文字号"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发文单位" class="compact-form-item">
              <el-input
                v-model="formData.issuingUnit"
                placeholder="输入发文单位"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="主送机关" class="compact-form-item">
              <el-input
                v-model="formData.toPrimary"
                placeholder="输入主送机关"
                size="small"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：成文日期 + 文档上传区域 -->
        <el-row :gutter="12" class="office-row">
          <el-col :span="8">
            <el-form-item label="成文日期" class="compact-form-item">
              <el-date-picker
                v-model="formData.documentDate"
                type="date"
                placeholder="选择日期"
                size="small"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="格式参考文档" class="compact-form-item compact-upload">
                  <div class="file-upload-wrapper">
                    <el-tag
                      v-for="file in formData.referenceTemplate"
                      class="file-tag compact-tag clickable-file-tag"
                      :key="file.fileId"
                      @close="removeTemplateFile(file)"
                      @click.stop="previewFile(file.fileId, file.fileName)"
                      closable
                      size="small"
                      title="点击预览文件"
                    >
                      {{ truncateFileName(file.fileName, 10) }}
                    </el-tag>
                    <el-upload
                      v-if="formData.referenceTemplate.length === 0"
                      action="/api/v1/public/file/upload"
                      :data="uploadData"
                      :on-success="handleTemplateUploadSuccess"
                      :on-error="handleUploadError"
                      :before-upload="beforeUpload"
                      :file-list="formData.referenceTemplate"
                      :limit="1"
                      list-type="text"
                      :show-file-list="false"
                      class="compact-upload-btn"
                    >
                      <el-button size="small" type="primary" text class="upload-text-btn">选择文件</el-button>
                    </el-upload>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="内容依据文档" class="compact-form-item compact-upload">
                  <div class="file-upload-wrapper">
                    <el-tag
                      v-for="file in formData.referenceMaterials"
                      class="file-tag compact-tag reference-file-tag clickable-file-tag"
                      :key="file.fileId"
                      @close="removeReferenceFile(file)"
                      @click.stop="previewFile(file.fileId, file.fileName)"
                      closable
                      size="small"
                      :title="`${file.fileName} - 点击预览文件`"
                    >
                      {{ truncateFileName(file.fileName, 10) }}
                    </el-tag>
                    <el-upload
                      v-if="formData.referenceMaterials.length < 1"
                      action="/api/v1/public/file/upload"
                      :data="uploadData"
                      :on-success="handleReferenceUploadSuccess"
                      :on-error="handleUploadError"
                      :before-upload="beforeUpload"
                      :file-list="[]"
                      :limit="1"
                      :show-file-list="false"
                      class="compact-upload-btn"
                    >
                      <el-button size="small" type="primary" text class="upload-text-btn">选择文件</el-button>
                    </el-upload>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 写作要求输入区域 -->
    <div class="input-area">
      <textarea
        v-model="formData.contentRequirements"
        placeholder="请输入写作要求"
        class="p-2 w-full text-gray-700 align-top outline-none resize-none input-textarea office-textarea"
        @keyup.enter.prevent="handleEnterPress"
        rows="2"
      ></textarea>
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar">
      <div class="flex justify-between items-center">
        <div class="flex gap-2 items-center m-4">
          <div class="px-2 text-xs text-gray-400">内容由 AI 生成，请仔细甄别</div>
        </div>
        <div class="flex gap-2 items-center m-4 text-gray-500 func-container">
          <!-- 发送按钮 -->
          <button
            @click="handleSend"
            class="p-2 text-white rounded-full bg-primary hover:bg-primary-middle"
            :disabled="isLoading || !canSend"
          >
            <svg v-if="!isLoading" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
            <svg v-else class="animate-spin" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 11-6.219-8.56"/></svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
  name: 'OfficeChatTemplate',
  props: {
    userId: {
      type: String,
      required: true
    },
    agentId: {
      type: String,
      required: true
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['send-message'],
  data() {
    return {
      formData: {
        subjectContent: '',
        title: '',
        documentNumber: '',
        issuingUnit: '',
        toPrimary: '',
        documentDate: '',
        referenceTemplate: [],
        referenceMaterials: [],
        contentRequirements: ''
      },
      hasAutoFilled: false // 防止重复自动填入
    }
  },
  mounted() {
    // 组件加载完成后自动填入预设值
    this.$nextTick(() => {
      // 延迟执行，确保组件完全加载
      setTimeout(() => {
        this.autoFillPresetValues()
      }, 500)
    })
  },
  computed: {
    uploadData() {
      return {
        userId: this.userId,
        agentId: this.agentId
      }
    },
    canSend() {
      return this.formData.title && this.formData.contentRequirements
    }
  },
  methods: {
    /**
     * 自动填入预设值（仅在首次加载时执行）
     */
    async autoFillPresetValues() {
      // 检查是否已经有数据，避免覆盖用户输入
      if (this.hasAutoFilled ||
          this.formData.title ||
          this.formData.contentRequirements ||
          this.formData.referenceMaterials.length > 0) {
        console.log('跳过自动填入：已有数据或已执行过自动填入')
        return
      }

      this.hasAutoFilled = true
      await this.fillPresetValues()
    },

    /**
     * 填入预设值
     */
    async fillPresetValues() {
      try {
        // 显示加载提示
        const loadingMessage = ElMessage({
          message: '正在加载预设模板和参考文件...',
          type: 'info',
          duration: 0,
          showClose: false
        })

        // 设置预设表单值
        this.formData = {
          ...this.formData,
          title: '关于下达2025年第一批以工代赈中央预算内投资计划的通知',
          subjectContent: '通知',
          issuingUnit: '广西壮族自治区发展和改革委员会',
          toPrimary: '各市发展改革委，各有关县（市、区）发展改革委（局）',
          documentDate: '2024-12-20',
          documentNumber: '桂发改以工代赈〔2024〕1234号',
          contentRequirements: '请根据《广西壮族自治区发展和改革委员会关于下达2025年第一批以工代赈中央预算内投资计划的通知》文件精神，结合本地区实际情况，起草一份具体的实施通知。\n\n要求：\n1. 明确项目建设要求和标准\n2. 强调以工代赈政策执行要点\n3. 明确资金使用和监管要求\n4. 提出具体工作安排和时间节点'
        }

        // 自动上传参考文件
        const fileName = '广西壮族自治区发展和改革委员会关于下达2025年第一批以工代赈中央预算内投资计划的通知.docx'
        try {
          const fileId = await this.uploadFileFromPublic(fileName)

          if (fileId) {
            const { type, extension } = this.getFileType(fileName)

            const fileInfo = {
              fileId: fileId,
              fileType: type,
              extension: extension,
              fileName: fileName
            }

            // 添加参考文件到表单数据
            this.formData.referenceMaterials = [fileInfo]

            // 关闭加载提示
            loadingMessage.close()

            ElMessage.success('模板加载完成，参考文件已准备就绪')
          } else {
            // 关闭加载提示
            loadingMessage.close()
            ElMessage.warning('模板加载完成，但参考文件上传失败')
          }
        } catch (uploadError) {
          console.error('上传参考文件失败:', uploadError)
          // 关闭加载提示
          loadingMessage.close()
          ElMessage.warning('模板加载完成，但参考文件上传失败: ' + uploadError.message)
        }

      } catch (error) {
        console.error('模板加载失败:', error)
        ElMessage.error('模板加载失败: ' + error.message)
      }
    },

    /**
     * 从public目录上传文件
     */
    async uploadFileFromPublic(fileName) {
      try {
        // 先获取文件内容
        const response = await fetch(`/${fileName}`)
        if (!response.ok) {
          throw new Error(`无法获取文件: ${fileName}`)
        }

        const blob = await response.blob()

        // 创建FormData
        const formData = new FormData()
        formData.append('file', blob, fileName)
        formData.append('userId', this.userId)
        formData.append('agentId', this.agentId)

        // 调用上传接口
        const uploadResponse = await fetch('/api/v1/public/file/upload', {
          method: 'POST',
          body: formData
        })

        const uploadResult = await uploadResponse.json()

        if (uploadResult.code === 200) {
          console.log('文件上传成功:', uploadResult)
          return uploadResult.data.fileId
        } else {
          throw new Error(uploadResult.msg || '文件上传失败')
        }
      } catch (error) {
        console.error('上传文件失败:', error)
        ElMessage.error(`上传文件失败: ${error.message}`)
        return null
      }
    },

    handleSend() {
      if (!this.canSend) {
        ElMessage.warning('请填写标题和写作要求')
        return
      }

      // 只传递表单数据，让父组件来处理
      this.$emit('send-message', { ...this.formData })
    },

    handleEnterPress(event) {
      if (event.shiftKey) {
        return
      }
      this.handleSend()
    },

    handleTemplateUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = this.getFileType(file.name)
        const fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name
        }
        this.formData.referenceTemplate.push(fileInfo)
        ElMessage.success('格式参考文档上传成功')
      } else {
        ElMessage.error(response.msg || '文件上传失败')
      }
    },

    handleReferenceUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = this.getFileType(file.name)
        const fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name
        }
        this.formData.referenceMaterials.push(fileInfo)
        ElMessage.success('内容依据文档上传成功')
      } else {
        ElMessage.error(response.msg || '文件上传失败')
      }
    },

    handleUploadError(error) {
      console.error('文件上传失败:', error)
      ElMessage.error('文件上传失败，请重试')
    },

    beforeUpload(file) {
      const isValidType = this.isValidFileType(file.type, file.name)
      const isValidSize = file.size / 1024 / 1024 < 50 // 50MB

      if (!isValidType) {
        ElMessage.error('不支持的文件格式')
        return false
      }

      if (!isValidSize) {
        ElMessage.error('文件大小不能超过 50MB')
        return false
      }

      return true
    },

    removeTemplateFile(file) {
      const index = this.formData.referenceTemplate.findIndex(f => f.fileId === file.fileId)
      if (index > -1) {
        this.formData.referenceTemplate.splice(index, 1)
      }
    },

    removeReferenceFile(file) {
      const index = this.formData.referenceMaterials.findIndex(f => f.fileId === file.fileId)
      if (index > -1) {
        this.formData.referenceMaterials.splice(index, 1)
      }
    },

    previewFile(fileId, fileName) {
      // TODO: 实现文件预览功能
      console.log('预览文件:', fileId, fileName)
      ElMessage.info('文件预览功能开发中...')
    },

    truncateFileName(fileName, maxLength = 12) {
      if (!fileName) return ''

      // 如果文件名长度小于等于最大长度，直接返回
      if (fileName.length <= maxLength) {
        return fileName
      }

      // 获取文件扩展名
      const dotIndex = fileName.lastIndexOf('.')
      const extension = dotIndex !== -1 ? fileName.substring(dotIndex) : ''
      const nameWithoutExt = dotIndex !== -1 ? fileName.substring(0, dotIndex) : fileName

      // 计算可用于显示文件名的长度（预留扩展名和省略号的空间）
      const availableLength = maxLength - extension.length - 3 // 3 是省略号的长度

      if (availableLength <= 0) {
        // 如果可用长度不够，只显示省略号和扩展名
        return '...' + extension
      }

      // 截断文件名并添加省略号
      return nameWithoutExt.substring(0, availableLength) + '...' + extension
    },

    /**
     * 获取文件类型
     */
    getFileType(filename) {
      const extension = filename.split('.').pop().toUpperCase()

      const typeMap = {
        DOCUMENT: ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
        IMAGE: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
        AUDIO: ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
        VIDEO: ['MP4', 'MOV', 'MPEG', 'MPGA']
      }

      for (const [type, extensions] of Object.entries(typeMap)) {
        if (extensions.includes(extension)) {
          return {
            type,
            extension
          }
        }
      }

      return {
        type: 'CUSTOM',
        extension
      }
    },

    isValidFileType(fileType, fileName) {
      const validTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
        'application/pdf', 'text/plain',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/csv'
      ]

      const extension = fileName.split('.').pop().toLowerCase()
      const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'txt', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'csv']

      return validTypes.includes(fileType) || validExtensions.includes(extension)
    }
  }
}
</script>

<style scoped>
.office-chat-template {
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.office-area {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.office-form {
  min-height: 180px;
  padding: 12px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.office-row {
  margin-bottom: 8px;
}

.compact-form-item {
  margin-bottom: 8px;
}

.compact-form-item :deep(.el-form-item__label) {
  font-size: 12px;
  color: #374151;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 4px;
  padding-bottom: 0;
}

.compact-select {
  width: 100%;
}

.file-upload-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  min-height: 28px;
}

.file-tag {
  max-width: 100%;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  margin-bottom: 4px;
  font-size: 11px;
}

.clickable-file-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-file-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.compact-tag {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
  padding: 0 6px;
  border-radius: 3px;
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.reference-file-tag {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.compact-upload-btn {
  display: inline-block;
}

.upload-text-btn {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
  padding: 0 8px;
  border-radius: 3px;
}

.input-area {
  padding: 12px 16px;
}

.office-textarea {
  min-height: 50px !important;
  height: 50px;
  font-size: 13px !important;
  line-height: 1.4;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  transition: all 0.3s ease;
  padding: 6px 8px !important;
}

.office-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.office-textarea::placeholder {
  color: #9ca3af;
  font-size: 13px;
}

.bottom-toolbar {
  padding: 0px 16px;
  border-top: 1px solid #e5e7eb;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
}

.func-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bg-primary {
  background-color: #3b82f6;
}

.hover\:bg-primary-middle:hover {
  background-color: #2563eb;
}

/* Element Plus 样式覆盖 */
:deep(.office-form .el-form-item) {
  margin-bottom: 8px;
}

:deep(.office-form .el-input) {
  font-size: 13px;
}

:deep(.office-form .el-input__inner) {
  font-size: 13px;
  border-radius: 4px;
  border-color: #d1d5db;
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
  transition: all 0.3s ease;
}

:deep(.office-form .el-input__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  background: #fefefe;
  transform: translateY(-1px);
}

:deep(.office-form .el-input__inner:hover) {
  border-color: #93c5fd;
  background: #fefefe;
}

:deep(.office-form .el-select) {
  width: 100%;
}

:deep(.office-form .el-select .el-input__inner) {
  cursor: pointer;
}

:deep(.office-form .el-date-editor) {
  width: 100%;
}

:deep(.office-form .el-date-editor .el-input__inner) {
  cursor: pointer;
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

:deep(.office-form .el-date-editor--date) {
  width: 100%;
}

:deep(.office-form .el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

:deep(.office-form .el-upload) {
  width: 100%;
  border: none;
}

:deep(.office-form .el-upload .el-upload-dragger) {
  width: 100%;
  height: auto;
  border: 1px dashed #d1d5db;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

:deep(.office-form .el-upload .el-upload-dragger:hover) {
  border-color: #3b82f6;
  background: #f8fafc;
}

:deep(.el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  max-width: 120px;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  :deep(.office-form .el-col-5) {
    flex: 0 0 25%;
    max-width: 25%;
  }

  :deep(.office-form .el-col-19) {
    flex: 0 0 75%;
    max-width: 75%;
  }

  :deep(.office-form .el-col-12) {
    flex: 0 0 50%;
    max-width: 50%;
  }

  :deep(.office-form .el-col-8) {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
}

@media (max-width: 992px) {
  .office-form {
    padding: 10px !important;
    min-height: 170px;
  }

  :deep(.office-form .el-col-5) {
    flex: 0 0 100%;
    max-width: 100%;
  }

  :deep(.office-form .el-col-19) {
    flex: 0 0 100%;
    max-width: 100%;
  }

  :deep(.office-form .el-col-12) {
    flex: 0 0 50%;
    max-width: 50%;
  }

  :deep(.office-form .el-col-8) {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .compact-form-item :deep(.el-form-item__label) {
    font-size: 11px !important;
  }

  :deep(.office-form .el-input__inner) {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .office-form {
    padding: 8px !important;
    min-height: 160px;
  }

  .office-row {
    margin-bottom: 6px !important;
  }

  .compact-form-item {
    margin-bottom: 6px !important;
  }

  :deep(.office-form .el-col-5),
  :deep(.office-form .el-col-19),
  :deep(.office-form .el-col-12),
  :deep(.office-form .el-col-8) {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 4px;
  }

  .office-textarea {
    min-height: 40px !important;
    height: 40px;
    font-size: 12px !important;
  }

  .compact-form-item :deep(.el-form-item__label) {
    font-size: 11px !important;
  }

  :deep(.office-form .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    padding: 0 6px;
  }

  :deep(.office-form .el-date-editor .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
  }
}
</style>