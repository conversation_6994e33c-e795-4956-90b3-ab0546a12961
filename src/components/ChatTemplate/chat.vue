<template>
  <div class="chat-template">
    <!-- 文件预览区域 -->
    <div class="file-preview-container" v-if="uploadedFiles.length > 0">
      <div class="flex flex-wrap gap-2 mb-3 uploaded-files-preview">
        <div
          v-for="(file, index) in uploadedFiles"
          :key="file.fileId"
          class="flex items-center p-2 bg-gray-50 rounded-lg file-preview-item"
        >
          <span class="mr-2">{{ getFileIcon(file.fileType) }}</span>
          <span class="text-sm text-gray-600 cursor-pointer hover:text-primary" @click="previewFile(file)">
            {{ file.fileName }}
          </span>
          <button
            class="ml-2 text-gray-400 hover:text-red-500"
            @click="removeFile(index)"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 输入框区域 -->
    <div class="input-area">
      <textarea
        v-model="userInput"
        :placeholder="placeholder"
        class="p-2 w-full text-gray-700 align-top outline-none resize-none input-textarea"
        @keyup.enter.prevent="handleEnterPress"
        rows="3"
      ></textarea>
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar">
      <div class="flex justify-between items-center">
        <div class="flex gap-2 items-center m-4">
          <!-- 深度思考按钮 -->
          <button
            @click="toggleMode('deep')"
            class="p-1 rounded transition-colors duration-200 mode-button"
            :class="[
              activeModes.includes('deep')
                ? 'bg-blue-100 text-blue-600'
                : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
            ]"
            title="深度思考"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain">
              <path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"/>
              <path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"/>
              <path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"/>
              <path d="M17.599 6.5a3 3 0 0 0 .399-1.375"/>
              <path d="M6.003 5.125A3 3 0 0 0 6.401 6.5"/>
              <path d="M3.477 10.896a4 4 0 0 1 .585-.396"/>
              <path d="M19.938 10.5a4 4 0 0 1 .585.396"/>
              <path d="M6 18a4 4 0 0 1-1.967-.516"/>
              <path d="M19.967 17.484A4 4 0 0 1 18 18"/>
            </svg>
          </button>

          <!-- 联网搜索按钮 -->
          <button
            v-if="showSearchMode"
            @click="toggleMode('search')"
            class="p-1 rounded transition-colors duration-200 mode-button"
            :class="[
              activeModes.includes('search')
                ? 'bg-green-100 text-green-600'
                : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
            ]"
            title="联网搜索"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search">
              <circle cx="11" cy="11" r="8"/>
              <path d="m21 21-4.35-4.35"/>
            </svg>
          </button>

          <!-- 知识库选择按钮 -->
          <button
            v-if="showKnowledgeSelectionButton"
            @click="showKnowledgeSelection"
            class="p-1 rounded transition-colors duration-200 mode-button"
            :class="[
              selectedKnowledgeList.length > 0
                ? 'bg-purple-100 text-purple-600'
                : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
            ]"
            :title="selectedKnowledgeList.length > 0 ? `已选择 ${selectedKnowledgeList.length} 个知识库` : '选择知识库'"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database">
              <ellipse cx="12" cy="5" rx="9" ry="3"/>
              <path d="M3 5v14c0 1.66 4.03 3 9 3s9-1.34 9-3V5"/>
              <path d="M3 12c0 1.66 4.03 3 9 3s9-1.34 9-3"/>
            </svg>
            <span v-if="selectedKnowledgeList.length > 0" class="ml-1 text-xs">{{ selectedKnowledgeList.length }}</span>
          </button>

          <div class="px-2 text-xs text-gray-400">内容由 AI 生成，请仔细甄别</div>
        </div>

        <div class="flex gap-2 items-center m-4 text-gray-500 func-container">
          <!-- 文件上传按钮 -->
          <div v-if="showFileUpload" class="file-upload-container">
            <input
              ref="fileInput"
              type="file"
              multiple
              accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.pdf,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.csv"
              @change="handleFileSelect"
              class="hidden"
            />
            <button
              @click="$refs.fileInput.click()"
              class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors duration-200"
              title="上传文件"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-paperclip">
                <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
              </svg>
            </button>
          </div>

          <!-- 发送按钮 -->
          <button
            @click="handleSend"
            class="p-2 text-white rounded-full bg-primary hover:bg-primary-middle transition-colors duration-200"
            :disabled="isLoading || !canSend"
          >
            <svg v-if="!isLoading" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send">
              <path d="m22 2-7 20-4-9-9-4Z"/>
              <path d="M22 2 11 13"/>
            </svg>
            <svg v-else class="animate-spin" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 12a9 9 0 11-6.219-8.56"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
  name: 'ChatTemplate',
  props: {
    placeholder: {
      type: String,
      default: '你可以问我任何问题，或说出我如何帮助你...'
    },
    showSearchMode: {
      type: Boolean,
      default: true
    },
    showKnowledgeSelectionButton: {
      type: Boolean,
      default: true
    },
    showFileUpload: {
      type: Boolean,
      default: true
    },
    selectedKnowledgeList: {
      type: Array,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      required: true
    },
    agentId: {
      type: String,
      required: true
    }
  },
  emits: ['send-message', 'show-knowledge-selection'],
  data() {
    return {
      userInput: '',
      uploadedFiles: [],
      activeModes: []
    }
  },
  computed: {
    canSend() {
      return (this.userInput.trim() || this.uploadedFiles.length > 0) && !this.isLoading
    }
  },
  methods: {
    handleSend() {
      if (!this.canSend) {
        return
      }

      // 构建发送的消息数据
      const messageData = {
        type: 'normal',
        content: this.userInput.trim(),
        files: [...this.uploadedFiles],
        modes: [...this.activeModes],
        knowledgeList: [...this.selectedKnowledgeList]
      }

      this.$emit('send-message', messageData)

      // 清空输入
      this.userInput = ''
      this.uploadedFiles = []
    },

    handleEnterPress(event) {
      if (event.shiftKey) {
        return
      }
      this.handleSend()
    },

    toggleMode(mode) {
      const index = this.activeModes.indexOf(mode)
      if (index > -1) {
        this.activeModes.splice(index, 1)
      } else {
        this.activeModes.push(mode)
      }
    },

    showKnowledgeSelection() {
      this.$emit('show-knowledge-selection')
    },

    handleFileSelect(event) {
      const files = Array.from(event.target.files)

      files.forEach(file => {
        if (this.beforeUpload(file)) {
          this.uploadFile(file)
        }
      })

      // 清空文件输入
      event.target.value = ''
    },

    async uploadFile(file) {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('userId', this.userId)
      formData.append('agentId', this.agentId)

      try {
        const response = await fetch('/api/v1/public/file/upload', {
          method: 'POST',
          body: formData
        })

        const result = await response.json()

        if (result.code === 200) {
          const { type, extension } = this.getFileType(file.name)
          const fileInfo = {
            fileId: result.data.fileId,
            fileType: type,
            extension: extension,
            fileName: file.name
          }
          this.uploadedFiles.push(fileInfo)
          ElMessage.success(`文件 ${file.name} 上传成功`)
        } else {
          ElMessage.error(result.msg || '文件上传失败')
        }
      } catch (error) {
        console.error('文件上传失败:', error)
        ElMessage.error('文件上传失败，请重试')
      }
    },

    beforeUpload(file) {
      const isValidType = this.isValidFileType(file.type, file.name)
      const isValidSize = file.size / 1024 / 1024 < 50 // 50MB

      if (!isValidType) {
        ElMessage.error('不支持的文件格式')
        return false
      }

      if (!isValidSize) {
        ElMessage.error('文件大小不能超过 50MB')
        return false
      }

      return true
    },

    removeFile(index) {
      this.uploadedFiles.splice(index, 1)
    },

    previewFile(file) {
      // TODO: 实现文件预览功能
      console.log('预览文件:', file)
      ElMessage.info('文件预览功能开发中...')
    },

    getFileIcon(fileType) {
      const icons = {
        'IMAGE': '🖼️',
        'DOCUMENT': '📄',
        'EXCEL': '📊',
        'PPT': '📽️',
        'PDF': '📕',
        'OTHER': '📎'
      }
      return icons[fileType] || '📎'
    },

    getFileType(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
      const documentTypes = ['doc', 'docx', 'pdf', 'txt', 'rtf']
      const excelTypes = ['xls', 'xlsx', 'csv']
      const pptTypes = ['ppt', 'pptx']

      if (imageTypes.includes(extension)) {
        return { type: 'IMAGE', extension }
      } else if (documentTypes.includes(extension)) {
        return { type: 'DOCUMENT', extension }
      } else if (excelTypes.includes(extension)) {
        return { type: 'EXCEL', extension }
      } else if (pptTypes.includes(extension)) {
        return { type: 'PPT', extension }
      } else {
        return { type: 'OTHER', extension }
      }
    },

    isValidFileType(fileType, fileName) {
      const validTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
        'application/pdf', 'text/plain',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/csv'
      ]

      const extension = fileName.split('.').pop().toLowerCase()
      const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'txt', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'csv']

      return validTypes.includes(fileType) || validExtensions.includes(extension)
    }
  }
}
</script>

<style scoped>
.chat-template {
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-preview-container {
  padding: 12px 16px 0;
  border-bottom: 1px solid #e5e7eb;
}

.uploaded-files-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-preview-item {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  background: #f3f4f6;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.file-preview-item:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.input-area {
  padding: 12px 16px;
}

.input-textarea {
  width: 100%;
  min-height: 80px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.3s ease;
  resize: none;
}

.input-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.input-textarea::placeholder {
  color: #9ca3af;
}

.bottom-toolbar {
  padding: 8px 16px;
  border-top: 1px solid #e5e7eb;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
}

.mode-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.mode-button:hover {
  transform: translateY(-1px);
}

.func-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-upload-container {
  position: relative;
}

.hidden {
  display: none;
}

.bg-primary {
  background-color: #3b82f6;
}

.hover\:bg-primary-middle:hover {
  background-color: #2563eb;
}

.text-primary {
  color: #3b82f6;
}

.hover\:text-primary:hover {
  color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-template {
    border-radius: 6px;
  }

  .file-preview-container {
    padding: 8px 12px 0;
  }

  .input-area {
    padding: 8px 12px;
  }

  .input-textarea {
    min-height: 60px;
    font-size: 13px;
    padding: 6px 8px;
  }

  .bottom-toolbar {
    padding: 6px 12px;
  }

  .mode-button {
    width: 28px;
    height: 28px;
  }

  .mode-button svg {
    width: 14px;
    height: 14px;
  }

  .func-container button {
    padding: 6px;
  }

  .func-container button svg {
    width: 16px;
    height: 16px;
  }
}

/* 动画效果 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 按钮禁用状态 */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled:hover {
  transform: none;
}
</style>