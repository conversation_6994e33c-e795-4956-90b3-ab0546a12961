<template>
  <div class="official-announcement-template">
    <!-- 公文公告区域 -->
    <div class="announcement-area">
      <div class="announcement-form">
        <!-- 句子填空式表单 -->
        <div class="sentence-form">
          <span class="form-text">根据以下信息，写一篇</span>
          <div class="inline-form-item">
            <el-select
              v-model="formData.subjectContent"
              placeholder="请选择"
              size="small"
              class="inline-select"
            >
              <el-option label="通知" value="通知" />
              <el-option label="公告" value="公告" />
              <el-option label="通报" value="通报" />
              <el-option label="演讲稿" value="演讲稿" />
            </el-select>
          </div>
          <span class="form-text">，格式参考文档</span>
          <div class="inline-form-item file-item">
            <div class="file-upload-wrapper">
              <el-tag
                v-for="file in formData.referenceTemplate"
                class="file-tag compact-tag clickable-file-tag"
                :key="file.fileId"
                @close="removeTemplateFile(file)"
                @click.stop="previewFile(file.fileId, file.fileName)"
                closable
                size="small"
                :title="`${file.fileName} - 点击预览文件`"
              >
                {{ truncateFileName(file.fileName, 12) }}
              </el-tag>
              <el-upload
                v-if="formData.referenceTemplate.length < 3"
                action="/api/v1/public/file/upload"
                :data="uploadData"
                :on-success="handleTemplateUploadSuccess"
                :on-error="handleUploadError"
                :before-upload="beforeUpload"
                :file-list="formData.referenceTemplate"
                :limit="3"
                list-type="text"
                :show-file-list="false"
                class="compact-upload-btn"
              >
                <el-button size="small" type="primary" text class="upload-text-btn">选择文件</el-button>
              </el-upload>
            </div>
          </div>
          <span class="form-text">，标题</span>
          <div class="inline-form-item">
            <el-input
              v-model="formData.title"
              placeholder="输入公文公告标题"
              size="small"
              class="inline-input"
            />
          </div>
          <span class="form-text">，发文单位</span>
          <div class="inline-form-item">
            <el-input
              v-model="formData.issuingUnit"
              placeholder="输入发文单位"
              size="small"
              class="inline-input"
            />
          </div>
          <span class="form-text">，主送机关</span>
          <div class="inline-form-item">
            <el-input
              v-model="formData.toPrimary"
              placeholder="输入主送机关"
              size="small"
              class="inline-input"
            />
          </div>
          <span class="form-text">，发文字号</span>
          <div class="inline-form-item">
            <el-input
              v-model="formData.documentNumber"
              placeholder="输入发文字号"
              size="small"
              class="inline-input"
            />
          </div>
          <span class="form-text">，成文日期</span>
          <div class="inline-form-item">
            <el-date-picker
              v-model="formData.createDate"
              type="date"
              placeholder="选择成文日期"
              size="small"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="inline-date"
            />
          </div>
          <span class="form-text">，发布日期</span>
          <div class="inline-form-item">
            <el-date-picker
              v-model="formData.releaseDate"
              type="date"
              placeholder="选择发布日期"
              size="small"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="inline-date"
            />
          </div>
          <span class="form-text">，内容依据文档</span>
          <div class="inline-form-item file-item">
            <div class="file-upload-wrapper">
              <el-tag
                v-for="file in formData.referenceMaterials"
                class="file-tag compact-tag reference-file-tag clickable-file-tag"
                :key="file.fileId"
                @close="removeReferenceFile(file)"
                @click.stop="previewFile(file.fileId, file.fileName)"
                closable
                size="small"
                :title="`${file.fileName} - 点击预览文件`"
              >
                {{ truncateFileName(file.fileName, 12) }}
              </el-tag>
              <el-upload
                v-if="formData.referenceMaterials.length < 3"
                action="/api/v1/public/file/upload"
                :data="uploadData"
                :on-success="handleReferenceUploadSuccess"
                :on-error="handleUploadError"
                :before-upload="beforeUpload"
                :file-list="[]"
                :limit="3"
                :show-file-list="false"
                class="compact-upload-btn"
              >
                <el-button size="small" type="primary" text class="upload-text-btn">选择文件</el-button>
              </el-upload>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 内容要求输入区域 -->
    <div class="input-area">
      <textarea
        v-model="formData.contentRequirements"
        placeholder="请输入内容要求"
        class="p-2 w-full text-gray-700 align-top outline-none resize-none input-textarea announcement-textarea"
        @keyup.enter.prevent="handleEnterPress"
        rows="2"
      ></textarea>
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar">
      <div class="flex justify-between items-center">
        <div class="flex gap-2 items-center m-4">
          <div class="px-2 text-xs text-gray-400">内容由 AI 生成，请仔细甄别</div>
        </div>
        <div class="flex gap-2 items-center m-4 text-gray-500 func-container">
          <!-- 发送按钮 -->
          <button
            @click="handleSend"
            class="p-2 text-white rounded-full bg-primary hover:bg-primary-middle"
            :disabled="isLoading || !canSend"
          >
            <svg v-if="!isLoading" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
            <svg v-else class="animate-spin" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 11-6.219-8.56"/></svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
  name: 'OfficialAnnouncementTemplate',
  props: {
    userId: {
      type: String,
      required: true
    },
    agentId: {
      type: String,
      required: true
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['send-message'],
  data() {
    return {
      formData: {
        title: '',
        subjectContent: '',
        issuingUnit: '',
        toPrimary: '',
        documentNumber: '',
        releaseDate: '',
        createDate: '',
        contentRequirements: '',
        referenceMaterials: [],
        referenceTemplate: []
      },
      hasAutoFilled: false // 防止重复自动填入
    }
  },
  mounted() {
    // 组件加载完成后自动填入预设值
    this.$nextTick(() => {
      // 延迟执行，确保组件完全加载
      setTimeout(() => {
        this.autoFillPresetValues()
      }, 500)
    })
  },
  computed: {
    uploadData() {
      return {
        userId: this.userId,
        agentId: this.agentId
      }
    },
    canSend() {
      return this.formData.title && this.formData.contentRequirements
    }
  },
  methods: {
    /**
     * 自动填入预设值（仅在首次加载时执行）
     */
    async autoFillPresetValues() {
      // 检查是否已经有数据，避免覆盖用户输入
      if (this.hasAutoFilled ||
          this.formData.title ||
          this.formData.contentRequirements ||
          this.formData.referenceMaterials.length > 0) {
        console.log('跳过自动填入：已有数据或已执行过自动填入')
        return
      }

      this.hasAutoFilled = true
      await this.fillPresetValues()
    },

    /**
     * 填入预设值
     */
    async fillPresetValues() {
      try {
        // 显示加载提示
        const loadingMessage = ElMessage({
          message: '正在加载预设模板...',
          type: 'info',
          duration: 0,
          showClose: false
        })

        // 设置预设表单值
        this.formData = {
          ...this.formData,
          title: '关于加强政务公开工作的公告',
          subjectContent: '公告',
          issuingUnit: '广西壮族自治区人民政府办公厅',
          toPrimary: '各市、县人民政府，自治区人民政府各组成部门、各直属机构',
          documentNumber: '桂政办公告〔2024〕001号',
          releaseDate: '2024-12-30',
          createDate: '2024-12-28',
          contentRequirements: '请根据政务公开相关要求，起草一份关于加强政务公开工作的公告。\n\n要求：\n1. 明确政务公开的重要意义\n2. 详细说明公开的内容和范围\n3. 规范公开的方式和程序\n4. 强调监督和保障措施'
        }

        // 关闭加载提示
        loadingMessage.close()
        ElMessage.success('模板加载完成')

      } catch (error) {
        console.error('模板加载失败:', error)
        ElMessage.error('模板加载失败: ' + error.message)
      }
    },

    handleSend() {
      if (!this.canSend) {
        ElMessage.warning('请填写标题和内容要求')
        return
      }

      // 只传递表单数据，让父组件来处理
      this.$emit('send-message', { ...this.formData })
    },

    handleEnterPress(event) {
      if (event.shiftKey) {
        return
      }
      this.handleSend()
    },

    handleTemplateUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = this.getFileType(file.name)
        const fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name
        }
        this.formData.referenceTemplate.push(fileInfo)
        ElMessage.success('格式参考文档上传成功')
      } else {
        ElMessage.error(response.msg || '文件上传失败')
      }
    },

    handleReferenceUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = this.getFileType(file.name)
        const fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name
        }
        this.formData.referenceMaterials.push(fileInfo)
        ElMessage.success('内容依据文档上传成功')
      } else {
        ElMessage.error(response.msg || '文件上传失败')
      }
    },

    handleUploadError(error) {
      console.error('文件上传失败:', error)
      ElMessage.error('文件上传失败，请重试')
    },

    beforeUpload(file) {
      const isValidType = this.isValidFileType(file.type, file.name)
      const isValidSize = file.size / 1024 / 1024 < 50 // 50MB

      if (!isValidType) {
        ElMessage.error('不支持的文件格式')
        return false
      }

      if (!isValidSize) {
        ElMessage.error('文件大小不能超过 50MB')
        return false
      }

      return true
    },

    removeTemplateFile(file) {
      const index = this.formData.referenceTemplate.findIndex(f => f.fileId === file.fileId)
      if (index > -1) {
        this.formData.referenceTemplate.splice(index, 1)
      }
    },

    removeReferenceFile(file) {
      const index = this.formData.referenceMaterials.findIndex(f => f.fileId === file.fileId)
      if (index > -1) {
        this.formData.referenceMaterials.splice(index, 1)
      }
    },

    previewFile(fileId, fileName) {
      // TODO: 实现文件预览功能
      console.log('预览文件:', fileId, fileName)
      ElMessage.info('文件预览功能开发中...')
    },

    truncateFileName(fileName, maxLength = 12) {
      if (!fileName) return ''

      // 如果文件名长度小于等于最大长度，直接返回
      if (fileName.length <= maxLength) {
        return fileName
      }

      // 获取文件扩展名
      const dotIndex = fileName.lastIndexOf('.')
      const extension = dotIndex !== -1 ? fileName.substring(dotIndex) : ''
      const nameWithoutExt = dotIndex !== -1 ? fileName.substring(0, dotIndex) : fileName

      // 计算可用于显示文件名的长度（预留扩展名和省略号的空间）
      const availableLength = maxLength - extension.length - 3 // 3 是省略号的长度

      if (availableLength <= 0) {
        // 如果可用长度不够，只显示省略号和扩展名
        return '...' + extension
      }

      // 截断文件名并添加省略号
      return nameWithoutExt.substring(0, availableLength) + '...' + extension
    },

    /**
     * 获取文件类型
     */
    getFileType(filename) {
      const extension = filename.split('.').pop().toUpperCase()

      const typeMap = {
        DOCUMENT: ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
        IMAGE: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
        AUDIO: ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
        VIDEO: ['MP4', 'MOV', 'MPEG', 'MPGA']
      }

      for (const [type, extensions] of Object.entries(typeMap)) {
        if (extensions.includes(extension)) {
          return {
            type,
            extension
          }
        }
      }

      return {
        type: 'CUSTOM',
        extension
      }
    },

    isValidFileType(fileType, fileName) {
      const validTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
        'application/pdf', 'text/plain',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/csv'
      ]

      const extension = fileName.split('.').pop().toLowerCase()
      const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'txt', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'csv']

      return validTypes.includes(fileType) || validExtensions.includes(extension)
    }
  }
}
</script>

<style scoped>
.official-announcement-template {
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 公文公告区域样式 */
.announcement-area {
  flex: 1;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.announcement-form {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  min-height: 120px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.announcement-form:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border-color: #cbd5e1;
}

/* 句子填空式表单样式 */
.sentence-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  line-height: 1.6;
  font-size: 14px;
  animation: slideInUp 0.3s ease-out;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-text {
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
  margin: 0 4px;
}

.inline-form-item {
  display: inline-flex;
  align-items: center;
  margin: 2px 0;
}

.inline-select {
  min-width: 80px;
  width: auto;
}

.inline-input {
  min-width: 120px;
  width: auto;
}

.inline-date {
  min-width: 140px;
  width: auto;
}

.file-item {
  max-width: 200px;
}

/* 文件上传区域样式 */
.file-upload-wrapper {
  min-height: 28px;
  display: inline-flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  max-width: 180px;
  position: relative;
}

.file-upload-wrapper:empty::before {
  content: "暂无文件";
  color: #9ca3af;
  font-size: 11px;
  line-height: 24px;
  opacity: 0.7;
}

.compact-tag {
  max-width: 100%;
  font-size: 11px !important;
  height: 24px !important;
  line-height: 22px !important;
  padding: 0 8px !important;
  border-radius: 4px;
  white-space: nowrap;
}

.compact-upload-btn .el-upload {
  display: inline-block;
  width: 100%;
}

.compact-upload-btn:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

.upload-text-btn {
  font-size: 11px !important;
  padding: 4px 8px !important;
  height: 24px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background: #ffffff !important;
  color: #3b82f6 !important;
  width: 100%;
  justify-content: center;
  white-space: nowrap;
}

.upload-text-btn:hover {
  background: #f8fafc !important;
  border-color: #3b82f6 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.upload-text-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
}

.file-tag {
  max-width: 100%;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  margin-bottom: 4px;
  font-size: 11px;
}

.clickable-file-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-file-tag:hover {
  opacity: 0.8;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 相关参考文件标签特殊样式 */
.reference-file-tag {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #86efac;
  color: #059669;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 4px;
  max-width: 120px;
  margin: 2px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reference-file-tag:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-color: #6ee7b7;
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.1);
  transform: translateY(-1px);
}

.reference-file-tag :deep(.el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  font-size: 11px;
  line-height: 1.2;
}

.reference-file-tag :deep(.el-tag__close) {
  color: #059669;
  opacity: 0.7;
  transition: all 0.2s ease;
  margin-left: 4px;
  font-size: 10px;
}

.reference-file-tag:hover :deep(.el-tag__close) {
  opacity: 1;
  color: #047857;
}

.input-area {
  padding: 12px 16px;
}

.announcement-textarea {
  min-height: 50px !important;
  height: 50px;
  font-size: 13px !important;
  line-height: 1.4;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  transition: all 0.3s ease;
  padding: 6px 8px !important;
}

.announcement-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.announcement-textarea::placeholder {
  color: #9ca3af;
  font-size: 13px;
}

.bottom-toolbar {
  padding: 0px 16px;
  border-top: 1px solid #e5e7eb;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
}

.func-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bg-primary {
  background-color: #3b82f6;
}

.hover\:bg-primary-middle:hover {
  background-color: #2563eb;
}

/* Element Plus 样式覆盖 - 句子填空式表单 */
:deep(.sentence-form .el-input) {
  font-size: 13px;
}

:deep(.sentence-form .el-input__wrapper) {
  border-radius: 4px;
  border: 1px solid #d1d5db;
  box-shadow: none;
  transition: all 0.3s ease;
  background: #ffffff;
}

:deep(.sentence-form .el-input__wrapper:hover) {
  border-color: #93c5fd;
  background: #fefefe;
}

:deep(.sentence-form .el-input.is-focus .el-input__wrapper) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  background: #fefefe;
}

:deep(.sentence-form .el-input__inner) {
  font-size: 13px;
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
}

:deep(.sentence-form .el-select) {
  min-width: 80px;
}

:deep(.sentence-form .el-select .el-input__wrapper) {
  cursor: pointer;
}

:deep(.sentence-form .el-date-editor) {
  min-width: 140px;
}

:deep(.sentence-form .el-date-editor .el-input__wrapper) {
  cursor: pointer;
}

:deep(.sentence-form .el-date-editor .el-input__inner) {
  cursor: pointer;
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

/* 修复日期选择器清除按钮导致的宽度变化问题 */
:deep(.sentence-form .el-date-editor .el-input__wrapper) {
  min-width: 140px;
  width: 140px;
  box-sizing: border-box;
}

:deep(.sentence-form .el-date-editor .el-input__suffix) {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.sentence-form .el-date-editor .el-input__suffix-inner) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

:deep(.sentence-form .el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

/* 上传组件拖拽区域优化 */
:deep(.announcement-form .el-upload) {
  width: 100%;
  border: none;
}

:deep(.announcement-form .el-upload .el-upload-dragger) {
  width: 100%;
  height: auto;
  border: 1px dashed #d1d5db;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

:deep(.announcement-form .el-upload .el-upload-dragger:hover) {
  border-color: #3b82f6;
  background: #f8fafc;
}

:deep(.el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  max-width: 120px;
  font-size: 11px;
}

/* Element Plus 样式覆盖 */
:deep(.announcement-form .el-form-item) {
  margin-bottom: 8px;
}

:deep(.announcement-form .el-input) {
  font-size: 13px;
}

:deep(.announcement-form .el-input__inner) {
  font-size: 13px;
  border-radius: 4px;
  border-color: #d1d5db;
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
  transition: all 0.3s ease;
}

:deep(.announcement-form .el-input__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  background: #fefefe;
  transform: translateY(-1px);
}

:deep(.announcement-form .el-input__inner:hover) {
  border-color: #93c5fd;
  background: #fefefe;
}

:deep(.announcement-form .el-textarea__inner) {
  font-size: 13px;
  border-radius: 4px;
  border-color: #d1d5db;
  padding: 8px;
  transition: all 0.3s ease;
}

:deep(.announcement-form .el-textarea__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  background: #fefefe;
}

:deep(.announcement-form .el-date-editor) {
  width: 100%;
}

:deep(.announcement-form .el-date-editor .el-input__inner) {
  cursor: pointer;
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

:deep(.announcement-form .el-date-editor--date) {
  width: 100%;
}

:deep(.announcement-form .el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

:deep(.announcement-form .el-select) {
  width: 100%;
}

:deep(.announcement-form .el-select .el-input__inner) {
  cursor: pointer;
}

:deep(.announcement-form .el-upload) {
  width: 100%;
  border: none;
}

:deep(.el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  max-width: 120px;
  font-size: 11px;
}

/* 响应式设计 - 句子填空式表单 */
@media (max-width: 992px) {
  .announcement-form {
    padding: 12px !important;
    min-height: 100px;
  }

  .sentence-form {
    font-size: 13px;
    gap: 6px;
  }

  .form-text {
    font-size: 13px;
  }

  .inline-input {
    min-width: 100px;
  }

  .inline-date {
    min-width: 120px;
  }

  .file-item {
    max-width: 160px;
  }

  :deep(.sentence-form .el-input__inner) {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
  }

  :deep(.sentence-form .el-date-editor .el-input__inner) {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
  }

  :deep(.sentence-form .el-date-editor .el-input__wrapper) {
    min-width: 120px;
    width: 120px;
    box-sizing: border-box;
  }

  :deep(.announcement-form .el-col-5) {
    flex: 0 0 25%;
    max-width: 25%;
  }

  :deep(.announcement-form .el-col-7) {
    flex: 0 0 35%;
    max-width: 35%;
  }

  :deep(.announcement-form .el-col-12) {
    flex: 0 0 40%;
    max-width: 40%;
  }

  :deep(.announcement-form .el-col-6) {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .compact-form-item :deep(.el-form-item__label) {
    font-size: 11px !important;
  }

  :deep(.announcement-form .el-input__inner) {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .announcement-form {
    padding: 10px !important;
    min-height: 80px;
  }

  .sentence-form {
    font-size: 12px;
    gap: 4px;
    flex-direction: column;
    align-items: flex-start;
  }

  .form-text {
    font-size: 12px;
    margin: 4px 0 2px 0;
  }

  .inline-form-item {
    width: 100%;
    margin: 2px 0 8px 0;
  }

  .inline-input,
  .inline-select,
  .inline-date {
    width: 100%;
    min-width: auto;
  }

  .file-item {
    max-width: 100%;
  }

  .file-upload-wrapper {
    max-width: 100%;
  }

  .announcement-textarea {
    min-height: 40px !important;
    height: 40px;
    font-size: 12px !important;
  }

  :deep(.sentence-form .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    padding: 0 6px;
  }

  :deep(.sentence-form .el-date-editor .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
  }

  :deep(.sentence-form .el-date-editor .el-input__wrapper) {
    width: 100%;
    min-width: auto;
    box-sizing: border-box;
  }
}

@media (max-width: 480px) {
  .announcement-form {
    padding: 8px !important;
    min-height: 60px;
  }

  .announcement-textarea {
    min-height: 35px !important;
    height: 35px;
    font-size: 11px !important;
  }

  .announcement-row {
    margin-bottom: 6px !important;
  }

  .compact-form-item {
    margin-bottom: 6px !important;
  }

  :deep(.announcement-form .el-col-5),
  :deep(.announcement-form .el-col-7),
  :deep(.announcement-form .el-col-12),
  :deep(.announcement-form .el-col-6) {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 4px;
  }

  .announcement-textarea {
    min-height: 40px !important;
    height: 40px;
    font-size: 12px !important;
  }

  .compact-form-item :deep(.el-form-item__label) {
    font-size: 11px !important;
  }

  :deep(.announcement-form .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    padding: 0 6px;
  }

  :deep(.announcement-form .el-date-editor .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
  }

  :deep(.sentence-form .el-date-editor .el-input__wrapper) {
    width: 100%;
    min-width: auto;
    box-sizing: border-box;
  }
}
</style>