<template>
  <div class="outline-node" :class="[`level-${level}`, { 'has-children': hasChildren }]">
    <!-- 节点内容 -->
    <div class="node-content" @click="handleNodeClick">
      <!-- 展开/收起按钮 -->
      <div class="expand-btn" v-if="hasChildren" @click.stop="toggleExpand">
        <el-icon class="expand-icon" :class="{ 'expanded': node.isExpanded }">
          <CaretRight />
        </el-icon>
      </div>
      
      <!-- 层级标识 -->
      <div class="level-indicator" :class="`h${level}`">
        <span class="level-text">H{{ level }}</span>
      </div>
      
      <!-- 编号 -->
      <div class="node-number" v-if="node.number || node.subNumber">
        <span v-if="node.number">{{ node.number }}</span>
        <span v-if="node.subNumber" class="sub-number">({{ node.subNumber }})</span>
      </div>
      
      <!-- 文本内容 -->
      <div class="node-text" v-if="!node.isEditing">
        <span>{{ node.text }}</span>
        <el-button 
          v-if="editable" 
          size="small" 
          type="text" 
          class="edit-btn"
          @click.stop="startEdit"
        >
          <el-icon><Edit /></el-icon>
        </el-button>
      </div>
      
      <!-- 编辑模式 -->
      <div class="node-edit" v-else>
        <el-input
          v-model="editText"
          size="small"
          @blur="saveEdit"
          @keyup.enter="saveEdit"
          @keyup.esc="cancelEdit"
          ref="editInput"
        />
        <div class="edit-actions">
          <el-button size="small" type="text" @click="saveEdit">
            <el-icon><Check /></el-icon>
          </el-button>
          <el-button size="small" type="text" @click="cancelEdit">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 内容描述 -->
    <div class="node-description" v-if="node.content && node.content.length > 0">
      <div class="description-item" v-for="(item, index) in node.content" :key="index">
        {{ item }}
      </div>
    </div>
    
    <!-- 子节点 -->
    <div class="node-children" v-if="hasChildren && node.isExpanded">
      <OutlineNode
        v-for="(child, index) in node.children"
        :key="child.id"
        :node="child"
        :level="level + 1"
        :editable="editable"
        @edit-node="$emit('edit-node', $event, $event)"
        @toggle-node="$emit('toggle-node', $event)"
      />
    </div>
  </div>
</template>

<script>
import { CaretRight, Edit, Check, Close } from '@element-plus/icons-vue'

export default {
  name: 'OutlineNode',
  components: {
    CaretRight,
    Edit,
    Check,
    Close
  },
  props: {
    node: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 1
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      editText: ''
    }
  },
  computed: {
    hasChildren() {
      return this.node.children && this.node.children.length > 0
    }
  },
  methods: {
    /**
     * 切换展开/收起
     */
    toggleExpand() {
      this.$emit('toggle-node', this.node.id)
    },
    
    /**
     * 处理节点点击
     */
    handleNodeClick() {
      // 可以在这里添加节点选中逻辑
    },
    
    /**
     * 开始编辑
     */
    startEdit() {
      if (!this.editable) return
      
      this.editText = this.node.text
      this.node.isEditing = true
      
      this.$nextTick(() => {
        if (this.$refs.editInput) {
          this.$refs.editInput.focus()
        }
      })
    },
    
    /**
     * 保存编辑
     */
    saveEdit() {
      if (this.editText.trim() && this.editText !== this.node.text) {
        this.$emit('edit-node', this.node.id, this.editText.trim())
      }
      this.node.isEditing = false
    },
    
    /**
     * 取消编辑
     */
    cancelEdit() {
      this.node.isEditing = false
      this.editText = this.node.text
    }
  }
}
</script>

<style scoped>
.outline-node {
  margin-bottom: 8px;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.node-content:hover {
  background-color: #f8fafc;
}

.expand-btn {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  background-color: #e5e7eb;
}

.expand-icon {
  transition: transform 0.2s ease;
  color: #6b7280;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.level-indicator {
  min-width: 32px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.level-indicator.h1 {
  background: #3b82f6;
}

.level-indicator.h2 {
  background: #10b981;
}

.level-indicator.h3 {
  background: #f59e0b;
}

.level-indicator.h4 {
  background: #ef4444;
}

.level-indicator.h5 {
  background: #8b5cf6;
}

.level-indicator.h6 {
  background: #6b7280;
}

.level-text {
  font-size: 10px;
  font-weight: 600;
}

.node-number {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  color: #374151;
}

.sub-number {
  color: #6b7280;
  font-size: 12px;
}

.node-text {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
}

.edit-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  padding: 4px;
  margin-left: 8px;
}

.node-content:hover .edit-btn {
  opacity: 1;
}

.node-edit {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-actions {
  display: flex;
  gap: 4px;
}

.node-description {
  margin-left: 60px;
  margin-top: 4px;
  padding-left: 12px;
  border-left: 2px solid #e5e7eb;
}

.description-item {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 4px;
}

.node-children {
  margin-left: 20px;
  padding-left: 16px;
  border-left: 1px solid #e5e7eb;
}

/* 不同层级的样式调整 */
.level-2 .node-content {
  padding-left: 8px;
}

.level-3 .node-content {
  padding-left: 4px;
}

.level-4 .node-content,
.level-5 .node-content,
.level-6 .node-content {
  padding-left: 2px;
}

/* 层级缩进 */
.level-1 {
  margin-left: 0;
}

.level-2 {
  margin-left: 8px;
}

.level-3 {
  margin-left: 16px;
}

.level-4 {
  margin-left: 24px;
}

.level-5 {
  margin-left: 32px;
}

.level-6 {
  margin-left: 40px;
}
</style>
