<template>
  <div class="outline-node" :class="[`level-${level}`, { 'has-children': hasChildren, 'dragging': isDragging }]">
    <!-- 节点内容 -->
    <div class="node-content" @click="handleNodeClick">
      <!-- 拖拽手柄 -->
      <div class="drag-handle" v-if="draggable" title="拖拽排序">
        <el-icon class="drag-icon">
          <DCaret />
        </el-icon>
      </div>

      <!-- 展开/收起按钮 -->
      <div class="expand-btn" v-if="hasChildren" @click.stop="toggleExpand">
        <el-icon class="expand-icon" :class="{ 'expanded': node.isExpanded }">
          <CaretRight />
        </el-icon>
      </div>
      
      <!-- 层级标识 -->
      <div class="level-indicator" :class="`h${level}`">
        <span class="level-text">H{{ level }}</span>
      </div>
      
      <!-- 编号 -->
      <div class="node-number" v-if="node.number || node.subNumber">
        <span v-if="node.number">{{ node.number }}</span>
        <span v-if="node.subNumber" class="sub-number">({{ node.subNumber }})</span>
      </div>
      
      <!-- 文本内容 -->
      <div class="node-text" v-if="!node.isEditing">
        <span>{{ node.text }}</span>
        <div class="node-actions" v-if="editable">
          <el-button
            size="small"
            type="text"
            class="action-btn add-btn"
            @click.stop="addChildNode"
            title="添加子标题"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
          <el-button
            size="small"
            type="text"
            class="action-btn edit-btn"
            @click.stop="startEdit"
            title="编辑"
          >
            <el-icon><Edit /></el-icon>
          </el-button>
          <el-button
            size="small"
            type="text"
            class="action-btn delete-btn"
            @click.stop="confirmDelete"
            title="删除"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>
      
      <!-- 编辑模式 -->
      <div class="node-edit" v-else>
        <el-input
          v-model="editText"
          size="small"
          @blur="saveEdit"
          @keyup.enter="saveEdit"
          @keyup.esc="cancelEdit"
          ref="editInput"
        />
        <div class="edit-actions">
          <el-button size="small" type="text" @click="saveEdit">
            <el-icon><Check /></el-icon>
          </el-button>
          <el-button size="small" type="text" @click="cancelEdit">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 内容描述 -->
    <div class="node-description" v-if="node.content && node.content.length > 0">
      <div class="description-item" v-for="(item, index) in node.content" :key="index">
        {{ item }}
      </div>
    </div>
    
    <!-- 子节点 -->
    <div class="node-children" v-if="hasChildren && node.isExpanded">
      <draggable
        v-model="node.children"
        group="outline-nodes"
        item-key="id"
        handle=".drag-handle"
        :animation="200"
        :ghost-class="'ghost-item'"
        :chosen-class="'chosen-item'"
        :drag-class="'drag-item'"
        @start="onChildDragStart"
        @end="onChildDragEnd"
        @change="onChildDragChange"
      >
        <template #item="{ element }">
          <OutlineNode
            :key="element.id"
            :node="element"
            :level="level + 1"
            :editable="editable"
            :draggable="draggable"
            @edit-node="$emit('edit-node', $event, $event)"
            @toggle-node="$emit('toggle-node', $event)"
            @delete-node="$emit('delete-node', $event)"
            @add-node="$emit('add-node', $event, $event)"
            @drag-change="$emit('drag-change')"
          />
        </template>
      </draggable>
    </div>
  </div>
</template>

<script>
import { CaretRight, Edit, Check, Close, Delete, DCaret, Plus } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import draggable from 'vuedraggable'

export default {
  name: 'OutlineNode',
  components: {
    CaretRight,
    Edit,
    Check,
    Close,
    Delete,
    DCaret,
    Plus,
    draggable
  },
  props: {
    node: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 1
    },
    editable: {
      type: Boolean,
      default: true
    },
    draggable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editText: '',
      isDragging: false
    }
  },
  computed: {
    hasChildren() {
      return this.node.children && this.node.children.length > 0
    }
  },
  methods: {
    /**
     * 切换展开/收起
     */
    toggleExpand() {
      this.$emit('toggle-node', this.node.id)
    },
    
    /**
     * 处理节点点击
     */
    handleNodeClick() {
      // 可以在这里添加节点选中逻辑
    },
    
    /**
     * 开始编辑
     */
    startEdit() {
      if (!this.editable) return
      
      this.editText = this.node.text
      this.node.isEditing = true
      
      this.$nextTick(() => {
        if (this.$refs.editInput) {
          this.$refs.editInput.focus()
        }
      })
    },
    
    /**
     * 保存编辑
     */
    saveEdit() {
      if (this.editText.trim() && this.editText !== this.node.text) {
        this.$emit('edit-node', this.node.id, this.editText.trim())
      }
      this.node.isEditing = false
    },
    
    /**
     * 取消编辑
     */
    cancelEdit() {
      this.node.isEditing = false
      this.editText = this.node.text
    },

    /**
     * 确认删除
     */
    confirmDelete() {
      const message = `确定要删除"${this.node.text}"吗？${this.hasChildren ? '删除后其子节点也会一并删除。' : ''}`

      // 使用原生confirm确保功能正常
      if (window.confirm(message)) {
        console.log('用户确认删除:', this.node.text)
        this.deleteNode()
      } else {
        console.log('用户取消删除')
      }
    },

    /**
     * 删除节点
     */
    deleteNode() {
      console.log('删除节点:', this.node.id, this.node.text)
      this.$emit('delete-node', this.node.id)
    },

    /**
     * 子节点拖拽开始
     */
    onChildDragStart(evt) {
      this.isDragging = true
      console.log('子节点拖拽开始:', evt)
    },

    /**
     * 子节点拖拽结束
     */
    onChildDragEnd(evt) {
      this.isDragging = false
      console.log('子节点拖拽结束:', evt)
      this.$emit('drag-change')
    },

    /**
     * 子节点拖拽变化
     */
    onChildDragChange(evt) {
      console.log('子节点拖拽变化:', evt)
      this.$emit('drag-change')
    },

    /**
     * 添加子节点
     */
    addChildNode() {
      const newNode = {
        id: Date.now() + Math.random(),
        level: this.level + 1,
        number: '',
        subNumber: '',
        text: '新标题',
        children: [],
        isExpanded: true,
        isEditing: true,
        originalText: '新标题'
      }

      // 如果当前节点没有children数组，创建一个
      if (!this.node.children) {
        this.node.children = []
      }

      // 添加新节点到children数组
      this.node.children.push(newNode)

      // 确保父节点是展开状态
      this.node.isExpanded = true

      // 触发内容变更事件
      this.$emit('add-node', newNode, this.node.id)

      console.log('添加子节点:', newNode.text, '到父节点:', this.node.text)
    }
  }
}
</script>

<style scoped>
.outline-node {
  margin-bottom: 8px;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.node-content:hover {
  background-color: #f8fafc;
}

/* 拖拽手柄样式 */
.drag-handle {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  border-radius: 4px;
  transition: all 0.2s ease;
  opacity: 0;
}

.node-content:hover .drag-handle {
  opacity: 1;
}

.drag-handle:hover {
  background-color: #e5e7eb;
}

.drag-handle:active {
  cursor: grabbing;
}

.drag-icon {
  color: #6b7280;
  transform: rotate(90deg);
}

.expand-btn {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  background-color: #e5e7eb;
}

.expand-icon {
  transition: transform 0.2s ease;
  color: #6b7280;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.level-indicator {
  min-width: 32px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.level-indicator.h1 {
  background: #3b82f6;
}

.level-indicator.h2 {
  background: #10b981;
}

.level-indicator.h3 {
  background: #f59e0b;
}

.level-indicator.h4 {
  background: #ef4444;
}

.level-indicator.h5 {
  background: #8b5cf6;
}

.level-indicator.h6 {
  background: #6b7280;
}

.level-text {
  font-size: 10px;
  font-weight: 600;
}

.node-number {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  color: #374151;
}

.sub-number {
  color: #6b7280;
  font-size: 12px;
}

.node-text {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
}

.node-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: 8px;
}

.node-content:hover .node-actions {
  opacity: 1;
}

.action-btn {
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.add-btn:hover {
  background-color: #f0fdf4;
  color: #16a34a;
}

.edit-btn:hover {
  background-color: #e0f2fe;
  color: #0369a1;
}

.delete-btn:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

.node-edit {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-actions {
  display: flex;
  gap: 4px;
}

.node-description {
  margin-left: 60px;
  margin-top: 4px;
  padding-left: 12px;
  border-left: 2px solid #e5e7eb;
}

.description-item {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 4px;
}

.node-children {
  margin-left: 20px;
  padding-left: 16px;
  border-left: 1px solid #e5e7eb;
}

/* 不同层级的样式调整 */
.level-2 .node-content {
  padding-left: 8px;
}

.level-3 .node-content {
  padding-left: 4px;
}

.level-4 .node-content,
.level-5 .node-content,
.level-6 .node-content {
  padding-left: 2px;
}

/* 层级缩进 */
.level-1 {
  margin-left: 0;
}

.level-2 {
  margin-left: 8px;
}

.level-3 {
  margin-left: 16px;
}

.level-4 {
  margin-left: 24px;
}

.level-5 {
  margin-left: 32px;
}

.level-6 {
  margin-left: 40px;
}

/* 删除确认对话框样式 */
:deep(.outline-delete-confirm) {
  border-radius: 8px;
}

:deep(.outline-delete-confirm .el-message-box__header) {
  padding-bottom: 12px;
}

:deep(.outline-delete-confirm .el-message-box__title) {
  color: #dc2626;
  font-weight: 600;
}

:deep(.outline-delete-confirm .el-message-box__content) {
  padding-top: 0;
}

:deep(.outline-delete-confirm .el-button--primary) {
  background-color: #dc2626;
  border-color: #dc2626;
}

:deep(.outline-delete-confirm .el-button--primary:hover) {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

/* 拖拽状态样式 */
.ghost-item {
  opacity: 0.5;
  background-color: #e0f2fe;
  border: 2px dashed #0369a1;
  border-radius: 6px;
}

.chosen-item {
  background-color: #f0f9ff;
  border: 1px solid #0369a1;
  box-shadow: 0 4px 8px rgba(3, 105, 161, 0.2);
}

.drag-item {
  transform: rotate(5deg);
  opacity: 0.8;
}

.outline-node.dragging {
  opacity: 0.6;
}

/* 拖拽区域指示 */
.node-children {
  position: relative;
}

.node-children::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e5e7eb;
  transition: background-color 0.2s ease;
}

.node-children:hover::before {
  background-color: #3b82f6;
}
</style>
