<template>
  <button
    @click="handleDownload"
    class="p-1 text-gray-400 rounded-full transition-colors duration-200 hover:text-primary hover:bg-gray-50"
    title="下载消息"
    :disabled="message.downloadLoading"
  >
    <svg v-if="!message.downloadLoading" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
      <polyline points="7,10 12,15 17,10"/>
      <line x1="12" y1="15" x2="12" y2="3"/>
    </svg>
    <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="animate-spin">
      <path d="M21 12a9 9 0 1 1-6.219-8.56"/>
    </svg>
  </button>
</template>

<script>
import { ElMessage } from 'element-plus'
import { textToFile } from '@/api/ai'

export default {
  name: 'DownloadButton',
  props: {
    // 消息对象
    message: {
      type: Object,
      required: true
    }
  },
  methods: {
    // 处理下载操作
    handleDownload() {
      this.downloadMessage(this.message)
    },

    // 下载消息方法
    async downloadMessage(message) {
      if (!message.content || message.downloadLoading) return

      // 设置下载状态
      message.downloadLoading = true

      try {
        // 获取原始内容或提取纯文本内容
        let textContent
        if (message.rawContent) {
          // 使用原始内容，简单清理HTML标签但保留格式
          textContent = message.rawContent
            .replace(/<think\b[^<]*(?:(?!<\/think>)<[^<]*)*<\/think>/gi, '') // 移除think标签及其内容
            .replace(/<details[^>]*>.*?Thinking.*?<\/details>/gis, '') // 移除包含Thinking的details标签
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
            .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // 移除style标签
            .replace(/<[^>]*>/g, '') // 移除其他HTML标签
            .replace(/&nbsp;/g, ' ') // 替换HTML空格
            .replace(/&lt;/g, '<') // 解码HTML实体
            .replace(/&gt;/g, '>')
            .replace(/&amp;/g, '&')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
        } else {
          // 对于用户消息或没有rawContent的情况，使用现有逻辑
          const tempElement = document.createElement('div')
          tempElement.innerHTML = message.content
          textContent = tempElement.textContent || tempElement.innerText
        }

        console.log('textContent:', textContent)

        // 第一步：调用文本转文件接口
        ElMessage({
          message: '正在生成文件...',
          type: 'info',
          duration: 2000
        })

        // 当消息内容为空时，使用messageId；否则使用text
        const params = (!textContent || !textContent.trim()) ?
          { messageId: message.messageId } :
          { text: textContent }

        const textToFileResponse = await textToFile(params)

        if (!textToFileResponse) {
          throw new Error('文件生成失败')
        }
        const fileId = textToFileResponse

        // 第二步：调用下载文件接口
        ElMessage({
          message: '正在下载文件...',
          type: 'info',
          duration: 2000
        })

        // 创建 Blob 对象
        const blob = new Blob([fileId], {
          type: 'application/octet-stream'
        })

        // 创建下载链接
        const downloadLink = document.createElement('a')
        downloadLink.href = URL.createObjectURL(blob)
        downloadLink.download = `AI消息_${new Date().getTime()}.docx`

        // 触发下载
        document.body.appendChild(downloadLink)
        downloadLink.click()

        // 清理
        document.body.removeChild(downloadLink)
        URL.revokeObjectURL(downloadLink.href)

        ElMessage({
          message: '下载成功',
          type: 'success',
          duration: 2000
        })

      } catch (error) {
        console.error('下载消息失败:', error)
        ElMessage({
          message: error.message || '下载失败，请重试',
          type: 'error',
          duration: 3000
        })
      } finally {
        // 清除下载状态
        message.downloadLoading = false
      }
    }
  }
}
</script>

<style scoped>
/* 组件样式已通过class内联定义，无需额外样式 */
</style>