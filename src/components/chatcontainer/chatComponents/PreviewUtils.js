import { ElMessage, ElImageViewer } from 'element-plus'
import { createVNode, render } from 'vue'

// 创建简单的工具函数，供外部直接调用
const baseURL = import.meta.env.VITE_API_BASE_URL || ''

// 导出预览相关的方法，供组件直接使用
export const getFileType = (filename) => {
  const extension = filename.split('.').pop().toUpperCase()
  const typeMap = {
    DOCUMENT: ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
    IMAGE: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
    AUDIO: ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
    VIDEO: ['MP4', 'MOV', 'MPEG', 'MPGA']
  }
  for (const [type, extensions] of Object.entries(typeMap)) {
    if (extensions.includes(extension)) {
      return { type, extension }
    }
  }
  return { type: 'CUSTOM', extension }
}

export const getFileIcon = (fileType) => {
  const icons = {
    'DOCUMENT': '📄', 'IMAGE': '🖼️', 'AUDIO': '🎵', 'VIDEO': '🎥', 'CUSTOM': '📎'
  }
  return icons[fileType] || '📎'
}

export const getQuestionImage = (fileId) => {
  if (!fileId || fileId.trim() === '') return ''
  return `${baseURL}/v1/public/file/get?fileId=${fileId}`
}

export const getFilePreviewUrl = (fileId) => {
  if (!fileId || fileId.trim() === '') return ''
  return `${baseURL}/v1/public/file/get?fileId=${fileId}`
}

export const createImageViewer = (imageUrl) => {
  const container = document.createElement('div')
  const vnode = createVNode(ElImageViewer, {
    urlList: [imageUrl],
    onClose: () => {
      render(null, container)
      if (document.body.contains(container)) {
        document.body.removeChild(container)
      }
    }
  })
  document.body.appendChild(container)
  render(vnode, container)
}

export const previewFile = (file) => {
  if (file.fileType === 'IMAGE') {
    const img = new Image()
    img.src = URL.createObjectURL(file.raw)
    window.open(img.src, '_blank')
  } else if (file.fileType === 'DOCUMENT' && ['PDF'].includes(file.extension)) {
    window.open(URL.createObjectURL(file.raw))
  } else if (file.fileType === 'DOCUMENT' && ['TXT', 'MD', 'HTML', 'CSV'].includes(file.extension)) {
    const reader = new FileReader()
    reader.onload = (e) => {
      console.log('文件内容预览:', e.target.result)
      ElMessage.info('文件内容已在控制台输出')
    }
    reader.readAsText(file.raw)
  } else if (file.fileType === 'AUDIO') {
    const audio = new Audio(URL.createObjectURL(file.raw))
    audio.play()
  } else if (file.fileType === 'VIDEO') {
    window.open(URL.createObjectURL(file.raw), '_blank')
  } else {
    ElMessage.info('该文件类型暂不支持预览')
  }
}

export const previewQuestionImage = (fileId) => {
  if (!fileId || fileId.trim() === '') return
  const imageUrl = getQuestionImage(fileId)
  createImageViewer(imageUrl)
}

export const previewMessageFile = (fileId, fileType) => {
  if (!fileId || fileId.trim() === '') return
  if (fileType === 'IMAGE' || fileType === 'image') {
    const imageUrl = getQuestionImage(fileId)
    createImageViewer(imageUrl)
  }
}

export const previewReportFile = (fileName) => {
  try {
    const pdfUrl = `/${fileName}`
    const newWindow = window.open(pdfUrl, '_blank')
    if (!newWindow) {
      ElMessage.warning('请允许浏览器弹窗以预览PDF文件')
    } else {
      console.log('在新页面打开PDF文件:', fileName)
    }
  } catch (error) {
    console.error('预览PDF文件失败:', error)
    ElMessage.error('无法预览PDF文件')
  }
}

export const previewFileById = (fileId, fileName) => {
  if (!fileId || fileId.trim() === '') return
  try {
    const fileUrl = getFilePreviewUrl(fileId)
    const newWindow = window.open(fileUrl, '_blank')
    if (!newWindow) {
      ElMessage.warning('弹窗被阻止，请允许弹窗或手动复制链接访问')
      console.log('文件预览链接：', fileUrl)
    }
  } catch (error) {
    console.error('预览文件失败：', error)
    ElMessage.error('预览文件失败，请稍后重试')
  }
}

export const escapeRegExp = (string) => {
  if (typeof string !== 'string') return ''
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

export const renderUserMessageContent = (message) => {
  if (!message.content) return ''
  let content = message.content
  if (message.files && message.files.length > 0) {
    message.files.forEach(file => {
      if (!file || !file.fileId || !file.fileName || !file.fileType) {
        console.warn('文件信息不完整:', file)
        return
      }
      const fileIcon = getFileIcon(file.fileType)
      if (file.fileType === 'IMAGE' || file.fileType === 'image') {
        const escapedFileName = file.fileName.replace(/'/g, '&#39;').replace(/"/g, '&quot;')
        const clickableLink = `<span class="file-link cursor-pointer hover:underline" onclick="window.chatContainerPro.previewMessageFile('${file.fileId}', '${file.fileType}')" title="点击预览图片">${fileIcon} ${escapedFileName}</span>`
        const regex = new RegExp(`${escapeRegExp(fileIcon)}\\s+${escapeRegExp(file.fileName)}`, 'g')
        content = content.replace(regex, clickableLink)
      }
    })
  }
  return content.replace(/\n/g, '<br>')
}
