<template>
  <!-- 工作流节点展示（展开状态） -->
  <div v-if="message.workflowNodes && message.workflowNodes.length > 0 && message.workflowExpanded" class="workflow-container">
    <!-- 展开状态：显示所有节点 -->
    <div class="workflow-expanded">
      <div class="workflow-header">
        <span class="workflow-header-text">执行过程</span>
        <button
          @click="toggleWorkflowExpanded"
          class="workflow-toggle-btn expanded"
        >
          <span class="toggle-text">收起</span>
          <svg class="toggle-arrow up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="18,15 12,9 6,15"></polyline>
          </svg>
        </button>
      </div>
      <div class="workflow-nodes">
        <div v-for="(node, nodeIndex) in message.workflowNodes" :key="nodeIndex" class="workflow-node">
          <div class="workflow-node-bubble" :class="getNodeBubbleClass(node.nodeType, node.nodeStatus, node.nodeName)">
            <div class="node-header">
              <div class="node-icon">
                <span class="icon-emoji">{{ getNodeIcon(node.nodeType, node.nodeName) }}</span>
              </div>
              <div class="node-info">
                <div class="node-name">{{ node.nodeName }}</div>
                <div class="workflow-title">{{ node.workflowTitle }}</div>
              </div>
              <div class="node-status">
                <div v-if="node.nodeStatus === 'start'" class="loading-spinner">
                  <svg class="animate-spin h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
                <div v-else-if="node.nodeStatus === 'end'" class="completed-icon">
                  <svg class="h-4 w-4 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
            </div>

            <!-- 知识库节点内容展示 -->
            <div v-if="isKnowledgeNode(node) && node.documents && node.documents.length > 0" class="knowledge-node-content">
              <!-- 收起状态：仅显示一个展开按钮 -->
              <div v-if="!node.isExpanded" class="knowledge-collapsed">
                <button
                  @click="toggleKnowledgeExpanded(node, nodeIndex)"
                  class="knowledge-toggle-btn collapsed"
                >
                  <span class="toggle-icon">📚</span>
                  <span class="toggle-text">查看检索文档 ({{ node.documents.length }}个片段)</span>
                  <svg class="toggle-arrow" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </button>
              </div>

              <!-- 展开状态：显示所有文档 -->
              <div v-else class="knowledge-expanded">
                <div class="knowledge-header">
                  <span class="knowledge-title">检索到的文档片段</span>
                  <button
                    @click="toggleKnowledgeExpanded(node, nodeIndex)"
                    class="knowledge-toggle-btn expanded"
                  >
                    <span class="toggle-text">收起</span>
                    <svg class="toggle-arrow up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="18,15 12,9 6,15"></polyline>
                    </svg>
                  </button>
                </div>
                <div class="documents-list">
                  <div
                    v-for="(doc, docIndex) in node.documents"
                    :key="docIndex"
                    class="document-item"
                  >
                    <div class="document-header">
                      <div class="document-info">
                        <span class="document-id">文档 #{{ docIndex + 1 }}</span>
                        <span class="similarity-score">相似度: {{ (parseFloat(doc.score) * 100).toFixed(1) }}%</span>
                      </div>
                      <el-popover
                        popper-class="knowledge-doc-tooltip"
                        placement="top"
                        :content="doc.content || '暂无文档内容'"
                        :disabled="!doc.content"
                        :enterable="true"
                        :show-after="300"
                        width="40vw"
                        trigger="hover"
                      >
                        <template #reference>
                          <button class="view-content-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                              <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            查看内容
                          </button>
                        </template>
                      </el-popover>
                    </div>
                    <div class="document-preview">
                      <p class="document-title">{{ doc.title || '文档片段' }}</p>
                      <p class="document-excerpt">
                        {{ (doc.content || '').substring(0, 120) }}{{ (doc.content || '').length > 120 ? '...' : '' }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WorkflowNode',
  props: {
    message: {
      type: Object,
      required: true
    }
  },
  emits: ['toggle-workflow-expanded', 'toggle-knowledge-expanded'],
  methods: {
    /**
     * 切换工作流展开状态
     */
    toggleWorkflowExpanded() {
      this.$emit('toggle-workflow-expanded', this.message);
    },

    /**
     * 切换知识库节点展开状态
     */
    toggleKnowledgeExpanded(node, nodeIndex) {
      this.$emit('toggle-knowledge-expanded', node, nodeIndex);
    },

    /**
     * 获取节点气泡样式类
     */
    getNodeBubbleClass(nodeType, nodeStatus, nodeName = '') {
      const baseClass = 'node-bubble'
      const activeClass = nodeStatus === 'start' ? 'node-active' : 'node-completed'

      // 将节点类型和名称转为小写用于匹配
      const lowerNodeType = (nodeType || '').toLowerCase()
      const lowerNodeName = (nodeName || '').toLowerCase()
      const combinedText = `${lowerNodeType} ${lowerNodeName}`

      if (combinedText.includes('start') || combinedText.includes('开始') || combinedText.includes('起始')) {
        return `${baseClass} node-start ${activeClass}`
      } else if (combinedText.includes('end') || combinedText.includes('结束') || combinedText.includes('终止')) {
        return `${baseClass} node-end ${activeClass}`
      } else if (combinedText.includes('retriever') || combinedText.includes('检索') || combinedText.includes('召回')) {
        return `${baseClass} node-retriever ${activeClass}`
      } else if (combinedText.includes('knowledge') || combinedText.includes('知识库') ||
                 combinedText.includes('知识') || combinedText.includes('文档库') ||
                 combinedText.includes('database')) {
        return `${baseClass} node-knowledge ${activeClass}`
      } else if (combinedText.includes('search') || combinedText.includes('搜索')) {
        return `${baseClass} node-search ${activeClass}`
      } else if (combinedText.includes('llm') || combinedText.includes('语言模型') ||
                 combinedText.includes('gpt') || combinedText.includes('claude')) {
        return `${baseClass} node-llm ${activeClass}`
      } else if (combinedText.includes('model') || combinedText.includes('大模型') ||
                 combinedText.includes('回答') || combinedText.includes('answer')) {
        return `${baseClass} node-model ${activeClass}`
      } else if (combinedText.includes('workflow') || combinedText.includes('工作流') || combinedText.includes('流程')) {
        return `${baseClass} node-workflow ${activeClass}`
      } else if (combinedText.includes('code') || combinedText.includes('代码') ||
                 combinedText.includes('python') || combinedText.includes('script')) {
        return `${baseClass} node-code ${activeClass}`
      } else if (combinedText.includes('database') || combinedText.includes('数据库') ||
                 combinedText.includes('sql') || combinedText.includes('db')) {
        return `${baseClass} node-database ${activeClass}`
      } else if (combinedText.includes('tool') || combinedText.includes('工具')) {
        return `${baseClass} node-tool ${activeClass}`
      } else {
        return `${baseClass} node-default ${activeClass}`
      }
    },

    /**
     * 获取节点图标
     */
    getNodeIcon(nodeType, nodeName = '') {
      // 根据节点类型和名称返回不同的图标
      const iconMap = {
        'start': '🚀',
        'end': '🎯',
        'search': '🔍',
        'model': '🧠',
        'knowledge': '📚',
        'tool': '🔧',
        'condition': '❓',
        'llm': '🤖',
        'workflow': '⚡',
        'retriever': '📖',
        'answer': '💬',
        'query': '🔎',
        'rewrite': '✏️',
        'classify': '🏷️',
        'extract': '📋',
        'judge': '⚖️',
        'code': '💻',
        'http': '🌐',
        'database': '🗄️',
        'file': '📁',
        'email': '📧',
        'time': '⏰',
        'math': '🧮',
        'text': '📝',
        'image': '🖼️',
        'audio': '🎵',
        'video': '🎬',
        'default': '⚙️'
      }

      // 将节点类型和名称转为小写用于匹配
      const lowerNodeType = (nodeType || '').toLowerCase()
      const lowerNodeName = (nodeName || '').toLowerCase()
      const combinedText = `${lowerNodeType} ${lowerNodeName}`

      // 开始节点
      if (combinedText.includes('start') || combinedText.includes('开始') || combinedText.includes('起始')) {
        return iconMap.start
      }
      // 结束节点
      else if (combinedText.includes('end') || combinedText.includes('结束') || combinedText.includes('终止')) {
        return iconMap.end
      }
      // 搜索/检索节点
      else if (combinedText.includes('search') || combinedText.includes('搜索') ||
               combinedText.includes('retriever') || combinedText.includes('检索') ||
               combinedText.includes('知识库') || combinedText.includes('召回')) {
        return iconMap.search
      }
      // 大模型/LLM节点
      else if (combinedText.includes('llm') || combinedText.includes('model') ||
               combinedText.includes('大模型') || combinedText.includes('语言模型') ||
               combinedText.includes('gpt') || combinedText.includes('claude')) {
        return iconMap.llm
      }
      // 回答生成节点
      else if (combinedText.includes('answer') || combinedText.includes('回答') ||
               combinedText.includes('生成') || combinedText.includes('response')) {
        return iconMap.answer
      }
      // 查询重写节点
      else if (combinedText.includes('rewrite') || combinedText.includes('重写') ||
               combinedText.includes('改写') || combinedText.includes('query')) {
        return iconMap.rewrite
      }
      // 分类节点
      else if (combinedText.includes('classify') || combinedText.includes('分类') ||
               combinedText.includes('判断') || combinedText.includes('classification')) {
        return iconMap.classify
      }
      // 提取节点
      else if (combinedText.includes('extract') || combinedText.includes('提取') ||
               combinedText.includes('抽取')) {
        return iconMap.extract
      }
      // 条件判断节点
      else if (combinedText.includes('condition') || combinedText.includes('条件') ||
               combinedText.includes('if') || combinedText.includes('判断')) {
        return iconMap.judge
      }
      // 代码执行节点
      else if (combinedText.includes('code') || combinedText.includes('代码') ||
               combinedText.includes('python') || combinedText.includes('script')) {
        return iconMap.code
      }
      // HTTP请求节点
      else if (combinedText.includes('http') || combinedText.includes('api') ||
               combinedText.includes('请求') || combinedText.includes('接口')) {
        return iconMap.http
      }
      // 数据库节点
      else if (combinedText.includes('database') || combinedText.includes('数据库') ||
               combinedText.includes('sql') || combinedText.includes('db')) {
        return iconMap.database
      }
      // 文件处理节点
      else if (combinedText.includes('file') || combinedText.includes('文件') ||
               combinedText.includes('upload') || combinedText.includes('上传')) {
        return iconMap.file
      }
      // 邮件节点
      else if (combinedText.includes('email') || combinedText.includes('邮件') ||
               combinedText.includes('mail')) {
        return iconMap.email
      }
      // 时间处理节点
      else if (combinedText.includes('time') || combinedText.includes('时间') ||
               combinedText.includes('date') || combinedText.includes('日期')) {
        return iconMap.time
      }
      // 数学计算节点
      else if (combinedText.includes('math') || combinedText.includes('计算') ||
               combinedText.includes('数学') || combinedText.includes('calc')) {
        return iconMap.math
      }
      // 文本处理节点
      else if (combinedText.includes('text') || combinedText.includes('文本') ||
               combinedText.includes('字符串') || combinedText.includes('string')) {
        return iconMap.text
      }
      // 图像处理节点
      else if (combinedText.includes('image') || combinedText.includes('图像') ||
               combinedText.includes('图片') || combinedText.includes('vision')) {
        return iconMap.image
      }
      // 音频处理节点
      else if (combinedText.includes('audio') || combinedText.includes('音频') ||
               combinedText.includes('声音') || combinedText.includes('语音')) {
        return iconMap.audio
      }
      // 视频处理节点
      else if (combinedText.includes('video') || combinedText.includes('视频') ||
               combinedText.includes('影像')) {
        return iconMap.video
      }
      // 工具节点
      else if (combinedText.includes('tool') || combinedText.includes('工具')) {
        return iconMap.tool
      }
      // 知识库节点
      else if (combinedText.includes('knowledge') || combinedText.includes('知识库') ||
               combinedText.includes('知识') || combinedText.includes('文档库') ||
               combinedText.includes('database')) {
        return iconMap.knowledge
      }
      // 默认图标
      else {
        return iconMap.default
      }
    },

    /**
     * 判断是否为知识库节点
     */
    isKnowledgeNode(node) {
      // 通过节点名称判断是否为知识库节点
      return this.isKnowledgeNodeByName(node.nodeName) ||
             node.nodeType === 'knowledge' ||
             (node.documents && node.documents.length > 0);
    },

    /**
     * 通过节点名称判断是否为知识库节点
     */
    isKnowledgeNodeByName(nodeName) {
      if (!nodeName) return false;
      const knowledgeKeywords = ['知识库', 'knowledge', '检索', 'retriever', '召回', 'search'];
      const lowerNodeName = nodeName.toLowerCase();
      return knowledgeKeywords.some(keyword =>
        lowerNodeName.includes(keyword.toLowerCase())
      );
    }
  }
}
</script>

<style scoped>
/* 工作流节点样式 */
.workflow-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.workflow-node {
  position: relative;
}

.workflow-node-bubble {
  padding: 12px 16px;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  background: #f8fafc;
}

.node-start {
  border-color: #10b981;
  background: #ecfdf5;
}

.node-end {
  border-color: #6366f1;
  background: #eef2ff;
}

.node-search {
  border-color: #f59e0b;
  background: #fefbf0;
}

.node-model {
  border-color: #ef4444;
  background: #fef2f2;
}

.node-default {
  border-color: #6b7280;
  background: #f9fafb;
}

.node-active {
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}

.node-completed {
  opacity: 0.8;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.node-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.node-info {
  flex: 1;
}

.node-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.workflow-title {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.node-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.completed-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-emoji {
  font-size: 16px;
  line-height: 1;
  display: block;
}

/* 节点连接线 */
.workflow-node:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -12px;
  width: 2px;
  height: 12px;
  background: linear-gradient(to bottom, #e2e8f0, transparent);
  transform: translateX(-50%);
  z-index: 1;
}

/* 节点脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.node-active {
  animation: pulse 2s ease-in-out infinite;
}

/* 加载动画 */
.loading-spinner svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 完成图标动画 */
.completed-icon svg {
  animation: checkmark 0.6s ease-in-out;
}

@keyframes checkmark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 图标动画 */
.icon-emoji {
  animation: iconBounce 0.6s ease-out;
}

@keyframes iconBounce {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 节点悬停效果 */
.workflow-node-bubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 渐变背景 - 优化各种节点类型的配色 */
.node-start {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #10b981;
}

.node-end {
  background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
  border-color: #6366f1;
}

.node-search {
  background: linear-gradient(135deg, #fefbf0 0%, #fef3c7 100%);
  border-color: #f59e0b;
}

.node-model {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border-color: #ef4444;
}

.node-default {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-color: #6b7280;
}

/* 为新增的节点类型添加样式 */
.node-llm {
  background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
  border-color: #3b82f6;
}

.node-retriever {
  background: linear-gradient(135deg, #f7fee7 0%, #ecfccb 100%);
  border-color: #84cc16;
}

.node-workflow {
  background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%);
  border-color: #a855f7;
}

.node-tool {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
  border-color: #ea580c;
}

.node-code {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #475569;
}

.node-database {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
  border-color: #14b8a6;
}

.node-knowledge {
  background: linear-gradient(135deg, #fefbf0 0%, #fef3c7 100%);
  border-color: #f59e0b;
}

/* 工作流收起/展开样式 */
.workflow-collapsed {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}

.workflow-toggle-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 20px;
  color: #64748b;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.workflow-toggle-btn:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #94a3b8;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.workflow-toggle-btn.collapsed {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
  border-color: #c7d2fe;
  color: #6366f1;
}

.workflow-toggle-btn.collapsed:hover {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  border-color: #a5b4fc;
  color: #4f46e5;
}

.workflow-toggle-btn.expanded {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #86efac;
  color: #059669;
  font-size: 12px;
  padding: 6px 12px;
}

.workflow-toggle-btn.expanded:hover {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #6ee7b7;
  color: #047857;
}

.toggle-text {
  font-weight: 500;
}

.toggle-arrow {
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.workflow-toggle-btn.collapsed .toggle-arrow {
  transform: rotate(0deg);
}

.workflow-toggle-btn.expanded .toggle-arrow {
  transform: rotate(180deg);
}

.workflow-expanded {
  margin-bottom: 16px;
  animation: slideDown 0.3s ease-out;
}

.workflow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;
}

.workflow-header-text {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.workflow-nodes {
  /* 移除最大高度限制和滚动条，让节点自然展示 */
}

/* 工作流节点动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* 知识库节点样式 */
.knowledge-node-content {
  margin-top: 12px;
  animation: fadeInUp 0.3s ease-out;
}

/* 知识库收起状态 */
.knowledge-collapsed {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 知识库展开状态 */
.knowledge-expanded {
  padding: 12px;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  border-radius: 8px;
  border: 1px solid #bbf7d0;
  animation: slideDown 0.3s ease-out;
}

/* 知识库切换按钮样式 */
.knowledge-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 20px;
  color: #64748b;
  font-size: 12px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.knowledge-toggle-btn:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #94a3b8;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.knowledge-toggle-btn.collapsed {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: #86efac;
  color: #16a34a;
}

.knowledge-toggle-btn.collapsed:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-color: #4ade80;
  color: #15803d;
}

.knowledge-toggle-btn.expanded {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #86efac;
  color: #059669;
  font-size: 12px;
  padding: 6px 12px;
}

.knowledge-toggle-btn.expanded:hover {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #6ee7b7;
  color: #047857;
}

.knowledge-toggle-btn .toggle-icon {
  font-size: 16px;
  animation: pulse 2s ease-in-out infinite;
}

.knowledge-toggle-btn .toggle-text {
  white-space: nowrap;
}

.knowledge-toggle-btn .toggle-arrow {
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.knowledge-toggle-btn.collapsed .toggle-arrow {
  transform: rotate(0deg);
}

.knowledge-toggle-btn.expanded .toggle-arrow {
  transform: rotate(180deg);
}

.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(34, 197, 94, 0.2);
}

.knowledge-title {
  font-weight: 600;
  color: #166534;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.knowledge-title::before {
  content: "📚";
  font-size: 16px;
}

.documents-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.document-item {
  padding: 10px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  border: 1px solid rgba(34, 197, 94, 0.2);
  transition: all 0.3s ease;
}

.document-item:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(34, 197, 94, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1);
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.document-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.document-id {
  font-size: 11px;
  font-weight: 600;
  color: #166534;
  background: rgba(34, 197, 94, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
}

.similarity-score {
  font-size: 10px;
  color: #059669;
  background: rgba(5, 150, 105, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.view-content-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 12px;
  color: #166534;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-content-btn:hover {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
  color: #15803d;
  transform: scale(1.05);
}

.document-preview {
  margin-top: 6px;
}

.document-title {
  font-size: 12px;
  font-weight: 600;
  color: #166534;
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.document-excerpt {
  font-size: 11px;
  color: #059669;
  line-height: 1.4;
  margin: 0;
  opacity: 0.8;
}

/* 知识库节点工具提示样式 */
:deep(.knowledge-doc-tooltip) {
  max-width: 500px;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  padding: 12px;
  font-size: 12px;
  color: #374151;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

:deep(.knowledge-doc-tooltip::-webkit-scrollbar) {
  width: 6px;
}

:deep(.knowledge-doc-tooltip::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 3px;
}

:deep(.knowledge-doc-tooltip::-webkit-scrollbar-thumb) {
  background: #cbd5e1;
  border-radius: 3px;
}

:deep(.knowledge-doc-tooltip::-webkit-scrollbar-thumb:hover) {
  background: #94a3b8;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workflow-node-bubble {
    padding: 10px 12px;
  }

  .node-header {
    gap: 8px;
  }

  .node-icon {
    width: 24px;
    height: 24px;
  }

  .icon-emoji {
    font-size: 14px;
  }

  .node-name {
    font-size: 13px;
  }

  .workflow-title {
    font-size: 11px;
  }

  .knowledge-node-content {
    padding: 8px;
  }

  .document-item {
    padding: 8px;
  }

  .knowledge-title {
    font-size: 12px;
  }

  .document-count {
    font-size: 10px;
  }
}
</style>