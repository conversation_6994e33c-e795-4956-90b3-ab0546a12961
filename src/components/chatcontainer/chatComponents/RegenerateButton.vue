<template>
  <button
    v-if="showButton"
    @click="handleRegenerate"
    class="p-1 text-gray-400 rounded-full transition-colors duration-200 hover:text-primary hover:bg-gray-50"
    title="重新生成"
  >
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
      <path d="M21 3v5h-5"/>
      <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
      <path d="M3 21v-5h5"/>
    </svg>
  </button>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
  name: 'RegenerateButton',
  props: {
    // 消息索引
    messageIndex: {
      type: Number,
      required: true
    },
    // 消息列表
    messages: {
      type: Array,
      required: true
    },
    // 是否允许重新生成
    allowRegenerate: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    // 是否显示按钮
    showButton() {
      return this.allowRegenerate
    }
  },
  methods: {
    // 处理重新生成操作
    handleRegenerate() {
      this.regenerateResponse(this.messageIndex)
    },

    // 重新生成响应方法
    regenerateResponse(index) {
      if (!this.allowRegenerate) return

      // 获取当前 AI 消息的上一条用户消息
      let userMessage = null
      for (let i = index - 1; i >= 0; i--) {
        if (this.messages[i].sender === 'user') {
          userMessage = this.messages[i].content
          break
        }
      }

      if (!userMessage) {
        ElMessage.warning('无法重新生成此消息')
        return
      }

      // 发射重新生成事件，传递用户消息内容
      this.$emit('regenerate', userMessage)
    }
  }
}
</script>

<style scoped>
/* 组件样式已通过class内联定义，无需额外样式 */
</style>