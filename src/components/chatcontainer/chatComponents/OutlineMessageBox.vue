<template>
  <div class="outline-message-box">
    <!-- 标题栏 -->
    <div class="outline-header">
      <div class="outline-title">
        <span class="title-icon">📋</span>
        <span class="title-text">{{ title || '青秀山元人机运输外卖项目' }}</span>
      </div>
      <div class="outline-actions">
        <el-button size="small" type="text" @click="toggleExpand">
          <el-icon><Plus v-if="!isExpanded" /><Minus v-else /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 提纲内容 -->
    <div class="outline-content" v-show="isExpanded">
      <div class="outline-tree">
        <OutlineNode
          v-for="(node, index) in parsedOutline"
          :key="index"
          :node="node"
          :level="1"
          @edit-node="handleEditNode"
          @toggle-node="handleToggleNode"
          @delete-node="handleDeleteNode"
        />
      </div>
      
      <!-- 生成内容按钮 -->
      <div class="generate-actions">
        <el-button type="primary" size="default" class="generate-btn" @click="handleGenerateContent">
          <el-icon><Document /></el-icon>
          基于此大纲生成文档
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { Plus, Minus, Document } from '@element-plus/icons-vue'
import OutlineNode from './OutlineNode.vue'

export default {
  name: 'OutlineMessageBox',
  components: {
    Plus,
    Minus,
    Document,
    OutlineNode
  },
  props: {
    content: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isExpanded: true,
      parsedOutline: []
    }
  },
  watch: {
    content: {
      immediate: true,
      handler(newContent) {
        this.parseMarkdownOutline(newContent)
      }
    }
  },
  methods: {
    /**
     * 解析Markdown提纲内容
     */
    parseMarkdownOutline(content) {
      if (!content) return
      
      const lines = content.split('\n').filter(line => line.trim())
      const outline = []
      const stack = [] // 用于跟踪层级关系
      
      lines.forEach(line => {
        const trimmedLine = line.trim()
        
        // 匹配标题格式：# 标题 或 #1 标题 或 ##(1) 标题
        const headerMatch = trimmedLine.match(/^(#{1,6})\s*(\d+\.?)?\s*(\([^)]+\))?\s*(.+)$/)
        
        if (headerMatch) {
          const level = headerMatch[1].length // # 的数量决定层级
          const number = headerMatch[2] || ''
          const subNumber = headerMatch[3] || ''
          const text = headerMatch[4].trim()
          
          const node = {
            id: Date.now() + Math.random(),
            level,
            number: number.replace('.', ''),
            subNumber: subNumber.replace(/[()]/g, ''),
            text,
            children: [],
            isExpanded: true,
            isEditing: false,
            originalText: text
          }
          
          // 根据层级找到正确的父节点
          while (stack.length > 0 && stack[stack.length - 1].level >= level) {
            stack.pop()
          }
          
          if (stack.length === 0) {
            outline.push(node)
          } else {
            stack[stack.length - 1].children.push(node)
          }
          
          stack.push(node)
        } else if (trimmedLine && stack.length > 0) {
          // 非标题行作为内容添加到最后一个节点
          const lastNode = stack[stack.length - 1]
          if (!lastNode.content) {
            lastNode.content = []
          }
          lastNode.content.push(trimmedLine)
        }
      })
      
      this.parsedOutline = outline
    },
    
    /**
     * 切换展开/收起
     */
    toggleExpand() {
      this.isExpanded = !this.isExpanded
    },
    
    /**
     * 处理节点编辑
     */
    handleEditNode(nodeId, newText) {
      this.updateNodeText(this.parsedOutline, nodeId, newText)
      this.$emit('content-changed', this.generateMarkdownFromOutline())
    },
    
    /**
     * 处理节点展开/收起
     */
    handleToggleNode(nodeId) {
      this.toggleNodeExpansion(this.parsedOutline, nodeId)
    },

    /**
     * 处理节点删除
     */
    handleDeleteNode(nodeId) {
      console.log('处理删除节点:', nodeId)
      console.log('删除前的提纲:', JSON.stringify(this.parsedOutline, null, 2))

      const deleted = this.deleteNodeById(this.parsedOutline, nodeId)
      console.log('删除结果:', deleted)
      console.log('删除后的提纲:', JSON.stringify(this.parsedOutline, null, 2))

      if (deleted) {
        // 强制触发响应式更新
        this.$forceUpdate()
        this.$emit('content-changed', this.generateMarkdownFromOutline())
      }
    },
    
    /**
     * 更新节点文本
     */
    updateNodeText(nodes, nodeId, newText) {
      for (const node of nodes) {
        if (node.id === nodeId) {
          node.text = newText
          return true
        }
        if (node.children && this.updateNodeText(node.children, nodeId, newText)) {
          return true
        }
      }
      return false
    },
    
    /**
     * 切换节点展开状态
     */
    toggleNodeExpansion(nodes, nodeId) {
      for (const node of nodes) {
        if (node.id === nodeId) {
          node.isExpanded = !node.isExpanded
          return true
        }
        if (node.children && this.toggleNodeExpansion(node.children, nodeId)) {
          return true
        }
      }
      return false
    },

    /**
     * 根据ID删除节点
     */
    deleteNodeById(nodes, nodeId) {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === nodeId) {
          // 找到目标节点，删除它
          console.log('找到要删除的节点:', nodes[i].text)
          nodes.splice(i, 1)
          return true
        }
        // 递归查找子节点
        if (nodes[i].children && this.deleteNodeById(nodes[i].children, nodeId)) {
          return true
        }
      }
      return false
    },
    
    /**
     * 从解析的提纲生成Markdown
     */
    generateMarkdownFromOutline() {
      const generateNodeMarkdown = (node) => {
        const prefix = '#'.repeat(node.level)
        let line = `${prefix} `
        
        if (node.number) {
          line += `${node.number} `
        }
        if (node.subNumber) {
          line += `(${node.subNumber}) `
        }
        line += node.text
        
        let result = [line]
        
        if (node.content) {
          result = result.concat(node.content)
        }
        
        if (node.children) {
          node.children.forEach(child => {
            result = result.concat(generateNodeMarkdown(child))
          })
        }
        
        return result
      }
      
      let markdown = []
      this.parsedOutline.forEach(node => {
        markdown = markdown.concat(generateNodeMarkdown(node))
      })
      
      return markdown.join('\n')
    },
    
    /**
     * 处理生成内容
     */
    handleGenerateContent() {
      const markdownContent = this.generateMarkdownFromOutline()
      this.$emit('generate-content', markdownContent)
    }
  }
}
</script>

<style scoped>
.outline-message-box {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.outline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.outline-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 16px;
}

.title-text {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.outline-content {
  padding: 16px;
}

.outline-tree {
  margin-bottom: 16px;
}

.generate-actions {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.generate-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 24px;
  border-radius: 8px;
  font-weight: 500;
}
</style>
