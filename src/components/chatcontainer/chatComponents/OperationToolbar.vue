<template>
  <div
    class="flex gap-2 items-center p-1 px-2 bg-white rounded-full border border-gray-100 shadow-sm opacity-0 transition-all duration-200 group-hover:opacity-100"
    :class="positionClass"
  >
    <!-- 复制按钮 - 所有消息都有 -->
    <CopyButton :content="message.content" />

    <!-- AI消息特有的按钮 -->
    <template v-if="messageType === 'ai'">
      <!-- 编辑按钮 - 仅对公文公告工作流的可编辑消息显示 -->
      <button
        v-if="showEditButton"
        @click="handleEditMessage"
        class="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors duration-200"
        title="编辑内容"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
          <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
        </svg>
      </button>

      <!-- 重新生成按钮 -->
      <RegenerateButton
        :message-index="messageIndex"
        :messages="messages"
        :allow-regenerate="allowRegenerate"
        @regenerate="handleRegenerateResponse"
      />

      <!-- 下载按钮 -->
      <DownloadButton :message="message" />
    </template>
  </div>
</template>

<script>
import CopyButton from './CopyButton.vue'
import RegenerateButton from './RegenerateButton.vue'
import DownloadButton from './DownloadButton.vue'

export default {
  name: 'OperationToolbar',
  components: {
    CopyButton,
    RegenerateButton,
    DownloadButton
  },
  props: {
    // 消息对象
    message: {
      type: Object,
      required: true
    },
    // 消息索引（用于重新生成功能）
    messageIndex: {
      type: Number,
      required: true
    },
    // 消息类型：'ai' 或 'user'
    messageType: {
      type: String,
      required: true,
      validator: (value) => ['ai', 'user'].includes(value)
    },
    // 是否允许重新生成（仅对AI消息有效）
    allowRegenerate: {
      type: Boolean,
      default: true
    },
    // 消息列表（用于重新生成功能）
    messages: {
      type: Array,
      required: true
    },
    // 工作流ID（用于判断是否显示编辑按钮）
    workflowId: {
      type: String,
      default: ''
    }
  },
  computed: {
    // 根据消息类型计算位置样式
    positionClass() {
      if (this.messageType === 'ai') {
        return 'absolute -bottom-8 left-15'
      } else {
        return 'absolute right-0 -bottom-8'
      }
    },
    // 是否显示编辑按钮
    showEditButton() {
      return this.messageType === 'ai' &&
             this.workflowId === '1947848702138769410' &&
             this.message.isEditable
    }
  },
  methods: {
    // 处理重新生成响应（从RegenerateButton传递过来）
    handleRegenerateResponse(userMessage) {
      this.$emit('regenerate-response', userMessage)
    },
    // 处理编辑消息
    handleEditMessage() {
      this.$emit('edit-message', this.message, this.messageIndex)
    }
  }
}
</script>

<style scoped>
/* 组件样式已通过class内联定义，无需额外样式 */
</style>