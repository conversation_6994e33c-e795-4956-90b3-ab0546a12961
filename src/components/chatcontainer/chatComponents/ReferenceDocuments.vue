<template>
  <!-- 参考文档引用展示 -->
  <div v-if="documents && documents.length > 0" class="pt-2 mt-4 border-t border-gray-200">
    <p class="text-sm text-gray-500">参考文档：</p>
    <ul class="mt-1 space-y-1">
      <li v-for="(docGroup, i) in getGroupedDocuments(documents)" :key="i" class="flex items-center justify-between text-sm text-primary">
        <div class="flex items-center">
          <template v-if="getDocumentIconType(docGroup.name) === 'pdf'">
            <img src="@/assets/image/PDF.png" alt="PDF" class="mr-1 w-4 h-4 text-red-500" />
          </template>
          <template v-else-if="getDocumentIconType(docGroup.name) === 'word'">
            <img src="@/assets/image/wps.png" alt="Word" class="mr-1 w-4 h-4 text-blue-600" />
          </template>
          <template v-else>
            <img src="@/assets/image/txt.png" alt="其他" class="mr-1 w-4 h-4 text-primary" />
          </template>
          <span class="cursor-pointer hover:underline" @click="downloadDocument(docGroup)">{{ docGroup.name }}</span>
        </div>

        <!-- 分块列表下拉 -->
        <div class="relative" v-if="docGroup.chunks.length > 1">
          <el-dropdown
            trigger="click"
            placement="bottom-end"
            :popper-options="{ strategy: 'fixed' }"
          >
            <span class="cursor-pointer text-xs px-2 py-1 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors">
              {{ docGroup.chunks.length }} 个分块
              <svg class="inline w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </span>
            <template #dropdown>
              <el-dropdown-menu class="chunk-dropdown-menu">
                <el-dropdown-item
                  v-for="(chunk, chunkIndex) in docGroup.chunks"
                  :key="chunkIndex"
                  class="chunk-dropdown-item"
                >
                  <el-popover
                    popper-class="doc-tooltip"
                    placement="left"
                    :content="chunk.content || '暂无分块内容'"
                    :disabled="!chunk.content"
                    :enterable="true"
                    :show-after="300"
                    width="25vw"
                    trigger="hover"
                  >
                    <template #reference>
                      <span class="block w-full text-left">
                        分块 {{ chunkIndex + 1 }}
                        <span class="text-xs text-gray-400 block mt-1">
                          {{ chunk.content ? chunk.content.substring(0, 30) + '...' : '暂无内容' }}
                        </span>
                      </span>
                    </template>
                  </el-popover>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 单个分块时直接显示内容 -->
        <div v-else-if="docGroup.chunks.length === 1" class="flex items-center">
          <el-popover
            popper-class="doc-tooltip"
            placement="top"
            :content="docGroup.chunks[0].content || '暂无文档内容'"
            :disabled="!docGroup.chunks[0].content"
            :enterable="true"
            :show-after="500"
            width="25vw"
            trigger="hover"
          >
            <template #reference>
              <span class="cursor-pointer text-xs px-2 py-1 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors">
                查看内容
              </span>
            </template>
          </el-popover>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { downloadKnowledge } from '@/api/knowledge'
import { ElMessage } from 'element-plus'

export default {
  name: 'ReferenceDocuments',
  props: {
    documents: {
      type: Array,
      default: () => []
    }
  },
  emits: ['download-document'],
  methods: {
    /**
     * 获取文档图标类型
     */
    getDocumentIconType(fileName) {
      if (!fileName) return 'text'

      const extension = fileName.split('.').pop().toLowerCase()

      if (extension === 'pdf') {
        return 'pdf'
      } else if (['doc', 'docx', 'docs'].includes(extension)) {
        return 'word'
      } else {
        return 'text'
      }
    },

    /**
     * 将文档按名称分组，合并同一文档的多个分块
     */
    getGroupedDocuments(documents) {
      if (!documents || !Array.isArray(documents)) {
        return [];
      }

      const groupedMap = new Map();

      documents.forEach(doc => {
        const docName = doc.document_name || '未知文档';

        if (!groupedMap.has(docName)) {
          groupedMap.set(docName, {
            name: docName,
            chunks: []
          });
        }

        groupedMap.get(docName).chunks.push({
          content: doc.content,
          // 保留原始文档的其他属性
          ...doc
        });
      });

      // 转换为数组并按文档名排序
      return Array.from(groupedMap.values()).sort((a, b) => a.name.localeCompare(b.name));
    },

    /**
     * 下载文档
     */
    async downloadDocument(docGroup) {
      try {
        // 取第一个分块的信息来获取document_id和dataset_id
        const firstChunk = docGroup.chunks[0];

        if (!firstChunk.document_id || !firstChunk.dataset_id) {
          ElMessage.error('缺少必要的文档信息，无法下载');
          return;
        }

        ElMessage.info('正在下载文档...');

        // 调用下载API
        const response = await downloadKnowledge({
          id: firstChunk.document_id,
          datasetId: firstChunk.dataset_id
        });

        // 创建下载链接
        const blob = new Blob([response], { type: 'application/octet-stream' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = docGroup.name; // 使用文档名称作为下载文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        ElMessage.success('文档下载成功');

        // 发射下载事件给父组件
        this.$emit('download-document', docGroup);
      } catch (error) {
        console.error('下载文档失败:', error);
        ElMessage.error('下载文档失败，请稍后重试');
      }
    }
  }
}
</script>

<style scoped>
/* 可以添加组件特定的样式 */
.chunk-dropdown-menu {
  max-height: 200px;
  overflow-y: auto;
}

.chunk-dropdown-item {
  padding: 8px 12px;
}

.doc-tooltip {
  max-width: 25vw;
  word-wrap: break-word;
}
</style>