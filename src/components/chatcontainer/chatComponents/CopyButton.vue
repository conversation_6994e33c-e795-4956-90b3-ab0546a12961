<template>
  <button
    @click="handleCopy"
    class="p-1 text-gray-400 rounded-full transition-colors duration-200 hover:text-primary hover:bg-gray-50"
    title="复制消息"
  >
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
      <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
    </svg>
  </button>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
  name: 'CopyButton',
  props: {
    // 要复制的内容
    content: {
      type: String,
      required: true
    }
  },
  methods: {
    // 处理复制操作
    handleCopy() {
      this.copyToClipboard(this.content)
    },

    // 复制到剪贴板方法
    copyToClipboard(content) {
      const tempElement = document.createElement('div')
      tempElement.innerHTML = content
      const textContent = tempElement.textContent || tempElement.innerText

      navigator.clipboard.writeText(textContent).then(() => {
        ElMessage({
          message: '复制成功',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        ElMessage({
          message: '复制失败，请手动复制',
          type: 'error',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style scoped>
/* 组件样式已通过class内联定义，无需额外样式 */
</style>