<template>
  <!-- 这个组件主要用于提供预览相关的方法，不需要模板 -->
  <div></div>
</template>

<script>
import { ElMessage, ElImageViewer } from 'element-plus'
import { createVNode, render } from 'vue'

export default {
  name: 'Preview',
  props: {
    // 基础URL配置
    baseURL: {
      type: String,
      default: () => import.meta.env.VITE_API_BASE_URL || ''
    }
  },
  methods: {
    /**
     * 获取文件类型和扩展名
     */
    getFileType(filename) {
      const extension = filename.split('.').pop().toUpperCase()

      const typeMap = {
        DOCUMENT: ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
        IMAGE: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
        AUDIO: ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
        VIDEO: ['MP4', 'MOV', 'MPEG', 'MPGA']
      }

      for (const [type, extensions] of Object.entries(typeMap)) {
        if (extensions.includes(extension)) {
          return {
            type,
            extension
          }
        }
      }

      return {
        type: 'CUSTOM',
        extension
      }
    },

    /**
     * 获取文件图标
     */
    getFileIcon(fileType) {
      const icons = {
        'DOCUMENT': '📄',
        'IMAGE': '🖼️',
        'AUDIO': '🎵',
        'VIDEO': '🎥',
        'CUSTOM': '📎'
      }
      return icons[fileType] || '📎'
    },

    /**
     * 获取预设问题图片URL
     */
    getQuestionImage(fileId) {
      if (!fileId || fileId.trim() === '') return ''
      return `${this.baseURL}/v1/public/file/get?fileId=${fileId}`
    },

    /**
     * 获取文件预览URL
     */
    getFilePreviewUrl(fileId) {
      if (!fileId || fileId.trim() === '') return ''
      return `${this.baseURL}/v1/public/file/get?fileId=${fileId}`
    },

    /**
     * 创建图片查看器
     */
    createImageViewer(imageUrl) {
      const container = document.createElement('div')
      const vnode = createVNode(ElImageViewer, {
        urlList: [imageUrl],
        onClose: () => {
          render(null, container)
          if (document.body.contains(container)) {
            document.body.removeChild(container)
          }
        }
      })

      document.body.appendChild(container)
      render(vnode, container)
    },

    /**
     * 预览文件 - 通用文件预览方法
     */
    previewFile(file) {
      // 图片预览
      if (file.fileType === 'IMAGE') {
        const img = new Image()
        img.src = URL.createObjectURL(file.raw)
        // 简单的图片预览
        window.open(img.src, '_blank')
      }
      // PDF预览
      else if (file.fileType === 'DOCUMENT' && ['PDF'].includes(file.extension)) {
        window.open(URL.createObjectURL(file.raw))
      }
      // 文本文件预览
      else if (file.fileType === 'DOCUMENT' && ['TXT', 'MD', 'HTML', 'CSV'].includes(file.extension)) {
        const reader = new FileReader()
        reader.onload = (e) => {
          console.log('文件内容预览:', e.target.result)
          ElMessage.info('文件内容已在控制台输出')
        }
        reader.readAsText(file.raw)
      }
      // 音频预览
      else if (file.fileType === 'AUDIO') {
        const audio = new Audio(URL.createObjectURL(file.raw))
        audio.play()
      }
      // 视频预览
      else if (file.fileType === 'VIDEO') {
        window.open(URL.createObjectURL(file.raw), '_blank')
      }
      else {
        ElMessage.info('该文件类型暂不支持预览')
      }
    },

    /**
     * 预览预设问题图片
     */
    previewQuestionImage(fileId) {
      if (!fileId || fileId.trim() === '') return

      const imageUrl = this.getQuestionImage(fileId)
      this.createImageViewer(imageUrl)
    },

    /**
     * 预览消息中的文件
     */
    previewMessageFile(fileId, fileType) {
      if (!fileId || fileId.trim() === '') return

      // 只处理图片类型的文件
      if (fileType === 'IMAGE' || fileType === 'image') {
        const imageUrl = this.getQuestionImage(fileId)
        this.createImageViewer(imageUrl)
      }
    },

    /**
     * 预览检测报告PDF文件
     */
    previewReportFile(fileName) {
      try {
        // 构造PDF文件的URL
        const pdfUrl = `/${fileName}`

        // 在新窗口中打开PDF文件
        const newWindow = window.open(pdfUrl, '_blank')

        if (!newWindow) {
          // 如果浏览器阻止了弹窗，提示用户
          ElMessage.warning('请允许浏览器弹窗以预览PDF文件')
        } else {
          console.log('在新页面打开PDF文件:', fileName)
        }
      } catch (error) {
        console.error('预览PDF文件失败:', error)
        ElMessage.error('无法预览PDF文件')
      }
    },

    /**
     * 预览文件通过fileId
     */
    previewFileById(fileId, fileName) {
      if (!fileId || fileId.trim() === '') return

      try {
        const fileUrl = this.getFilePreviewUrl(fileId)
        // 在新窗口中打开文件
        const newWindow = window.open(fileUrl, '_blank')

        if (!newWindow) {
          // 如果弹窗被阻止，给用户提示
          ElMessage.warning('弹窗被阻止，请允许弹窗或手动复制链接访问')
          console.log('文件预览链接：', fileUrl)
        }
      } catch (error) {
        console.error('预览文件失败：', error)
        ElMessage.error('预览文件失败，请稍后重试')
      }
    },

    /**
     * 转义正则表达式中的特殊字符
     */
    escapeRegExp(string) {
      if (typeof string !== 'string') {
        return ''
      }
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    },

    /**
     * 渲染用户消息内容，将文件名转换为可点击链接
     */
    renderUserMessageContent(message) {
      if (!message.content) return ''

      let content = message.content

      // 如果消息中有文件信息，处理文件名点击
      if (message.files && message.files.length > 0) {
        message.files.forEach(file => {
          // 检查文件对象是否有效
          if (!file || !file.fileId || !file.fileName || !file.fileType) {
            console.warn('文件信息不完整:', file)
            return
          }

          // 创建文件图标和名称的HTML
          const fileIcon = this.getFileIcon(file.fileType)
          const originalText = `${fileIcon} ${file.fileName}`

          // 只对图片类型的文件创建可点击链接
          if (file.fileType === 'IMAGE' || file.fileType === 'image') {
            // 转义文件名中的特殊字符以避免HTML注入
            const escapedFileName = file.fileName.replace(/'/g, '&#39;').replace(/"/g, '&quot;')
            const clickableLink = `<span class="file-link cursor-pointer hover:underline" onclick="window.chatContainerPro.previewMessageFile('${file.fileId}', '${file.fileType}')" title="点击预览图片">${fileIcon} ${escapedFileName}</span>`

            // 使用正则表达式进行更精确的替换
            const regex = new RegExp(`${this.escapeRegExp(fileIcon)}\\s+${this.escapeRegExp(file.fileName)}`, 'g')
            content = content.replace(regex, clickableLink)
          }
        })
      }

      // 转换换行符为HTML
      return content.replace(/\n/g, '<br>')
    },

    /**
     * 获取不带扩展名的文件名
     */
    getFileNameWithoutExtension(fileName) {
      if (!fileName) return ''
      const lastDotIndex = fileName.lastIndexOf('.')
      return lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName
    },

    /**
     * 获取文件扩展名
     */
    getFileExtension(fileName) {
      if (!fileName) return ''
      const lastDotIndex = fileName.lastIndexOf('.')
      return lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1) : ''
    },

    /**
     * 截断文件名以保证显示美观
     */
    truncateFileName(fileName, maxLength = 12) {
      if (!fileName) return ''

      // 如果文件名长度小于等于最大长度，直接返回
      if (fileName.length <= maxLength) {
        return fileName
      }

      // 获取文件扩展名
      const dotIndex = fileName.lastIndexOf('.')
      const extension = dotIndex !== -1 ? fileName.substring(dotIndex) : ''
      const nameWithoutExt = dotIndex !== -1 ? fileName.substring(0, dotIndex) : fileName

      // 计算可用于显示文件名的长度（预留扩展名和省略号的空间）
      const availableLength = maxLength - extension.length - 3 // 3 是省略号的长度

      if (availableLength <= 0) {
        // 如果可用长度不够，只显示省略号和扩展名
        return '...' + extension
      }

      // 截断文件名并添加省略号
      return nameWithoutExt.substring(0, availableLength) + '...' + extension
    }
  }
}
</script>

<style scoped>
/* 预览相关的样式 */
.file-link {
  color: #1890ff;
  text-decoration: none;
  cursor: pointer;
}

.file-link:hover {
  text-decoration: underline;
  color: #40a9ff;
}

/* 预设问题图片样式 */
.question-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.question-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
</style>
