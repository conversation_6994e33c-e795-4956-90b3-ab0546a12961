<template>
  <div class="chat-input-container">
    <!-- 文件预览区域 -->
    <div class="file-preview-container">
      <div v-if="uploadedFiles.length > 0" class="flex flex-wrap gap-2 mb-3 uploaded-files-preview">
        <div 
          v-for="(file, index) in uploadedFiles" 
          :key="file.fileId"
          class="flex items-center p-2 bg-gray-50 rounded-lg file-preview-item"
        >
          <span class="mr-2">{{ getFileIcon(file.fileType) }}</span>
          <span class="text-sm text-gray-600 cursor-pointer hover:text-primary" @click="$emit('preview-file', file)">
            {{ file.fileName }}
          </span>
          <button 
            class="ml-2 text-gray-400 hover:text-red-500"
            @click="$emit('remove-file', index)"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 预设问题区域 -->
    <div v-if="suggestedQuestions.length > 0" class="suggested-questions-container">
      <div class="suggested-questions-wrapper">
        <div 
          v-for="(question, index) in suggestedQuestions" 
          :key="index"
          class="suggested-question-item"
          @click="$emit('select-question', question, index)"
        >
          <!-- 普通预设问题的图片显示 -->
          <img 
            v-if="question.fileId && question.fileId.trim()"
            :src="getQuestionImage(question.fileId)" 
            :alt="`预设问题图片${index + 1}`"
            class="question-image"
            title="点击放大图片"
            @click.stop="$emit('preview-question-image', question.fileId)"
          />
          
          <!-- 检测报告分析智能体的文件信息显示 -->
          <div 
            v-if="isReportAnalysisAgent && question.fileName" 
            class="report-file-info"
            @click.stop="$emit('preview-report-file', question.fileName)"
            title="点击预览PDF文件"
          >
            <div class="file-icon-container">
              <img src="@/assets/image/PDF.png" alt="PDF" class="file-icon" />
            </div>
            <div class="file-details">
              <div class="file-name">{{ getFileNameWithoutExtension(question.fileName) }}</div>
              <div class="file-extension">.{{ getFileExtension(question.fileName) }}</div>
            </div>
            <div class="preview-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </div>
          </div>
          
          <span class="question-text" title="点击发送消息">{{ question.content || question }}</span>
        </div>
      </div>
    </div>

    <!-- 普通输入区域 -->
    <div class="input-area">
      <textarea
        :value="userInput"
        @input="$emit('update:userInput', $event.target.value)"
        placeholder="你可以问我任何问题，或说出我如何帮助你..."
        class="p-2 w-full text-gray-700 align-top outline-none resize-none input-textarea"
        @keyup.enter.prevent="$emit('handle-enter', $event)"
        rows="3"
      ></textarea>
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar">
      <div class="flex justify-between items-center">
        <div class="flex gap-2 items-center m-4">
          <!-- 深度思考按钮 -->
          <button 
            @click="$emit('toggle-mode', 'deep')" 
            class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
            :class="[
              activeModes.includes('deep')
                ? 'bg-primary text-white' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            ]"
          >
            <div class="flex gap-1 items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12z"></path><path d="M12 8v8"></path><path d="M8 12h8"></path></svg>
              深度思考
            </div>
          </button>
          
          <!-- 联网搜索按钮 -->
          <button 
            v-if="showSearchMode"
            @click="$emit('toggle-mode', 'search')" 
            class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
            :class="[
              activeModes.includes('search')
                ? 'bg-primary text-white' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            ]"
          >
            <div class="flex gap-1 items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
              联网搜索
            </div>
          </button>
          
          <!-- 知识库选择按钮 -->
          <button 
            v-if="showKnowledgeSelectionButton"
            @click="$emit('show-knowledge-selection')" 
            class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
            :class="[
              selectedKnowledgeList.length > 0
                ? 'bg-primary text-white' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            ]"
          >
            <div class="flex gap-1 items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/><path d="M16 8V6H8v2"/><path d="M16 12V10H8v2"/><path d="M16 16V14H8v2"/></svg>
              知识库选择
              <span v-if="selectedKnowledgeList.length > 0" class="ml-1 px-1.5 py-0.5 text-xs bg-white bg-opacity-30 rounded-full">
                {{ selectedKnowledgeList.length }}
              </span>
            </div>
          </button>
          
          <div class="px-2 text-xs text-gray-400">内容由 AI 生成，请仔细甄别</div>
        </div>
        <div class="flex gap-2 items-center m-4 text-gray-500 func-container">
          <!-- 文件上传按钮 -->
          <el-upload
            v-if="showFileUpload"
            class="upload-button"
            :action="`/api/v1/public/chat/uploadFile`"
            :data="uploadData"
            :show-file-list="false"
            :on-success="(response, file) => $emit('upload-success', response, file)"
            :on-error="(error) => $emit('upload-error', error)"
            :before-upload="(file) => $emit('before-upload', file)"
          >
            <button class="p-2 rounded-full hover:bg-gray-100">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-paperclip"><path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"/></svg>
            </button>
          </el-upload>
          
          <button 
            @click="$emit('send-message')" 
            class="p-2 text-white rounded-full bg-primary hover:bg-primary-middle"
            :disabled="isLoading"
          >
            <svg v-if="!isLoading" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="animate-spin"><path d="M21 12a9 9 0 1 1-6.219-8.56"/></svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatInput',
  props: {
    userInput: {
      type: String,
      default: ''
    },
    uploadedFiles: {
      type: Array,
      default: () => []
    },
    suggestedQuestions: {
      type: Array,
      default: () => []
    },
    activeModes: {
      type: Array,
      default: () => []
    },
    showSearchMode: {
      type: Boolean,
      default: true
    },
    showKnowledgeSelectionButton: {
      type: Boolean,
      default: true
    },
    showFileUpload: {
      type: Boolean,
      default: true
    },
    selectedKnowledgeList: {
      type: Array,
      default: () => []
    },
    uploadData: {
      type: Object,
      default: () => ({})
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    isReportAnalysisAgent: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'update:userInput',
    'preview-file',
    'remove-file',
    'select-question',
    'preview-question-image',
    'preview-report-file',
    'handle-enter',
    'toggle-mode',
    'show-knowledge-selection',
    'upload-success',
    'upload-error',
    'before-upload',
    'send-message'
  ],
  methods: {
    getFileIcon(fileType) {
      const icons = {
        'DOCUMENT': '📄',
        'IMAGE': '🖼️',
        'AUDIO': '🎵',
        'VIDEO': '🎥',
        'CUSTOM': '📎'
      }
      return icons[fileType] || '📎'
    },
    getQuestionImage(fileId) {
      if (!fileId || fileId.trim() === '') return '';
      const baseURL = import.meta.env.VITE_API_BASE_URL || '';
      return `${baseURL}/v1/public/file/get?fileId=${fileId}`;
    },
    getFileNameWithoutExtension(fileName) {
      if (!fileName) return '';
      const lastDotIndex = fileName.lastIndexOf('.');
      return lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;
    },
    getFileExtension(fileName) {
      if (!fileName) return '';
      const lastDotIndex = fileName.lastIndexOf('.');
      return lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1) : '';
    }
  }
}
</script>

<style scoped>
.chat-input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-area {
  flex: 1;
  min-height: 80px;
}

.input-textarea {
  height: 100%;
  min-height: 60px;
}

.bottom-toolbar {
  margin-top: -2rem;
  flex-shrink: 0;
  min-height: 60px;
}

/* 文件预览相关样式 */
.file-preview-container {
  max-height: 80px;
  overflow-y: auto;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.uploaded-files-preview {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.file-preview-item {
  display: inline-flex;
  align-items: center;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  margin: 2px;
  transition: all 0.3s ease;
}

.file-preview-item:hover {
  background-color: #e5e7eb;
}

.file-preview-item button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.file-preview-item:hover button {
  opacity: 1;
}

/* 预设问题区域样式 */
.suggested-questions-container {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.suggested-questions-wrapper {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 4px 0;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.suggested-questions-wrapper::-webkit-scrollbar {
  height: 6px;
}

.suggested-questions-wrapper::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.suggested-questions-wrapper::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.suggested-questions-wrapper::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.suggested-question-item {
  flex-shrink: 0;
  padding: 12px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  font-size: 14px;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  user-select: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 180px;
}

.suggested-question-item:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.suggested-question-item:active {
  transform: translateY(0);
}

/* 预设问题图片样式 */
.question-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.question-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 预设问题文本样式 */
.question-text {
  text-align: center;
  font-weight: 500;
  line-height: 1.4;
  white-space: normal;
  word-break: break-word;
}

.report-file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.report-file-info:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-details {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #374151;
  font-size: 12px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-extension {
  font-size: 10px;
  color: #6b7280;
  margin-top: 1px;
}

.preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  transition: all 0.3s ease;
  margin-left: auto;
}

.file-icon-container {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.file-icon {
  width: 100%;
  height: 100%;
}

/* 上传按钮样式 */
.upload-button {
  display: inline-block;
  position: relative;
}

.upload-button :deep(.el-upload) {
  display: block;
}

.upload-button :deep(.el-upload-list) {
  display: none;
}
</style>
