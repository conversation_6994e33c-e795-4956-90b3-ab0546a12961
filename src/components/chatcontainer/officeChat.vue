<template>
  <div class="office-chat-container">
    <!-- 公文办公区域 -->
    <div class="office-area">
      <el-form :model="officeFormData" class="office-form" label-position="top">
        <!-- 第一行：写作类型 + 标题 -->
        <el-row :gutter="12" class="office-row">
          <el-col :span="5">
            <el-form-item label="写作类型" class="compact-form-item">
              <el-select 
                :model-value="officeFormData.subjectContent" 
                @update:model-value="updateFormField('subjectContent', $event)"
                placeholder="请选择" 
                size="small" 
                class="compact-select"
              >
                <el-option label="公告" value="公告" />
                <el-option label="通知" value="通知" />
                <el-option label="通报" value="通报" />
                <el-option label="演讲稿" value="演讲稿" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="19">
            <el-form-item label="标题" class="compact-form-item">
              <el-input 
                :model-value="officeFormData.title" 
                @update:model-value="updateFormField('title', $event)"
                placeholder="输入标题" 
                size="small" 
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 第二行：发文字号 + 发文单位 + 主送机关 -->
        <el-row :gutter="12" class="office-row">
          <el-col :span="8">
            <el-form-item label="发文字号" class="compact-form-item">
              <el-input 
                :model-value="officeFormData.documentNumber" 
                @update:model-value="updateFormField('documentNumber', $event)"
                placeholder="输入发文字号" 
                size="small" 
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发文单位" class="compact-form-item">
              <el-input 
                :model-value="officeFormData.issuingUnit" 
                @update:model-value="updateFormField('issuingUnit', $event)"
                placeholder="输入发文单位" 
                size="small" 
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="主送机关" class="compact-form-item">
              <el-input 
                :model-value="officeFormData.toPrimary" 
                @update:model-value="updateFormField('toPrimary', $event)"
                placeholder="输入主送单位" 
                size="small" 
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 第三行：日期 + 文件 -->
        <el-row :gutter="12" class="office-row">
          <el-col :span="6">
            <el-form-item label="成文日期" class="compact-form-item">
              <el-date-picker 
                :model-value="officeFormData.createDate" 
                @update:model-value="updateFormField('createDate', $event)"
                type="date" 
                placeholder="选择日期" 
                size="small"
                style="width: 100%;" 
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD" 
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发布日期" class="compact-form-item">
              <el-date-picker 
                :model-value="officeFormData.releaseDate" 
                @update:model-value="updateFormField('releaseDate', $event)"
                type="date" 
                placeholder="选择日期" 
                size="small"
                style="width: 100%;" 
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD" 
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="格式参考文档" class="compact-form-item compact-upload">
              <div class="file-upload-wrapper">
                <el-tag
                  v-for="file in officeFormData.referenceTemplate"
                  class="file-tag compact-tag clickable-file-tag"
                  :key="file.fileId"
                  @close="$emit('handle-tag-template-delete', file)"
                  @click.stop="$emit('preview-file', file.fileId, file.fileName)"
                  closable
                  size="small"
                  title="点击预览文件"
                >
                  {{ file.fileName }}
                </el-tag>
                <el-upload
                  v-if="officeFormData.referenceTemplate.length === 0"
                  action="/api/v1/public/file/upload"
                  :data="uploadData"
                  :on-success="(response, file) => $emit('template-upload-success', response, file)"
                  :on-error="(error) => $emit('upload-error', error)"
                  :before-upload="(file) => $emit('before-upload', file)"
                  :file-list="officeFormData.referenceTemplate"
                  :limit="1"
                  list-type="text"
                  :show-file-list="false"
                  class="compact-upload-btn"
                >
                  <el-button size="small" type="primary" text class="upload-text-btn">选择文件</el-button>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="内容依据文档" class="compact-form-item compact-upload">
              <div class="file-upload-wrapper">
                <el-tag
                  v-for="file in officeFormData.referenceMaterials"
                  class="file-tag compact-tag reference-file-tag clickable-file-tag"
                  :key="file.fileId"
                  @close="$emit('handle-tag-reference-delete', file)"
                  @click.stop="$emit('preview-file', file.fileId, file.fileName)"
                  closable
                  size="small"
                  :title="`${file.fileName} - 点击预览文件`"
                >
                  {{ truncateFileName(file.fileName, 12) }}
                </el-tag>
                <el-upload
                  v-if="officeFormData.referenceMaterials.length < 1"
                  action="/api/v1/public/file/upload"
                  :data="uploadData"
                  :on-success="(response, file) => $emit('reference-upload-success', response, file)"
                  :on-error="(error) => $emit('upload-error', error)"
                  :before-upload="(file) => $emit('before-upload', file)"
                  :file-list="[]"
                  :limit="1"
                  :show-file-list="false"
                  class="compact-upload-btn"
                >
                  <el-button size="small" type="primary" text class="upload-text-btn">选择文件</el-button>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 写作要求输入区域 -->
    <div class="input-area">
      <textarea
        :value="officeFormData.contentRequirements"
        @input="updateFormField('contentRequirements', $event.target.value)"
        placeholder="请输入写作要求"
        class="p-2 w-full text-gray-700 align-top outline-none resize-none input-textarea office-textarea"
        @keyup.enter.prevent="$emit('handle-enter', $event)"
        rows="2"
      ></textarea>
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar">
      <div class="flex justify-between items-center">
        <div class="flex gap-2 items-center m-4">
          <div class="px-2 text-xs text-gray-400">内容由 AI 生成，请仔细甄别</div>
        </div>
        <div class="flex gap-2 items-center m-4 text-gray-500 func-container">
          <!-- 发送按钮 -->
          <button
            @click="$emit('send-office-message')"
            class="p-2 text-white rounded-full bg-primary hover:bg-primary-middle"
            :disabled="isLoading"
          >
            <svg v-if="!isLoading" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="animate-spin"><path d="M21 12a9 9 0 1 1-6.219-8.56"/></svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
  name: 'OfficeChatInput',
  props: {
    officeFormData: {
      type: Object,
      required: true
    },
    uploadData: {
      type: Object,
      default: () => ({})
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      required: true
    },
    agentId: {
      type: String,
      required: true
    }
  },
  emits: [
    'update-form-field',
    'handle-tag-template-delete',
    'handle-tag-reference-delete',
    'preview-file',
    'template-upload-success',
    'reference-upload-success',
    'upload-error',
    'before-upload',
    'handle-enter',
    'send-office-message',
    'preset-values-filled'
  ],
  data() {
    return {
      hasAutoFilled: false // 防止重复自动填入
    }
  },
  mounted() {
    // 组件加载完成后自动填入预设值
    this.$nextTick(() => {
      // 延迟执行，确保组件完全加载
      setTimeout(() => {
        this.autoFillPresetValues()
      }, 500)
    })
  },
  methods: {
    updateFormField(field, value) {
      this.$emit('update-form-field', { field, value })
    },

    /**
     * 自动填入预设值（仅在首次加载时执行）
     */
    async autoFillPresetValues() {
      // 检查是否已经有数据，避免覆盖用户输入
      if (this.hasAutoFilled ||
          this.officeFormData.title ||
          this.officeFormData.contentRequirements ||
          this.officeFormData.referenceMaterials.length > 0) {
        console.log('跳过自动填入：已有数据或已执行过自动填入')
        return
      }

      this.hasAutoFilled = true
      await this.fillPresetValues()
    },

    /**
     * 填入预设值
     */
    async fillPresetValues() {
      try {
        // 显示加载提示
        const loadingMessage = ElMessage({
          message: '正在加载预设模板和参考文件...',
          type: 'info',
          duration: 0,
          showClose: false
        })

        // 设置预设表单值
        const presetFormData = {
          title: '关于下达2025年第一批以工代赈中央预算内投资计划的通知',
          subjectContent: '通知',
          issuingUnit: '广西壮族自治区发展和改革委员会',
          toPrimary: '各市发展改革委，各有关县（市、区）发展改革委（局）',
          createDate: '2024-12-20',
          releaseDate: '2024-12-20',
          documentNumber: '桂发改以工代赈〔2024〕1234号',
          contentRequirements: '请根据《广西壮族自治区发展和改革委员会关于下达2025年第一批以工代赈中央预算内投资计划的通知》文件精神，结合本地区实际情况，起草一份具体的实施通知。\n\n要求：\n1. 明确项目建设要求和标准\n2. 强调以工代赈政策执行要点\n3. 明确资金使用和监管要求\n4. 提出具体工作安排和时间节点',
          referenceTemplate: [],
          referenceMaterials: []
        }

        // 通过事件通知父组件更新表单数据
        this.$emit('preset-values-filled', presetFormData)

        // 自动上传参考文件
        const fileName = '广西壮族自治区发展和改革委员会关于下达2025年第一批以工代赈中央预算内投资计划的通知.docx'
        try {
          const fileId = await this.uploadFileFromPublic(fileName)

          if (fileId) {
            const { type, extension } = this.getFileType(fileName)

            const fileInfo = {
              fileId: fileId,
              fileType: type,
              extension: extension,
              fileName: fileName
            }

            // 通过事件通知父组件添加参考文件
            presetFormData.referenceMaterials = [fileInfo]
            this.$emit('preset-values-filled', presetFormData)

            // 关闭加载提示
            loadingMessage.close()

            ElMessage.success('模板加载完成，参考文件已准备就绪')
          } else {
            // 关闭加载提示
            loadingMessage.close()
            ElMessage.warning('模板加载完成，但参考文件上传失败')
          }
        } catch (uploadError) {
          console.error('上传参考文件失败:', uploadError)
          // 关闭加载提示
          loadingMessage.close()
          ElMessage.warning('模板加载完成，但参考文件上传失败: ' + uploadError.message)
        }

      } catch (error) {
        console.error('模板加载失败:', error)
        ElMessage.error('模板加载失败: ' + error.message)
      }
    },

    /**
     * 从public目录上传文件
     */
    async uploadFileFromPublic(fileName) {
      try {
        // 先获取文件内容
        const response = await fetch(`/${fileName}`)
        if (!response.ok) {
          throw new Error(`无法获取文件: ${fileName}`)
        }

        const blob = await response.blob()

        // 创建FormData
        const formData = new FormData()
        formData.append('file', blob, fileName)
        formData.append('userId', this.userId)
        formData.append('agentId', this.agentId)

        // 调用上传接口
        const uploadResponse = await fetch('/api/v1/public/file/upload', {
          method: 'POST',
          body: formData
        })

        const uploadResult = await uploadResponse.json()

        if (uploadResult.code === 200) {
          console.log('文件上传成功:', uploadResult)
          return uploadResult.data.fileId
        } else {
          throw new Error(uploadResult.msg || '文件上传失败')
        }
      } catch (error) {
        console.error('上传文件失败:', error)
        ElMessage.error(`上传文件失败: ${error.message}`)
        return null
      }
    },

    /**
     * 获取文件类型
     */
    getFileType(filename) {
      const extension = filename.split('.').pop().toUpperCase()

      const typeMap = {
        DOCUMENT: ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
        IMAGE: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
        AUDIO: ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
        VIDEO: ['MP4', 'MOV', 'MPEG', 'MPGA']
      }

      for (const [type, extensions] of Object.entries(typeMap)) {
        if (extensions.includes(extension)) {
          return {
            type,
            extension
          }
        }
      }

      return {
        type: 'CUSTOM',
        extension
      }
    },

    truncateFileName(fileName, maxLength = 12) {
      if (!fileName) return ''

      // 如果文件名长度小于等于最大长度，直接返回
      if (fileName.length <= maxLength) {
        return fileName
      }

      // 获取文件扩展名
      const dotIndex = fileName.lastIndexOf('.')
      const extension = dotIndex !== -1 ? fileName.substring(dotIndex) : ''
      const nameWithoutExt = dotIndex !== -1 ? fileName.substring(0, dotIndex) : fileName

      // 计算可用于显示文件名的长度（预留扩展名和省略号的空间）
      const availableLength = maxLength - extension.length - 3 // 3 是省略号的长度

      if (availableLength <= 0) {
        // 如果可用长度不够，只显示省略号和扩展名
        return '...' + extension
      }

      // 截断文件名并添加省略号
      return nameWithoutExt.substring(0, availableLength) + '...' + extension
    }
  }
}
</script>

<style scoped>
.office-chat-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 公文办公区域样式 */
.office-area {
  flex: 1;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.office-form {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  min-height: 180px;
  padding: 12px 12px 0 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.office-form:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border-color: #cbd5e1;
}

/* 紧凑型行样式 */
.office-row {
  margin-bottom: 0 !important;
  animation: slideInUp 0.3s ease-out;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为每一行添加延迟动画 */
.office-row:nth-child(1) {
  animation-delay: 0.1s;
}

.office-row:nth-child(2) {
  animation-delay: 0.2s;
}

.office-row:nth-child(3) {
  animation-delay: 0.3s;
}

/* 紧凑型表单项样式 */
.compact-form-item {
  margin-bottom: 8px !important;
  position: relative;
}

.compact-form-item :deep(.el-form-item__label) {
  margin-bottom: 4px !important;
  font-size: 12px !important;
  line-height: 1.2 !important;
  padding-bottom: 0 !important;
  color: #374151;
  font-weight: 500;
  position: relative;
  display: inline-block;
}

.compact-form-item :deep(.el-form-item__label)::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  transition: width 0.3s ease;
}

.compact-form-item:focus-within :deep(.el-form-item__label)::after {
  width: 100%;
}

/* 紧凑型选择框样式 */
.compact-select :deep(.el-input__inner) {
  height: 28px !important;
  line-height: 28px !important;
  font-size: 13px !important;
  padding: 0 8px !important;
}

/* 文件上传区域样式 */
.file-upload-wrapper {
  min-height: 28px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
}

.file-upload-wrapper:empty::before {
  content: "暂无文件";
  color: #9ca3af;
  font-size: 11px;
  line-height: 24px;
  opacity: 0.7;
}

.compact-tag {
  max-width: 100%;
  font-size: 11px !important;
  height: 20px !important;
  line-height: 18px !important;
  padding: 0 6px !important;
}

.compact-upload-btn .el-upload {
  display: inline-block;
  width: 100%;
}

.compact-upload-btn:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

.upload-text-btn {
  font-size: 11px !important;
  padding: 4px 8px !important;
  height: 24px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background: #ffffff !important;
  color: #3b82f6 !important;
  width: 100%;
  justify-content: center;
}

.upload-text-btn:hover {
  background: #f8fafc !important;
  border-color: #3b82f6 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.upload-text-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
}

.input-area {
  flex: 1;
  min-height: 50px;
}

/* 公文办公写作要求输入框样式 */
.office-textarea {
  min-height: 50px !important;
  height: 50px;
  font-size: 13px !important;
  line-height: 1.4;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  transition: all 0.3s ease;
  padding: 6px 8px !important;
}

.office-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.office-textarea::placeholder {
  color: #9ca3af;
  font-size: 13px;
}

.bottom-toolbar {
  margin-top: 0;
  flex-shrink: 0;
  min-height: 60px;
}

.file-tag {
  max-width: 100%;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  margin-bottom: 4px;
  font-size: 11px;
}

.clickable-file-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-file-tag:hover {
  opacity: 0.8;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 相关参考文件标签特殊样式 */
.reference-file-tag {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #86efac;
  color: #059669;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 4px;
  max-width: 120px;
  margin: 2px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reference-file-tag:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-color: #6ee7b7;
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.1);
  transform: translateY(-1px);
}

.reference-file-tag :deep(.el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  font-size: 11px;
  line-height: 1.2;
}

.reference-file-tag :deep(.el-tag__close) {
  color: #059669;
  opacity: 0.7;
  transition: all 0.2s ease;
  margin-left: 4px;
  font-size: 10px;
}

.reference-file-tag:hover :deep(.el-tag__close) {
  opacity: 1;
  color: #047857;
}

/* 公文办公表单优化样式 */
:deep(.office-form .el-form-item) {
  margin-bottom: 8px;
}

:deep(.office-form .el-form-item__label) {
  font-size: 12px;
  color: #374151;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 4px;
  padding-bottom: 0;
}

:deep(.office-form .el-input) {
  font-size: 13px;
}

:deep(.office-form .el-input__inner) {
  font-size: 13px;
  border-radius: 4px;
  border-color: #d1d5db;
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
  transition: all 0.3s ease;
}

:deep(.office-form .el-input__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  background: #fefefe;
  transform: translateY(-1px);
}

:deep(.office-form .el-input__inner:hover) {
  border-color: #93c5fd;
  background: #fefefe;
}

:deep(.office-form .el-select) {
  width: 100%;
}

:deep(.office-form .el-select .el-input__inner) {
  cursor: pointer;
}

:deep(.office-form .el-date-editor) {
  width: 100%;
}

:deep(.office-form .el-date-editor .el-input__inner) {
  cursor: pointer;
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

:deep(.office-form .el-date-editor--date) {
  width: 100%;
}

:deep(.office-form .el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

/* 上传组件拖拽区域优化 */
:deep(.office-form .el-upload) {
  width: 100%;
  border: none;
}

:deep(.office-form .el-upload .el-upload-dragger) {
  width: 100%;
  height: auto;
  border: 1px dashed #d1d5db;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

:deep(.office-form .el-upload .el-upload-dragger:hover) {
  border-color: #3b82f6;
  background: #f8fafc;
}

:deep(.el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  max-width: 120px;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  :deep(.office-form .el-col-5) {
    flex: 0 0 25%;
    max-width: 25%;
  }
  
  :deep(.office-form .el-col-19) {
    flex: 0 0 75%;
    max-width: 75%;
  }
  
  :deep(.office-form .el-col-6) {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  :deep(.office-form .el-col-8) {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
}

@media (max-width: 992px) {
  .office-form {
    padding: 10px !important;
    min-height: 170px;
  }
  
  :deep(.office-form .el-col-5) {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  :deep(.office-form .el-col-19) {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  :deep(.office-form .el-col-6) {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  :deep(.office-form .el-col-8) {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  .compact-form-item :deep(.el-form-item__label) {
    font-size: 11px !important;
  }
  
  :deep(.office-form .el-input__inner) {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .office-form {
    padding: 8px !important;
    min-height: 160px;
  }
  
  .office-row {
    margin-bottom: 6px !important;
  }
  
  .compact-form-item {
    margin-bottom: 6px !important;
  }
  
  :deep(.office-form .el-col-5),
  :deep(.office-form .el-col-19),
  :deep(.office-form .el-col-6),
  :deep(.office-form .el-col-8) {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 4px;
  }
  
  .office-textarea {
    min-height: 40px !important;
    height: 40px;
    font-size: 12px !important;
  }
  
  .compact-form-item :deep(.el-form-item__label) {
    font-size: 11px !important;
  }
  
  :deep(.office-form .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    padding: 0 6px;
  }
  
  :deep(.office-form .el-date-editor .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .office-form {
    padding: 6px !important;
    min-height: 140px;
  }

  .office-textarea {
    min-height: 35px !important;
    height: 35px;
    font-size: 11px !important;
  }
}


</style>
