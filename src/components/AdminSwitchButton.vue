<template>
  <div class="fixed top-4 right-4 z-50 admin-switch-button">
    <!-- 意见反馈按钮 -->
    <el-button 
      type="warning" 
      size="small"
      @click="showFeedbackDialog"
      class="feedback-button shadow-lg hover:shadow-xl transition-all duration-300"
    >
      <el-icon class="mr-1"><MessageBox /></el-icon>
      吐槽
    </el-button>
    
    <!-- 管理端切换按钮 -->
    <el-button 
      v-if="isAdmin"
      type="primary" 
      size="small"
      @click="switchToAdmin"
      class="shadow-lg hover:shadow-xl transition-all duration-300"
    >
      <el-icon class="mr-1"><Setting /></el-icon>
      切换至管理端
    </el-button>
  </div>

  <!-- 意见反馈弹窗 -->
  <FeedbackDialog v-model="feedbackDialogVisible" />
</template>

<script setup>
import { Setting, MessageBox } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref, onMounted } from 'vue'
import { loginUserInfo } from '@/api/auth'
import FeedbackDialog from './FeedbackDialog.vue'

// 判断是否为管理员
const isAdmin = ref(false)

// 控制意见反馈弹窗显示
const feedbackDialogVisible = ref(false)

// 检查用户权限
const checkUserPermission = async () => {
  try {
    const response = await loginUserInfo()
    if (response.code === 200 && response.data) {
      // 只有当用户类型为2时才显示按钮（管理员）
      isAdmin.value = response.data.type === 2
    } else {
      isAdmin.value = false
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    isAdmin.value = false
  }
}

// 组件挂载时检查权限
onMounted(() => {
  checkUserPermission()
  
  // 监听自定义事件，用于手动刷新权限
  window.addEventListener('refresh-admin-permission', () => {
    checkUserPermission()
  })
  
  // 监听登录成功事件，自动检查权限
  window.addEventListener('login-success', () => {
    // 延迟一点时间确保登录状态已完全更新
    setTimeout(() => {
      checkUserPermission()
    }, 100)
  })
  
  // 监听用户信息更新事件
  window.addEventListener('user-info-updated', () => {
    checkUserPermission()
  })
  
  // 监听退出登录事件，自动隐藏按钮
  window.addEventListener('logout-success', () => {
    isAdmin.value = false
  })
})

// 暴露刷新权限的方法给外部使用
window.refreshAdminPermission = () => {
  checkUserPermission()
}

// 显示意见反馈弹窗
const showFeedbackDialog = () => {
  feedbackDialogVisible.value = true
}

const switchToAdmin = () => {
  // 获取当前URL
  const currentUrl = window.location.href
  
  // 检查URL是否已经包含manager路径
  if (currentUrl.includes('/manager/')) {
    ElMessage.info('已经在管理端界面')
    return
  }
  
  // 构建管理端URL
  const baseUrl = window.location.origin
  const search = window.location.search
  const hash = window.location.hash
  
  // 清除现有路径，直接跳转到manager根路径
  let adminUrl = baseUrl + '/manager/'
  
  // 添加查询参数和hash
  adminUrl += search + hash
  
  // 跳转到管理端
  window.location.href = adminUrl
}
</script>

<style scoped>
/* 管理端切换按钮样式 */
.admin-switch-button {
  position: fixed;
  top: 3px;
  right: 16px;
  z-index: 9999;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-switch-button .el-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.admin-switch-button .el-button:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6b4190 100%);
  transform: translateY(-2px);
}

.admin-switch-button .el-button:active {
  transform: translateY(0);
}

/* 意见反馈按钮样式 */
.feedback-button {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
  border: none !important;
  border-radius: 20px !important;
  padding: 8px 16px !important;
  font-weight: 500 !important;
  letter-spacing: 0.5px !important;
}

.feedback-button:hover {
  background: linear-gradient(135deg, #e885f7 0%, #f3435a 100%) !important;
  transform: translateY(-2px);
}

.feedback-button:active {
  transform: translateY(0);
}
</style> 