<template>
  <div class="workspace-section">
    <div class="workspace-header">
      <div class="workspace-nav">
        <button 
          v-for="tab in workspaceTabs" 
          :key="tab.key"
          class="workspace-tab"
          :class="{ active: activeWorkspaceTab === tab.key }"
          @click="activeWorkspaceTab = tab.key"
        >
          {{ tab.label }}
        </button>
      </div>
    </div>
    
    <div class="workspace-content">
      <!-- 实时跟随选项卡内容 -->
      <div v-if="activeWorkspaceTab === 'realtime'" class="realtime-content">
        <!-- 当前执行节点信息 -->
        <div v-if="currentNode" class="current-node-info">
          <h4 class="section-title">当前节点</h4>
          <div class="node-card">
            <div class="node-header">
              <span class="node-name">{{ currentNode.name }}</span>
              <span class="node-type">{{ currentNode.type }}</span>
            </div>
            <div class="node-description">
              {{ currentNode.description }}
            </div>
            <div class="node-status">
              状态: <span :class="currentNode.status">{{ getStatusText(currentNode.status) }}</span>
            </div>
          </div>
        </div>

        <!-- 执行流程 -->
        <div class="execution-flow">
          <div class="section-header">
            <h4 class="section-title">判断结果</h4>
            <div class="section-actions">
              <!--<button class="action-btn download-btn" @click="downloadResults" title="下载结果">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7,10 12,15 17,10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
              </button>-->
              <!--<button class="action-btn copy-btn" @click="copyResults" title="复制结果">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
              </button>-->
            </div>
          </div>
          <div class="flow-container">
            <!-- 展示工作流节点 -->
            <div v-if="allWorkflowNodes.length > 0" class="workflow-container">
              <div v-for="(node, nodeIndex) in allWorkflowNodes" :key="nodeIndex" class="workflow-node">
                <div class="workflow-node-bubble" :class="getNodeBubbleClass(node.nodeType, node.nodeStatus, node.nodeName)">
                  <div class="node-header">
                    <div class="node-icon">
                      <span class="icon-emoji">{{ getNodeIcon(node.nodeType, node.nodeName) }}</span>
                    </div>
                    <div class="node-info">
                      <div class="node-name">{{ node.nodeName }}</div>
                      <div class="workflow-title">{{ node.workflowTitle }}</div>
                    </div>
                    <div class="node-status">
                      <div v-if="node.nodeStatus === 'start'" class="loading-spinner">
                        <svg class="animate-spin h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                      <div v-else-if="node.nodeStatus === 'end'" class="completed-icon">
                        <svg class="h-4 w-4 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 知识库节点内容展示 -->
                  <div v-if="isKnowledgeNode(node) && node.documents && node.documents.length > 0" class="knowledge-node-content">
                    <!-- 收起状态：仅显示一个展开按钮 -->
                    <div v-if="!node.isExpanded" class="knowledge-collapsed">
                      <button 
                        @click="toggleKnowledgeExpanded(node, nodeIndex)"
                        class="knowledge-toggle-btn collapsed"
                      >
                        <span class="toggle-icon">📚</span>
                        <span class="toggle-text">查看检索文档 ({{ node.documents.length }}个片段)</span>
                        <svg class="toggle-arrow" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                      </button>
                    </div>
                    
                    <!-- 展开状态：显示所有文档 -->
                    <div v-else class="knowledge-expanded">
                      <div class="knowledge-header">
                        <span class="knowledge-title">检索到的文档片段</span>
                        <button 
                          @click="toggleKnowledgeExpanded(node, nodeIndex)"
                          class="knowledge-toggle-btn expanded"
                        >
                          <span class="toggle-text">收起</span>
                          <svg class="toggle-arrow up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="18,15 12,9 6,15"></polyline>
                          </svg>
                        </button>
                      </div>
                      <div class="documents-list">
                        <div 
                          v-for="(doc, docIndex) in node.documents" 
                          :key="docIndex" 
                          class="document-item"
                        >
                          <div class="document-header">
                            <div class="document-info">
                              <span class="document-id">文档 #{{ docIndex + 1 }}</span>
                              <span class="similarity-score">相似度: {{ (parseFloat(doc.score) * 100).toFixed(1) }}%</span>
                            </div>
                            <el-popover 
                              popper-class="knowledge-doc-tooltip"
                              placement="top" 
                              :content="doc.content || '暂无文档内容'" 
                              :disabled="!doc.content"
                              :enterable="true"
                              :show-after="300"
                              width="40vw"
                              trigger="hover"
                            >
                              <template #reference>
                                <button class="view-content-btn">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                    <circle cx="12" cy="12" r="3"></circle>
                                  </svg>
                                  查看内容
                                </button>
                              </template>
                            </el-popover>
                          </div>
                          <div class="document-preview">
                            <p class="document-title">{{ doc.title || '文档片段' }}</p>
                            <p class="document-excerpt">
                              {{ (doc.content || '').substring(0, 120) }}{{ (doc.content || '').length > 120 ? '...' : '' }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 图像解析节点内容展示 -->
                  <div v-if="isImageNode(node) && getImageNodeData(node)" class="image-node-content">
                    <!-- 收起状态：仅显示一个展开按钮 -->
                    <div v-if="!node.isExpanded" class="image-collapsed">
                      <button 
                        @click="toggleImageExpanded(node, nodeIndex)"
                        class="image-toggle-btn collapsed"
                      >
                        <span class="toggle-icon">🖼️</span>
                        <span class="toggle-text">查看输入输出数据</span>
                        <svg class="toggle-arrow" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                      </button>
                    </div>
                    
                    <!-- 展开状态：显示输入输出数据 -->
                    <div v-else class="image-expanded">
                      <div class="image-header">
                        <span class="image-title">输入输出数据</span>
                        <button 
                          @click="toggleImageExpanded(node, nodeIndex)"
                          class="image-toggle-btn expanded"
                        >
                          <span class="toggle-text">收起</span>
                          <svg class="toggle-arrow up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="18,15 12,9 6,15"></polyline>
                          </svg>
                        </button>
                      </div>
                      <div class="image-data-container">
                         <!-- 输入数据 -->
                         <div class="data-section">
                           <div class="data-section-header">
                             <span class="data-section-title">📥 输入数据</span>
                           </div>
                           <div class="data-content">
                             <pre v-if="getImageNodeData(node).inputData">{{ formatJsonData(getImageNodeData(node).inputData) }}</pre>
                             <div v-else class="no-data-placeholder">暂无输入数据</div>
                           </div>
                         </div>
                         
                         <!-- 输出数据 -->
                         <div class="data-section">
                           <div class="data-section-header">
                             <span class="data-section-title">📤 输出数据</span>
                           </div>
                           <div class="data-content">
                             <pre v-if="getImageNodeData(node).outputData">{{ formatJsonData(getImageNodeData(node).outputData) }}</pre>
                             <div v-else class="no-data-placeholder">暂无输出数据</div>
                           </div>
                         </div>
                       </div>
                    </div>
                  </div>

                  <!-- 通用节点输入输出数据展示 -->
                  <div v-else-if="hasNodeInputOutputData(node)" class="node-io-content">
                    <!-- 收起状态：仅显示一个展开按钮 -->
                    <div v-if="!node.isIOExpanded" class="node-io-collapsed">
                      <button 
                        @click="toggleNodeIOExpanded(node, nodeIndex)"
                        class="node-io-toggle-btn collapsed"
                      >
                        <span class="toggle-icon">⚙️</span>
                        <span class="toggle-text">查看输入输出数据</span>
                        <svg class="toggle-arrow" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                      </button>
                    </div>
                    
                    <!-- 展开状态：显示输入输出数据 -->
                    <div v-else class="node-io-expanded">
                      <div class="node-io-header">
                        <span class="node-io-title">输入输出数据</span>
                        <button 
                          @click="toggleNodeIOExpanded(node, nodeIndex)"
                          class="node-io-toggle-btn expanded"
                        >
                          <span class="toggle-text">收起</span>
                          <svg class="toggle-arrow up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="18,15 12,9 6,15"></polyline>
                          </svg>
                        </button>
                      </div>
                      <div class="node-io-data-container">
                         <!-- 输入数据 -->
                         <div v-if="getNodeInputOutputData(node).inputData" class="data-section">
                           <div class="data-section-header">
                             <span class="data-section-title">📥 输入数据</span>
                           </div>
                           <div class="data-content">
                             <pre>{{ formatJsonData(getNodeInputOutputData(node).inputData) }}</pre>
                           </div>
                         </div>
                         
                         <!-- 输出数据 -->
                         <div v-if="getNodeInputOutputData(node).outputData" class="data-section">
                           <div class="data-section-header">
                             <span class="data-section-title">📤 输出数据</span>
                           </div>
                           <div class="data-content">
                             <pre>{{ formatJsonData(getNodeInputOutputData(node).outputData) }}</pre>
                           </div>
                         </div>
                         
                         <!-- 参数数据 -->
                         <div v-if="getNodeInputOutputData(node).parameters" class="data-section">
                           <div class="data-section-header">
                             <span class="data-section-title">⚙️ 参数数据</span>
                           </div>
                           <div class="data-content">
                             <pre>{{ formatJsonData(getNodeInputOutputData(node).parameters) }}</pre>
                           </div>
                         </div>
                         
                         <!-- 结果数据 -->
                         <div v-if="getNodeInputOutputData(node).result" class="data-section">
                           <div class="data-section-header">
                             <span class="data-section-title">📋 结果数据</span>
                           </div>
                           <div class="data-content">
                             <pre>{{ formatJsonData(getNodeInputOutputData(node).result) }}</pre>
                           </div>
                         </div>
                         
                         <!-- 如果没有任何数据，显示占位符 -->
                         <div v-if="!hasAnyNodeData(getNodeInputOutputData(node))" class="no-data-placeholder">
                           暂无可显示的输入输出数据
                         </div>
                       </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 下载文件区域 - 显示在最后一个工作流节点下方 -->
            <div v-if="extractedDownloadFiles && extractedDownloadFiles.length > 0" class="download-files-section">
              <div class="download-files-header">
                <div class="section-icon">📁</div>
                <h4 class="section-title">生成文件</h4>
                <div class="files-count">{{ extractedDownloadFiles.length }}个文件</div>
              </div>
              <div class="download-files-container">
                <div 
                  v-for="(file, index) in extractedDownloadFiles" 
                  :key="index" 
                  class="download-file-card"
                >
                  <div class="file-icon-container">
                    <div class="file-icon" :class="getFileTypeClass(file.name || file.url)">
                      {{ getFileTypeIcon(file.name || file.url) }}
                    </div>
                    <div class="file-status-indicator completed">
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 6L9 17l-5-5"/>
                      </svg>
                    </div>
                  </div>
                  
                  <div class="file-info">
                    <div class="file-name">{{ file.name || '生成文件' }}</div>
                    <div class="file-meta">
                      <span class="file-size" v-if="file.size">{{ formatFileSize(file.size) }}</span>
                      <span class="file-type">{{ getFileExtension(file.name || file.url) }}</span>
                      <span class="generation-time">刚刚生成</span>
                    </div>
                  </div>
                  
                  <div class="file-actions">
                    <button 
                      class="download-btn primary"
                      @click="downloadFile(file)"
                      title="下载文件"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                      </svg>
                      下载
                    </button>
                    <button 
                      class="preview-btn secondary"
                      @click="previewFile(file)"
                      v-if="canPreview(file)"
                      title="预览文件"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                        <circle cx="12" cy="12" r="3"/>
                      </svg>
                      预览
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 如果没有工作流节点，显示提示 -->
            <div v-else-if="allWorkflowNodes.length === 0" class="empty-state">
              <div class="empty-icon">⚡</div>
              <div class="empty-text">等待工作流执行...</div>
              <div class="empty-description">AI开始处理后，执行过程将在此显示</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件选项卡内容 - 知识库节点内容展示 -->
      <div v-else-if="activeWorkspaceTab === 'files'" class="files-content">
        <div class="section-header">
          <h4 class="section-title">文档检索结果</h4>
        </div>
        
        <div v-if="knowledgeNodesWithDocuments.length > 0" class="knowledge-nodes-container">
          <div v-for="(node, nodeIndex) in knowledgeNodesWithDocuments" :key="nodeIndex" class="knowledge-node-card">
            <div class="knowledge-node-header">
              <div class="node-icon">
                <span class="icon-emoji">{{ getNodeIcon(node.nodeType, node.nodeName) }}</span>
              </div>
              <div class="node-info">
                <div class="node-name">{{ node.nodeName }}</div>
                <div class="workflow-title">{{ node.workflowTitle }}</div>
              </div>
            </div>
            
            <!-- 知识库节点内容展示 -->
            <div v-if="node.documents && node.documents.length > 0" class="knowledge-node-content">
              <!-- 收起状态：仅显示一个展开按钮 -->
              <div v-if="!node.isExpanded" class="knowledge-collapsed">
                <button 
                  @click="toggleKnowledgeExpanded(node, nodeIndex)"
                  class="knowledge-toggle-btn collapsed"
                >
                  <span class="toggle-icon">📚</span>
                  <span class="toggle-text">查看检索文档 ({{ node.documents.length }}个片段)</span>
                  <svg class="toggle-arrow" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </button>
              </div>
              
              <!-- 展开状态：显示所有文档 -->
              <div v-else class="knowledge-expanded">
                <div class="knowledge-header">
                  <span class="knowledge-title">检索到的文档片段</span>
                  <button 
                    @click="toggleKnowledgeExpanded(node, nodeIndex)"
                    class="knowledge-toggle-btn expanded"
                  >
                    <span class="toggle-text">收起</span>
                    <svg class="toggle-arrow up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="18,15 12,9 6,15"></polyline>
                    </svg>
                  </button>
                </div>
                <div class="documents-list">
                  <div 
                    v-for="(doc, docIndex) in node.documents" 
                    :key="docIndex" 
                    class="document-item"
                  >
                    <div class="document-header">
                      <div class="document-info">
                        <span class="document-id">文档 #{{ docIndex + 1 }}</span>
                        <span class="similarity-score">相似度: {{ (parseFloat(doc.score) * 100).toFixed(1) }}%</span>
                      </div>
                      <el-popover 
                        popper-class="knowledge-doc-tooltip"
                        placement="top" 
                        :content="doc.content || '暂无文档内容'" 
                        :disabled="!doc.content"
                        :enterable="true"
                        :show-after="300"
                        width="40vw"
                        trigger="hover"
                      >
                        <template #reference>
                          <button class="view-content-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                              <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            查看内容
                          </button>
                        </template>
                      </el-popover>
                    </div>
                    <div class="document-preview">
                      <p class="document-title">{{ doc.title || '文档片段' }}</p>
                      <p class="document-excerpt">{{ doc.content ? doc.content.substring(0, 150) + (doc.content.length > 150 ? '...' : '') : '暂无预览内容' }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 如果没有知识库节点或文档，显示空状态 -->
        <div v-else class="empty-state">
          <div class="empty-icon">📚</div>
          <div class="empty-text">暂无文档检索结果</div>
          <div class="empty-description">当AI进行知识库检索时，相关文档将在此显示</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage, ElPopover } from 'element-plus'

export default {
  name: 'WorkspaceSection',
  components: {
    ElPopover
  },
  props: {
    currentNode: {
      type: Object,
      default: null
    },
    executionSteps: {
      type: Array,
      default: () => []
    },
    currentAiMessage: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      activeWorkspaceTab: 'realtime' // 默认选中实时跟随
    }
  },
  computed: {
    workspaceTabs() {
      return [
        { key: 'realtime', label: '实时跟随' },
        //{ key: 'browser', label: '浏览器' },
        { key: 'files', label: '文件' }
      ]
    },
    
    // 修改：获取所有工作流节点而不是最新的一个
    allWorkflowNodes() {
      console.log('🔍 WorkspaceSection - 计算 allWorkflowNodes:', {
        hasCurrentAiMessage: !!this.currentAiMessage,
        hasWorkflowNodes: !!(this.currentAiMessage && this.currentAiMessage.workflowNodes),
        workflowNodesLength: this.currentAiMessage?.workflowNodes?.length || 0,
        workflowNodes: this.currentAiMessage?.workflowNodes
      })

      if (!this.currentAiMessage) {
        console.log('❌ WorkspaceSection - 没有 currentAiMessage')
        return [];
      }

      if (!this.currentAiMessage.workflowNodes) {
        console.log('❌ WorkspaceSection - 没有 workflowNodes')
        return [];
      }

      if (this.currentAiMessage.workflowNodes.length === 0) {
        console.log('❌ WorkspaceSection - workflowNodes 为空')
        return [];
      }
      
      // 处理图像解析节点的输入输出数据
      const processedNodes = this.processAllNodes(this.currentAiMessage.workflowNodes);
      
      // 返回所有节点，按顺序显示，并为知识库节点、图像解析节点和通用IO节点设置展开状态
      const finalNodes = processedNodes.map(node => {
        // 为了避免直接修改原始对象，创建一个副本
        const nodeClone = { ...node };
        
        // 自动展开/收起逻辑：当前执行节点自动展开，执行完成节点自动收起
        const shouldAutoExpand = nodeClone.nodeStatus === 'start'; // 正在执行的节点自动展开
        const shouldAutoCollapse = nodeClone.nodeStatus === 'end'; // 执行完成的节点自动收起
        
        // 知识库节点展开/收起控制
        if (this.isKnowledgeNode(nodeClone) && nodeClone.documents && nodeClone.documents.length > 0) {
          if (nodeClone.userExpanded === undefined) {
            // 用户从未手动操作过，根据节点执行状态自动控制
            nodeClone.isExpanded = shouldAutoExpand;
          } else {
            // 用户手动操作过，尊重用户选择
            nodeClone.isExpanded = nodeClone.userExpanded;
          }
        }
        
        // 图像解析节点展开/收起控制
        if (this.isImageNode(nodeClone) && nodeClone.data) {
          if (nodeClone.userExpanded === undefined) {
            // 用户从未手动操作过，根据节点执行状态自动控制
            nodeClone.isExpanded = shouldAutoExpand;
          } else {
            // 用户手动操作过，尊重用户选择
            nodeClone.isExpanded = nodeClone.userExpanded;
          }
        }
        
        // 通用IO节点展开/收起控制
        if (!this.isKnowledgeNode(nodeClone) && !this.isImageNode(nodeClone) && this.hasNodeInputOutputData(nodeClone)) {
          if (nodeClone.userExpandedIO === undefined) {
            // 用户从未手动操作过，根据节点执行状态自动控制
            nodeClone.isIOExpanded = shouldAutoExpand;
          } else {
            // 用户手动操作过，尊重用户选择
            nodeClone.isIOExpanded = nodeClone.userExpandedIO;
          }
        }
        
        return nodeClone;
      });
      
      return finalNodes;
    },

    latestWorkflowNode() {
      if (!this.currentAiMessage || !this.currentAiMessage.workflowNodes || this.currentAiMessage.workflowNodes.length === 0) {
        return null;
      }
      // 返回最新的节点（数组的最后一个元素）
      return this.currentAiMessage.workflowNodes[this.currentAiMessage.workflowNodes.length - 1];
    },

    // 获取包含文档的知识库节点
    knowledgeNodesWithDocuments() {
      if (!this.currentAiMessage || !this.currentAiMessage.workflowNodes || this.currentAiMessage.workflowNodes.length === 0) {
        return [];
      }
      
      // 筛选出知识库节点且包含文档的节点，并设置默认展开状态
      return this.currentAiMessage.workflowNodes.filter(node => {
        return this.isKnowledgeNode(node) && node.documents && node.documents.length > 0;
      }).map(node => {
        // 为了避免直接修改原始对象，创建一个副本
        const nodeClone = { ...node };
        
        // 如果用户没有明确展开过，就设置为收起
        if (nodeClone.userExpanded !== true) {
          nodeClone.isExpanded = false;
        }
        
        return nodeClone;
      });
    },

    // 自动从工作流节点中提取下载文件
    extractedDownloadFiles() {
      if (!this.currentAiMessage || !this.currentAiMessage.workflowNodes || this.currentAiMessage.workflowNodes.length === 0) {
        return [];
      }
      
      const downloadFiles = [];
      
      // 遍历所有工作流节点，查找包含下载链接的节点
      this.currentAiMessage.workflowNodes.forEach((node, index) => {
        console.log(`检查节点 ${index}:`, {
          nodeName: node.nodeName,
          nodeType: node.nodeType,
          nodeStatus: node.nodeStatus,
          hasResult: !!node.result,
          hasParameters: !!node.parameters,
          hasOutputData: !!node.outputData
        });
        
        // 检查节点的 result 字段
        if (node.result) {
          console.log(`节点 ${index} 的 result 数据:`, node.result);
          try {
            let resultData = node.result;
            
            // 如果 result 是字符串，尝试解析为 JSON
            if (typeof resultData === 'string') {
              resultData = JSON.parse(resultData);
            }
            
            console.log(`节点 ${index} 解析后的 result 数据:`, resultData);
            
            // 检查是否包含 url 字段
            if (resultData && resultData.url) {
              const fileName = this.getFileNameFromUrl(resultData.url);
              // 避免重复添加相同的文件
              const isDuplicate = downloadFiles.some(file => file.url === resultData.url);
              if (!isDuplicate) {
                console.log(`节点 ${index} 发现下载文件:`, {
                  url: resultData.url,
                  fileName: fileName
                });
                downloadFiles.push({
                  url: resultData.url,
                  name: fileName,
                  nodeName: node.nodeName,
                  workflowTitle: node.workflowTitle,
                  size: resultData.size || null
                });
              } else {
                console.log(`节点 ${index} 跳过重复文件:`, resultData.url);
              }
            }
          } catch (error) {
            console.log('解析节点结果数据失败:', error, node.result);
          }
        }
        
        // 检查节点的 parameters 字段（有些情况下文件链接可能在这里）
        if (node.parameters) {
          try {
            let paramsData = node.parameters;
            
            // 如果 parameters 是字符串，尝试解析为 JSON
            if (typeof paramsData === 'string') {
              paramsData = JSON.parse(paramsData);
            }
            
            // 检查是否包含 url 字段
            if (paramsData && paramsData.url) {
              const fileName = this.getFileNameFromUrl(paramsData.url);
              // 避免重复添加相同的文件
              const isDuplicate = downloadFiles.some(file => file.url === paramsData.url);
              if (!isDuplicate) {
                downloadFiles.push({
                  url: paramsData.url,
                  name: fileName,
                  nodeName: node.nodeName,
                  workflowTitle: node.workflowTitle,
                  size: paramsData.size || null
                });
              }
            }
          } catch (error) {
            console.log('解析节点参数数据失败:', error, node.parameters);
          }
        }
        
        // 检查节点的 outputData 字段（图像解析等节点可能在这里）
        if (node.outputData) {
          try {
            let outputData = node.outputData;
            
            // 如果 outputData 是字符串，尝试解析为 JSON
            if (typeof outputData === 'string') {
              outputData = JSON.parse(outputData);
            }
            
            // 检查是否包含 url 字段
            if (outputData && outputData.url) {
              const fileName = this.getFileNameFromUrl(outputData.url);
              // 避免重复添加相同的文件
              const isDuplicate = downloadFiles.some(file => file.url === outputData.url);
              if (!isDuplicate) {
                downloadFiles.push({
                  url: outputData.url,
                  name: fileName,
                  nodeName: node.nodeName,
                  workflowTitle: node.workflowTitle,
                  size: outputData.size || null
                });
              }
            }
          } catch (error) {
            console.log('解析节点输出数据失败:', error, node.outputData);
          }
        }
      });
      
      // 最终去重处理，确保没有重复的文件
      const uniqueFiles = downloadFiles.filter((file, index, self) => 
        index === self.findIndex(f => f.url === file.url)
      );
      
      console.log('提取到的下载文件:', uniqueFiles);
      return uniqueFiles;
    }
  },
  methods: {
    getStatusText(status) {
      const statusMap = {
        pending: '等待中',
        running: '运行中',
        completed: '已完成',
        error: '错误'
      }
      return statusMap[status] || status
    },

    downloadResults() {
      try {
        // 生成执行步骤的文本内容
        const content = this.executionSteps.map(step => 
          `${step.timestamp} - ${step.title} (${this.getStatusText(step.status)})\n${step.description ? step.description + '\n' : ''}`
        ).join('\n')
        
        // 创建下载链接
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `执行结果_${new Date().toLocaleString().replace(/[/:]/g, '-')}.txt`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        ElMessage.success('执行结果已下载')
      } catch (error) {
        console.error('下载失败:', error)
        ElMessage.error('下载失败')
      }
    },

    copyResults() {
      try {
        // 生成执行步骤的文本内容
        const content = this.executionSteps.map(step => 
          `${step.timestamp} - ${step.title} (${this.getStatusText(step.status)})\n${step.description ? step.description + '\n' : ''}`
        ).join('\n')
        
        // 复制到剪贴板
        navigator.clipboard.writeText(content).then(() => {
          ElMessage.success('执行结果已复制到剪贴板')
        }).catch(err => {
          console.error('复制失败:', err)
          // 降级处理：使用传统方法
          const textarea = document.createElement('textarea')
          textarea.value = content
          document.body.appendChild(textarea)
          textarea.select()
          document.execCommand('copy')
          document.body.removeChild(textarea)
          ElMessage.success('执行结果已复制到剪贴板')
        })
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    },

    // 获取节点样式类
    getNodeBubbleClass(nodeType, nodeStatus, nodeName = '') {
      const baseClass = 'node-bubble'
      const activeClass = nodeStatus === 'start' ? 'node-active' : 'node-completed'
      
      // 将节点类型和名称转为小写用于匹配
      const lowerNodeType = (nodeType || '').toLowerCase()
      const lowerNodeName = (nodeName || '').toLowerCase()
      const combinedText = `${lowerNodeType} ${lowerNodeName}`
      
      if (combinedText.includes('start') || combinedText.includes('开始') || combinedText.includes('起始')) {
        return `${baseClass} node-start ${activeClass}`
      } else if (combinedText.includes('end') || combinedText.includes('结束') || combinedText.includes('终止')) {
        return `${baseClass} node-end ${activeClass}`
      } else if (combinedText.includes('retriever') || combinedText.includes('检索') || combinedText.includes('召回')) {
        return `${baseClass} node-retriever ${activeClass}`
      } else if (combinedText.includes('search') || combinedText.includes('搜索') || combinedText.includes('知识库')) {
        return `${baseClass} node-search ${activeClass}`
      } else if (combinedText.includes('llm') || combinedText.includes('语言模型') || 
                 combinedText.includes('gpt') || combinedText.includes('claude')) {
        return `${baseClass} node-llm ${activeClass}`
      } else if (combinedText.includes('model') || combinedText.includes('大模型') || 
                 combinedText.includes('回答') || combinedText.includes('answer')) {
        return `${baseClass} node-model ${activeClass}`
      } else if (combinedText.includes('workflow') || combinedText.includes('工作流') || combinedText.includes('流程')) {
        return `${baseClass} node-workflow ${activeClass}`
      } else if (combinedText.includes('code') || combinedText.includes('代码') || 
                 combinedText.includes('python') || combinedText.includes('script')) {
        return `${baseClass} node-code ${activeClass}`
      } else if (combinedText.includes('database') || combinedText.includes('数据库') || 
                 combinedText.includes('sql') || combinedText.includes('db')) {
        return `${baseClass} node-database ${activeClass}`
      } else if (combinedText.includes('tool') || combinedText.includes('工具')) {
        return `${baseClass} node-tool ${activeClass}`
      } else {
        return `${baseClass} node-default ${activeClass}`
      }
    },

    // 获取节点图标
    getNodeIcon(nodeType, nodeName = '') {
      // 根据节点类型和名称返回不同的图标
      const iconMap = {
        'start': '🚀',
        'end': '🎯', 
        'search': '🔍',
        'model': '🧠',
        'knowledge': '📚',
        'tool': '🔧',
        'condition': '❓',
        'llm': '🤖',
        'workflow': '⚡',
        'retriever': '📖',
        'answer': '💬',
        'query': '🔎',
        'rewrite': '✏️',
        'classify': '🏷️',
        'extract': '📋',
        'judge': '⚖️',
        'code': '💻',
        'http': '🌐',
        'database': '🗄️',
        'file': '📁',
        'email': '📧',
        'time': '⏰',
        'math': '🧮',
        'text': '📝',
        'image': '🖼️',
        'audio': '🎵',
        'video': '🎬',
        'default': '⚙️'
      }
      
      // 将节点类型和名称转为小写用于匹配
      const lowerNodeType = (nodeType || '').toLowerCase()
      const lowerNodeName = (nodeName || '').toLowerCase()
      const combinedText = `${lowerNodeType} ${lowerNodeName}`
      
      // 开始节点
      if (combinedText.includes('start') || combinedText.includes('开始') || combinedText.includes('起始')) {
        return iconMap.start
      }
      // 结束节点  
      else if (combinedText.includes('end') || combinedText.includes('结束') || combinedText.includes('终止')) {
        return iconMap.end
      }
      // 搜索/检索节点
      else if (combinedText.includes('search') || combinedText.includes('搜索') || 
               combinedText.includes('retriever') || combinedText.includes('检索') ||
               combinedText.includes('知识库') || combinedText.includes('召回')) {
        return iconMap.search
      }
      // 大模型/LLM节点
      else if (combinedText.includes('llm') || combinedText.includes('model') || 
               combinedText.includes('大模型') || combinedText.includes('语言模型') ||
               combinedText.includes('gpt') || combinedText.includes('claude')) {
        return iconMap.llm
      }
      // 回答生成节点
      else if (combinedText.includes('answer') || combinedText.includes('回答') || 
               combinedText.includes('生成') || combinedText.includes('response')) {
        return iconMap.answer
      }
      // 查询重写节点
      else if (combinedText.includes('rewrite') || combinedText.includes('重写') ||
               combinedText.includes('改写') || combinedText.includes('query')) {
        return iconMap.rewrite
      }
      // 分类节点
      else if (combinedText.includes('classify') || combinedText.includes('分类') ||
               combinedText.includes('判断') || combinedText.includes('classification')) {
        return iconMap.classify
      }
      // 提取节点
      else if (combinedText.includes('extract') || combinedText.includes('提取') ||
               combinedText.includes('抽取')) {
        return iconMap.extract
      }
      // 条件判断节点
      else if (combinedText.includes('condition') || combinedText.includes('条件') ||
               combinedText.includes('if') || combinedText.includes('判断')) {
        return iconMap.judge
      }
      // 代码执行节点
      else if (combinedText.includes('code') || combinedText.includes('代码') ||
               combinedText.includes('python') || combinedText.includes('script')) {
        return iconMap.code
      }
      // HTTP请求节点
      else if (combinedText.includes('http') || combinedText.includes('api') ||
               combinedText.includes('请求') || combinedText.includes('接口')) {
        return iconMap.http
      }
      // 数据库节点
      else if (combinedText.includes('database') || combinedText.includes('数据库') ||
               combinedText.includes('sql') || combinedText.includes('db')) {
        return iconMap.database
      }
      // 文件处理节点
      else if (combinedText.includes('file') || combinedText.includes('文件') ||
               combinedText.includes('upload') || combinedText.includes('上传')) {
        return iconMap.file
      }
      // 邮件节点
      else if (combinedText.includes('email') || combinedText.includes('邮件') ||
               combinedText.includes('mail')) {
        return iconMap.email
      }
      // 时间处理节点
      else if (combinedText.includes('time') || combinedText.includes('时间') ||
               combinedText.includes('date') || combinedText.includes('日期')) {
        return iconMap.time
      }
      // 数学计算节点
      else if (combinedText.includes('math') || combinedText.includes('计算') ||
               combinedText.includes('数学') || combinedText.includes('calc')) {
        return iconMap.math
      }
      // 文本处理节点
      else if (combinedText.includes('text') || combinedText.includes('文本') ||
               combinedText.includes('字符串') || combinedText.includes('string')) {
        return iconMap.text
      }
      // 图像处理节点
      else if (combinedText.includes('image') || combinedText.includes('图像') ||
               combinedText.includes('图片') || combinedText.includes('vision')) {
        return iconMap.image
      }
      // 音频处理节点
      else if (combinedText.includes('audio') || combinedText.includes('音频') ||
               combinedText.includes('声音') || combinedText.includes('语音')) {
        return iconMap.audio
      }
      // 视频处理节点
      else if (combinedText.includes('video') || combinedText.includes('视频') ||
               combinedText.includes('影像')) {
        return iconMap.video
      }
      // 工具节点
      else if (combinedText.includes('tool') || combinedText.includes('工具')) {
        return iconMap.tool
      }
      // 知识库节点
      else if (combinedText.includes('knowledge') || combinedText.includes('知识')) {
        return iconMap.knowledge
      }
      // 工作流节点
      else if (combinedText.includes('workflow') || combinedText.includes('工作流') ||
               combinedText.includes('流程')) {
        return iconMap.workflow
      }
      // 默认图标
      else {
        return iconMap.default
      }
    },

    // 获取文档图标类型
    getDocumentIconType(fileName) {
      if (!fileName) return 'text'
      
      const extension = fileName.split('.').pop().toLowerCase()
      
      if (extension === 'pdf') {
        return 'pdf'
      } else if (['doc', 'docx', 'docs'].includes(extension)) {
        return 'word'
      } else {
        return 'text'
      }
    },

    // 判断是否为知识库节点
    isKnowledgeNode(node) {
      // 通过节点名称判断是否为知识库节点
      return this.isKnowledgeNodeByName(node.nodeName) || 
             node.nodeType === 'knowledge' ||
             (node.documents && node.documents.length > 0);
    },

    // 通过节点名称判断是否为知识库节点
    isKnowledgeNodeByName(nodeName) {
      if (!nodeName) return false;
      const knowledgeKeywords = ['知识库', 'knowledge', '检索', 'retriever', '召回', 'search'];
      const lowerNodeName = nodeName.toLowerCase();
      return knowledgeKeywords.some(keyword => 
        lowerNodeName.includes(keyword.toLowerCase())
      );
    },

    // 处理所有节点，包括图像解析节点和通用节点的输入输出数据合并
    processAllNodes(workflowNodes) {
      return this.processImageNodes(workflowNodes);
    },

    // 处理图像解析节点，将输入输出数据合并
    processImageNodes(workflowNodes) {
      const processedNodes = [];
      const imageNodeMap = new Map(); // 存储图像解析节点的映射
      
      // 遍历所有节点
      workflowNodes.forEach((node, index) => {
        if (this.isImageNode(node)) {
          const key = `${node.nodeName}_${node.nodeType}_${node.workflowId}`;
          
          if (!imageNodeMap.has(key)) {
            // 创建新的图像解析节点
            const imageNodeData = {
              ...node,
              data: {
                inputData: null,
                outputData: null
              }
            };
            imageNodeMap.set(key, imageNodeData);
          }
          
          const imageNodeData = imageNodeMap.get(key);
          
          // 根据节点状态设置输入输出数据
          if (node.nodeStatus === 'start') {
            if (this.isValidNonEmptyData(node.parameters)) {
              try {
                const parsedParams = typeof node.parameters === 'string' 
                  ? JSON.parse(node.parameters) 
                  : node.parameters;
                imageNodeData.data.inputData = parsedParams;
              } catch (e) {
                imageNodeData.data.inputData = node.parameters;
              }
            }
          } else if (node.nodeStatus === 'end') {
            // 处理输入数据（从parameters字段）
            if (this.isValidNonEmptyData(node.parameters)) {
              try {
                const parsedParams = typeof node.parameters === 'string' 
                  ? JSON.parse(node.parameters) 
                  : node.parameters;
                imageNodeData.data.inputData = parsedParams;
              } catch (e) {
                imageNodeData.data.inputData = node.parameters;
              }
            }
            
            // 处理输出数据（从result字段）
            if (this.isValidNonEmptyData(node.result)) {
              try {
                const parsedResult = typeof node.result === 'string' 
                  ? JSON.parse(node.result) 
                  : node.result;
                imageNodeData.data.outputData = parsedResult;
              } catch (e) {
                imageNodeData.data.outputData = node.result;
              }
            }
            
            // 更新节点状态
            imageNodeData.nodeStatus = 'end';
          }
        } else {
          // 非图像解析节点直接添加
          processedNodes.push(node);
        }
      });
      
      // 将处理后的图像解析节点添加到结果中
      imageNodeMap.forEach((imageNode, key) => {
        // 找到在原始节点列表中的位置，保持顺序
        const originalIndex = workflowNodes.findIndex(n => 
          this.isImageNode(n) && 
          n.nodeName === imageNode.nodeName && 
          n.nodeType === imageNode.nodeType &&
          n.workflowId === imageNode.workflowId
        );
        
        if (originalIndex !== -1) {
          processedNodes.splice(originalIndex, 0, imageNode);
        } else {
          processedNodes.push(imageNode);
        }
      });
      
      // 按照原始顺序排序
      const sortedNodes = processedNodes.sort((a, b) => {
        const aIndex = workflowNodes.findIndex(n => 
          n.nodeName === a.nodeName && n.nodeType === a.nodeType && n.workflowId === a.workflowId
        );
        const bIndex = workflowNodes.findIndex(n => 
          n.nodeName === b.nodeName && n.nodeType === b.nodeType && n.workflowId === b.workflowId
        );
        return aIndex - bIndex;
      });
      
      return sortedNodes;
    },

    // 检查数据是否为有效的非空JSON
    isValidNonEmptyData(data) {
      if (!data) {
        return false;
      }
      
      if (typeof data === 'string') {
        // 检查是否为空的JSON对象字符串
        if (data === '{}' || data === '[]' || data.trim() === '') {
          return false;
        }
        
        try {
          const parsed = JSON.parse(data);
          // 检查解析后是否为空对象或空数组
          if (typeof parsed === 'object' && parsed !== null) {
            if (Array.isArray(parsed) && parsed.length === 0) {
              return false;
            }
            if (Object.keys(parsed).length === 0) {
              return false;
            }
          }
          return true;
        } catch (e) {
          return true; // 如果不能解析为JSON，那就当作普通字符串处理
        }
      }
      
      if (typeof data === 'object' && data !== null) {
        if (Array.isArray(data) && data.length === 0) {
          return false;
        }
        if (Object.keys(data).length === 0) {
          return false;
        }
        return true;
      }
      
      return true;
    },

    // 判断是否为图像解析节点
    isImageNode(node) {
      const isImageByType = node.nodeType === 'node_T4rCD2HdlRW3Yyzk';
      const isImageByName = node.nodeName && node.nodeName.includes('图像解析');
      const result = isImageByType || isImageByName;
      
      return result;
    },

    // 获取图像解析节点的输入输出数据
    getImageNodeData(node) {
      if (!node || !node.data) {
        return null;
      }
      
      return node.data;
    },

    // 格式化JSON数据，使其更易读
    formatJsonData(data) {
      if (data === null || data === undefined) {
        return 'null';
      }
      
      if (typeof data === 'string') {
        try {
          const parsed = JSON.parse(data);
          return JSON.stringify(parsed, null, 2);
        } catch (e) {
          return data; // 如果JSON无效，则返回原始字符串
        }
      }
      
      return JSON.stringify(data, null, 2);
    },

    // 切换知识库节点展开状态
    toggleKnowledgeExpanded(node, nodeIndex) {
      // 切换展开状态
      node.isExpanded = !node.isExpanded;
      
      // 标记用户已手动操作过
      if (node.isExpanded) {
        // 用户手动展开，设置手动展开标记
        node.userExpanded = true;
      } else {
        // 用户手动收起，清除手动展开标记
        node.userExpanded = false;
      }
      
      // 同时修改原始节点的状态，确保下次重新计算时保留用户的选择
      if (this.currentAiMessage && this.currentAiMessage.workflowNodes) {
        // 查找对应的原始节点（通过节点名称和类型匹配）
        const originalNode = this.currentAiMessage.workflowNodes.find(originalNode => 
          originalNode.nodeName === node.nodeName && 
          originalNode.nodeType === node.nodeType &&
          originalNode.workflowId === node.workflowId
        );
        
        if (originalNode) {
          originalNode.isExpanded = node.isExpanded;
          originalNode.userExpanded = node.userExpanded;
        }
      }
      
      // 强制更新视图以确保变化被正确显示
      this.$forceUpdate();
    },

    // 切换图像解析节点展开状态
    toggleImageExpanded(node, nodeIndex) {
      // 切换展开状态
      node.isExpanded = !node.isExpanded;
      
      // 标记用户已手动操作过
      if (node.isExpanded) {
        // 用户手动展开，设置手动展开标记
        node.userExpanded = true;
      } else {
        // 用户手动收起，清除手动展开标记
        node.userExpanded = false;
      }
      
      // 同时修改原始节点的状态，确保下次重新计算时保留用户的选择
      if (this.currentAiMessage && this.currentAiMessage.workflowNodes) {
        // 查找对应的原始节点（通过节点名称和类型匹配）
        const originalNode = this.currentAiMessage.workflowNodes.find(originalNode => 
          originalNode.nodeName === node.nodeName && 
          originalNode.nodeType === node.nodeType &&
          originalNode.workflowId === node.workflowId
        );
        
        if (originalNode) {
          originalNode.isExpanded = node.isExpanded;
          originalNode.userExpanded = node.userExpanded;
        }
      }
      
      // 强制更新视图以确保变化被正确显示
      this.$forceUpdate();
    },

    // 自动滚动到最新内容
    scrollToLatest() {
      this.$nextTick(() => {
        let container = null;
        if (this.activeWorkspaceTab === 'realtime') {
          container = this.$el.querySelector('.flow-container');
        } else if (this.activeWorkspaceTab === 'files') {
          container = this.$el.querySelector('.files-content');
        }
        
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },

         // 判断是否有节点输入输出数据
     hasNodeInputOutputData(node) {
       // 排除知识库节点和图像解析节点，它们有自己的展示逻辑
       if (this.isKnowledgeNode(node) || this.isImageNode(node)) {
         return false;
       }
       
       // 检查节点是否有任何有效的输入输出数据
       return this.isValidNonEmptyData(node.inputData) || 
              this.isValidNonEmptyData(node.outputData) || 
              this.isValidNonEmptyData(node.parameters) || 
              this.isValidNonEmptyData(node.result);
     },

         // 获取节点输入输出数据
     getNodeInputOutputData(node) {
       return {
         inputData: this.isValidNonEmptyData(node.inputData) ? node.inputData : null,
         outputData: this.isValidNonEmptyData(node.outputData) ? node.outputData : null,
         parameters: this.isValidNonEmptyData(node.parameters) ? node.parameters : null,
         result: this.isValidNonEmptyData(node.result) ? node.result : null
       };
     },

         // 判断是否有任何节点数据
     hasAnyNodeData(nodeData) {
       return nodeData.inputData !== null || 
              nodeData.outputData !== null || 
              nodeData.parameters !== null || 
              nodeData.result !== null;
     },

    // 切换节点输入输出数据展开状态
    toggleNodeIOExpanded(node, nodeIndex) {
      // 切换展开状态
      node.isIOExpanded = !node.isIOExpanded;
      
      // 标记用户已手动操作过
      if (node.isIOExpanded) {
        // 用户手动展开，设置手动展开标记
        node.userExpandedIO = true;
      } else {
        // 用户手动收起，清除手动展开标记
        node.userExpandedIO = false;
      }
      
      // 同时修改原始节点的状态，确保下次重新计算时保留用户的选择
      if (this.currentAiMessage && this.currentAiMessage.workflowNodes) {
        // 查找对应的原始节点（通过节点名称和类型匹配）
        const originalNode = this.currentAiMessage.workflowNodes.find(originalNode => 
          originalNode.nodeName === node.nodeName && 
          originalNode.nodeType === node.nodeType &&
          originalNode.workflowId === node.workflowId
        );
        
        if (originalNode) {
          originalNode.isIOExpanded = node.isIOExpanded;
          originalNode.userExpandedIO = node.userExpandedIO;
        }
      }
      
      // 强制更新视图以确保变化被正确显示
      this.$forceUpdate();
    },

    // 下载文件处理相关方法
    downloadFile(file) {
      try {
        // 如果file是字符串，说明是url
        const url = typeof file === 'string' ? file : file.url;
        const fileName = typeof file === 'object' && file.name ? file.name : this.getFileNameFromUrl(url);
        
        // 创建下载链接
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.target = '_blank';
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        ElMessage.success(`文件 ${fileName} 开始下载`);
      } catch (error) {
        console.error('下载文件失败:', error);
        ElMessage.error('下载文件失败');
      }
    },

    previewFile(file) {
      try {
        const url = typeof file === 'string' ? file : file.url;
        window.open(url, '_blank');
      } catch (error) {
        console.error('预览文件失败:', error);
        ElMessage.error('预览文件失败');
      }
    },

    // 判断文件是否可以预览
    canPreview(file) {
      const url = typeof file === 'string' ? file : file.url;
      const fileName = typeof file === 'object' && file.name ? file.name : this.getFileNameFromUrl(url);
      const extension = this.getFileExtension(fileName).toLowerCase();
      
      // 可预览的文件类型
      const previewableTypes = ['pdf', 'txt', 'md', 'html', 'htm', 'jpg', 'jpeg', 'png', 'gif', 'svg'];
      return previewableTypes.includes(extension);
    },

    // 获取文件类型图标
    getFileTypeIcon(fileName) {
      const extension = this.getFileExtension(fileName).toLowerCase();
      
      const iconMap = {
        pdf: '📄',
        doc: '📝',
        docx: '📝',
        txt: '📄',
        md: '📄',
        html: '🌐',
        htm: '🌐',
        jpg: '🖼️',
        jpeg: '🖼️',
        png: '🖼️',
        gif: '🖼️',
        svg: '🖼️',
        mp4: '🎬',
        avi: '🎬',
        mov: '🎬',
        mp3: '🎵',
        wav: '🎵',
        zip: '📦',
        rar: '📦',
        '7z': '📦',
        xlsx: '📊',
        xls: '📊',
        csv: '📊',
        ppt: '📊',
        pptx: '📊'
      };
      
      return iconMap[extension] || '📁';
    },

    // 获取文件类型样式类
    getFileTypeClass(fileName) {
      const extension = this.getFileExtension(fileName).toLowerCase();
      
      if (['pdf'].includes(extension)) {
        return 'file-type-pdf';
      } else if (['doc', 'docx', 'txt', 'md'].includes(extension)) {
        return 'file-type-document';
      } else if (['jpg', 'jpeg', 'png', 'gif', 'svg'].includes(extension)) {
        return 'file-type-image';
      } else if (['mp4', 'avi', 'mov'].includes(extension)) {
        return 'file-type-video';
      } else if (['mp3', 'wav'].includes(extension)) {
        return 'file-type-audio';
      } else if (['xlsx', 'xls', 'csv'].includes(extension)) {
        return 'file-type-spreadsheet';
      } else if (['ppt', 'pptx'].includes(extension)) {
        return 'file-type-presentation';
      } else if (['zip', 'rar', '7z'].includes(extension)) {
        return 'file-type-archive';
      } else {
        return 'file-type-default';
      }
    },

    // 获取文件扩展名
    getFileExtension(fileName) {
      if (!fileName) return '';
      const lastDot = fileName.lastIndexOf('.');
      return lastDot > 0 ? fileName.substring(lastDot + 1) : '';
    },

    // 从URL中提取文件名
    getFileNameFromUrl(url) {
      if (!url) return '生成文件';
      
      try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        
        // 检查URL参数中是否包含文件名信息
        const searchParams = urlObj.searchParams;
        const fileId = searchParams.get('fileId');
        
        // 如果有fileId，生成一个更友好的文件名
        if (fileId) {
          // 尝试从路径中获取可能的文件类型
          const pathParts = pathname.split('/');
          const lastPart = pathParts[pathParts.length - 1];
          
          // 如果路径包含download，说明是下载链接
          if (pathname.includes('download')) {
            // 根据常见的文件生成场景生成文件名
            return this.generateFriendlyFileName(fileId);
          }
          
          return lastPart || '生成文件';
        }
        
        // 从路径中提取文件名
        const fileName = pathname.substring(pathname.lastIndexOf('/') + 1);
        
        // 如果文件名为空或者是纯数字/UUID，生成一个友好的名称
        if (!fileName || this.isUUIDOrNumber(fileName)) {
          return this.generateFriendlyFileName(fileName);
        }
        
        return fileName || '生成文件';
      } catch (error) {
        // 如果URL解析失败，尝试直接从字符串中提取
        const parts = url.split('/');
        const lastPart = parts[parts.length - 1];
        
        if (!lastPart || this.isUUIDOrNumber(lastPart)) {
          return this.generateFriendlyFileName(lastPart);
        }
        
        return lastPart || '生成文件';
      }
    },

    // 判断字符串是否为UUID或纯数字
    isUUIDOrNumber(str) {
      if (!str) return true;
      
      // UUID格式检查
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      
      // 纯数字检查
      const numberRegex = /^\d+$/;
      
      return uuidRegex.test(str) || numberRegex.test(str);
    },

    // 生成友好的文件名
    generateFriendlyFileName(id) {
      const now = new Date();
      const timestamp = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/[\/\s:]/g, '_');
      
      // 如果有ID，使用ID的前8位作为标识
      const shortId = id && id.length > 8 ? id.substring(0, 8) : '';
      
      return shortId ? `生成文件_${shortId}_${timestamp}` : `生成文件_${timestamp}`;
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '未知大小';
      
      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      let size = bytes;
      let unitIndex = 0;
      
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }
      
      return `${size.toFixed(unitIndex === 0 ? 0 : 1)}${units[unitIndex]}`;
    }
  },

  watch: {
    // 监听工作流节点变化，自动滚动到最新内容
    allWorkflowNodes: {
      handler(newNodes, oldNodes) {
        if (newNodes && newNodes.length > 0) {
          // 检查是否有新节点或状态变化
          const hasNewContent = !oldNodes || 
            newNodes.length > oldNodes.length || 
            newNodes.some((node, index) => {
              const oldNode = oldNodes[index];
              return !oldNode || node.nodeStatus !== oldNode.nodeStatus;
            });

          if (hasNewContent) {
            // 延迟执行滚动，确保DOM已更新
            setTimeout(() => {
              this.scrollToLatest();
            }, 300);
          }
        }
      },
      deep: true
    },

    // 监听知识库节点文档变化，用于文件选项卡
    knowledgeNodesWithDocuments: {
      handler(newNodes, oldNodes) {
        if (newNodes && newNodes.length > 0 && this.activeWorkspaceTab === 'files') {
          const hasNewDocuments = !oldNodes || 
            newNodes.length > oldNodes.length ||
            newNodes.some((node, index) => {
              const oldNode = oldNodes[index];
              return !oldNode || 
                (node.documents && node.documents.length !== (oldNode.documents ? oldNode.documents.length : 0));
            });

          if (hasNewDocuments) {
            setTimeout(() => {
              this.scrollToLatest();
            }, 300);
          }
        }
      },
      deep: true
    },

    // 监听下载文件变化，当有新文件时自动滚动
    extractedDownloadFiles: {
      handler(newFiles, oldFiles) {
        if (newFiles && newFiles.length > 0 && this.activeWorkspaceTab === 'realtime') {
          const hasNewFiles = !oldFiles || newFiles.length > oldFiles.length;
          
          if (hasNewFiles) {
            console.log('检测到新的下载文件，自动滚动到最新内容');
            setTimeout(() => {
              this.scrollToLatest();
            }, 300);
          }
        }
      },
      deep: true
    }
  }
}
</script>

<style scoped>
/* 右侧工作空间 */
.workspace-section {
  /* 移除 flex: 1，使用父组件设置的宽度 */
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-width: 200px; /* 减少最小宽度限制 */
  /* width: 100% 移除，让父组件控制宽度 */
  flex-shrink: 0; /* 防止被压缩 */
  flex-grow: 0;   /* 防止扩展 */
}

.workspace-header {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
}

.workspace-nav {
  display: flex;
  gap: 0;
  border-bottom: 1px solid #e5e7eb;
  background: transparent;
}

.workspace-tab {
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: #6b7280;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
  border-bottom: 2px solid transparent;
}

.workspace-tab:hover {
  color: #374151;
}

.workspace-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.workspace-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

/* 实时跟随选项卡内容 */
.realtime-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

/* 节点信息 */
.current-node-info {
  margin-bottom: 1.5rem;
}

.section-title {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.node-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.node-name {
  font-weight: 600;
  color: #111827;
}

.node-type {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.node-description {
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.node-status span {
  font-weight: 600;
}

.node-status .running {
  color: #2563eb;
}

.node-status .completed {
  color: #16a34a;
}

.node-status .error {
  color: #dc2626;
}

/* 执行流程 */
.execution-flow {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.section-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: #FDFEFF;
  border: none;
  border-radius: 0.375rem;
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.action-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #334155;
}

.action-btn svg {
  flex-shrink: 0;
}

.download-btn:hover {
  background: #dbeafe;
  border-color: #93c5fd;
  color: #2563eb;
}

.copy-btn:hover {
  background: #dcfce7;
  border-color: #86efac;
  color: #16a34a;
}

.flow-container {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 2.5rem;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* AI消息展示相关样式 */
.ai-message-display {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

/* 工作流节点样式 */
.workflow-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.workflow-node {
  position: relative;
}

.workflow-node-bubble {
  padding: 12px 16px;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  background: #f8fafc;
}

.node-start {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #10b981;
}

.node-end {
  background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
  border-color: #6366f1;
}

.node-search {
  background: linear-gradient(135deg, #fefbf0 0%, #fef3c7 100%);
  border-color: #f59e0b;
}

.node-model {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border-color: #ef4444;
}

.node-llm {
  background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
  border-color: #3b82f6;
}

.node-retriever {
  background: linear-gradient(135deg, #f7fee7 0%, #ecfccb 100%);
  border-color: #84cc16;
}

.node-workflow {
  background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%);
  border-color: #a855f7;
}

.node-tool {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
  border-color: #ea580c;
}

.node-code {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #475569;
}

.node-database {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
  border-color: #14b8a6;
}

.node-default {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-color: #6b7280;
}

.node-active {
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
  animation: pulse 2s ease-in-out infinite;
}

.node-completed {
  opacity: 0.8;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.node-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.node-info {
  flex: 1;
}

.node-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.workflow-title {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.node-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner svg {
  animation: spin 1s linear infinite;
}

.completed-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-in-out;
}

.icon-emoji {
  font-size: 18px;
  line-height: 1;
  display: block;
}

/* AI回答气泡样式 */
.ai-response-bubble {
  padding: 16px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  flex: 1;
  overflow-y: auto;
}

.ai-content {
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  word-wrap: break-word;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-icon img {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
}

/* 文档引用样式 */
.doc-tooltip {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  padding: 8px 12px;
  max-height: 40vh;
  overflow-y: auto;
}

/* 文件选项卡内容 - 知识库节点内容展示 */
.files-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.knowledge-nodes-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.knowledge-node-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.knowledge-node-card:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.knowledge-node-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.knowledge-node-content {
  border-top: 1px dashed #e5e7eb;
  padding-top: 12px;
}

.knowledge-collapsed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 8px 0;
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
}

.knowledge-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: #e0e7ff;
  border: none;
  border-radius: 0.375rem;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.knowledge-toggle-btn:hover {
  background: #dbeafe;
  border-color: #93c5fd;
  color: #2563eb;
}

.toggle-icon {
  font-size: 16px;
}

.toggle-text {
  font-weight: 500;
}

.toggle-arrow {
  transition: transform 0.3s ease;
}

.knowledge-expanded {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.knowledge-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.documents-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.document-item {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.document-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.document-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.875rem;
  color: #6b7280;
}

.document-id {
  font-weight: 500;
  color: #1f2937;
}

.similarity-score {
  font-weight: 500;
  color: #2563eb;
}

.view-content-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: #e0e7ff;
  border: none;
  border-radius: 0.375rem;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-content-btn:hover {
  background: #dbeafe;
  border-color: #93c5fd;
  color: #2563eb;
}

.view-content-btn svg {
  flex-shrink: 0;
}

 .knowledge-doc-tooltip {
   white-space: pre-wrap;
   word-break: break-word;
   line-height: 1.5;
   padding: 8px 12px;
   max-height: 40vh;
   overflow-y: auto;
 }

 .document-preview {
   margin-top: 8px;
 }

 .document-title {
   font-weight: 500;
   color: #374151;
   font-size: 12px;
   margin-bottom: 4px;
   line-height: 1.4;
   display: -webkit-box;
   -webkit-line-clamp: 2;
   -webkit-box-orient: vertical;
   overflow: hidden;
 }

 .document-excerpt {
   font-size: 11px;
   color: #6b7280;
   line-height: 1.4;
   display: -webkit-box;
   -webkit-line-clamp: 3;
   -webkit-box-orient: vertical;
   overflow: hidden;
 }

 .empty-description {
   margin-top: 8px;
   font-size: 14px;
   color: #9ca3af;
   text-align: center;
 }

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 500px;
  }
}

/* 节点连接线 */
.workflow-node:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -12px;
  width: 2px;
  height: 12px;
  background: linear-gradient(to bottom, #d1d5db, transparent);
  transform: translateX(-50%);
  z-index: 1;
}

/* 节点悬停效果 */
.workflow-node-bubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 工作流节点中的知识库内容样式 */
.workflow-node .knowledge-node-content {
  margin-top: 12px;
  animation: fadeInUp 0.3s ease-out;
}

/* 工作流节点中的图像解析内容样式 */
.workflow-node .image-node-content {
  margin-top: 12px;
  animation: fadeInUp 0.3s ease-out;
}

/* 工作流节点中的通用IO内容样式 */
.workflow-node .node-io-content {
  margin-top: 12px;
  animation: fadeInUp 0.3s ease-out;
}

.workflow-node .image-collapsed {
  display: flex;
  justify-content: center;
  align-items: center;
}

.workflow-node .image-expanded {
  padding: 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border: 1px solid #7dd3fc;
  animation: slideDown 0.3s ease-out;
}

.workflow-node .image-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 20px;
  cursor: pointer;
  color: #64748b;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.workflow-node .image-toggle-btn:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #94a3b8;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.workflow-node .image-toggle-btn.collapsed {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-color: #7dd3fc;
  color: #0284c7;
}

.workflow-node .image-toggle-btn.collapsed:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #38bdf8;
  color: #0369a1;
}

.workflow-node .image-toggle-btn.expanded {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #7dd3fc;
  color: #0284c7;
  font-size: 12px;
  padding: 6px 12px;
}

.workflow-node .image-toggle-btn.expanded:hover {
  background: linear-gradient(135deg, #bae6fd 0%, #7dd3fc 100%);
  border-color: #38bdf8;
  color: #0369a1;
}

.workflow-node .image-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(6, 182, 212, 0.2);
}

.workflow-node .image-title {
  font-weight: 600;
  color: #0c4a6e;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.workflow-node .image-title::before {
  content: "🖼️";
  font-size: 16px;
}

.workflow-node .image-data-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.workflow-node .data-section {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 6px;
  padding: 10px;
  transition: all 0.2s ease;
}

.workflow-node .data-section:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(6, 182, 212, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(6, 182, 212, 0.1);
}

.workflow-node .data-section-header {
  margin-bottom: 8px;
}

.workflow-node .data-section-title {
  font-weight: 600;
  color: #0c4a6e;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.workflow-node .data-content {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: 4px;
  padding: 8px;
  overflow-x: auto;
}

.workflow-node .data-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.4;
  color: #334155;
  white-space: pre-wrap;
  word-break: break-word;
}

.workflow-node .no-data-placeholder {
  padding: 12px;
  text-align: center;
  color: #94a3b8;
  font-style: italic;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 4px;
  border: 1px dashed rgba(148, 163, 184, 0.3);
}

.workflow-node .data-section-header {
  margin-bottom: 8px;
}



/* 图像解析节点图标动画 */
.workflow-node .image-toggle-btn .toggle-icon {
  font-size: 16px;
  animation: pulse 2s ease-in-out infinite;
}

.workflow-node .image-toggle-btn .toggle-text {
  white-space: nowrap;
}

.workflow-node .image-toggle-btn .toggle-arrow {
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.workflow-node .image-toggle-btn .toggle-arrow.up {
  transform: rotate(180deg);
}

.workflow-node .knowledge-collapsed {
  display: flex;
  justify-content: center;
  align-items: center;
}

.workflow-node .knowledge-expanded {
  padding: 12px;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  border-radius: 8px;
  border: 1px solid #bbf7d0;
  animation: slideDown 0.3s ease-out;
}

.workflow-node .knowledge-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 20px;
  cursor: pointer;
  color: #64748b;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.workflow-node .knowledge-toggle-btn:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #94a3b8;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.workflow-node .knowledge-toggle-btn.collapsed {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: #86efac;
  color: #16a34a;
}

.workflow-node .knowledge-toggle-btn.collapsed:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-color: #4ade80;
  color: #15803d;
}

.workflow-node .knowledge-toggle-btn.expanded {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #86efac;
  color: #059669;
  font-size: 12px;
  padding: 6px 12px;
}

.workflow-node .knowledge-toggle-btn.expanded:hover {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #6ee7b7;
  color: #047857;
}

.workflow-node .knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(34, 197, 94, 0.2);
}

.workflow-node .knowledge-title {
  font-weight: 600;
  color: #166534;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.workflow-node .knowledge-title::before {
  content: "📚";
  font-size: 16px;
}

.workflow-node .documents-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.workflow-node .document-item {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 6px;
  padding: 10px;
  transition: all 0.2s ease;
}

.workflow-node .document-item:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(34, 197, 94, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.1);
}

.workflow-node .document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.workflow-node .document-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.workflow-node .document-id {
  font-weight: 600;
  color: #166534;
  font-size: 11px;
  background: rgba(34, 197, 94, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.workflow-node .similarity-score {
  font-size: 10px;
  color: #059669;
  background: rgba(16, 185, 129, 0.1);
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: 500;
}

.workflow-node .view-content-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #16a34a;
  font-size: 11px;
  padding: 4px 8px;
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
}

.workflow-node .view-content-btn:hover {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.5);
  color: #15803d;
}

.workflow-node .document-preview {
  margin-top: 6px;
}

.workflow-node .document-title {
  font-weight: 500;
  color: #374151;
  font-size: 12px;
  margin-bottom: 4px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.workflow-node .document-excerpt {
  font-size: 11px;
  color: #6b7280;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.workflow-node .toggle-icon {
  font-size: 16px;
  animation: pulse 2s ease-in-out infinite;
}

.workflow-node .toggle-text {
  white-space: nowrap;
}

.workflow-node .toggle-arrow {
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.workflow-node .toggle-arrow.up {
  transform: rotate(180deg);
}

/* 通用节点IO展示样式 */
.workflow-node .node-io-collapsed {
  display: flex;
  justify-content: center;
  align-items: center;
}

.workflow-node .node-io-expanded {
  padding: 12px;
  background: linear-gradient(135deg, #fefbf0 0%, #fef3c7 100%);
  border-radius: 8px;
  border: 1px solid #fbbf24;
  animation: slideDown 0.3s ease-out;
}

.workflow-node .node-io-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 20px;
  cursor: pointer;
  color: #64748b;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.workflow-node .node-io-toggle-btn:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #94a3b8;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.workflow-node .node-io-toggle-btn.collapsed {
  background: linear-gradient(135deg, #fefbf0 0%, #fef3c7 100%);
  border-color: #fbbf24;
  color: #d97706;
}

.workflow-node .node-io-toggle-btn.collapsed:hover {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border-color: #f59e0b;
  color: #c2410c;
}

.workflow-node .node-io-toggle-btn.expanded {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border-color: #fbbf24;
  color: #d97706;
  font-size: 12px;
  padding: 6px 12px;
}

.workflow-node .node-io-toggle-btn.expanded:hover {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
  border-color: #f59e0b;
  color: #c2410c;
}

.workflow-node .node-io-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(251, 191, 36, 0.2);
}

.workflow-node .node-io-title {
  font-weight: 600;
  color: #78350f;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.workflow-node .node-io-title::before {
  content: "⚙️";
  font-size: 16px;
}

.workflow-node .node-io-data-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.workflow-node .node-io-data-container .data-section {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(251, 191, 36, 0.2);
  border-radius: 6px;
  padding: 10px;
  transition: all 0.2s ease;
}

.workflow-node .node-io-data-container .data-section:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(251, 191, 36, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(251, 191, 36, 0.1);
}

.workflow-node .node-io-data-container .data-section-header {
  margin-bottom: 8px;
}

.workflow-node .node-io-data-container .data-section-title {
  font-weight: 600;
  color: #78350f;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.workflow-node .node-io-data-container .data-content {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: 4px;
  padding: 8px;
  overflow-x: auto;
}

.workflow-node .node-io-data-container .data-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.4;
  color: #334155;
  white-space: pre-wrap;
  word-break: break-word;
}

.workflow-node .node-io-data-container .no-data-placeholder {
  padding: 12px;
  text-align: center;
  color: #94a3b8;
  font-style: italic;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 4px;
  border: 1px dashed rgba(148, 163, 184, 0.3);
}

/* 通用节点IO图标动画 */
.workflow-node .node-io-toggle-btn .toggle-icon {
  font-size: 16px;
  animation: pulse 2s ease-in-out infinite;
}

.workflow-node .node-io-toggle-btn .toggle-text {
  white-space: nowrap;
}

.workflow-node .node-io-toggle-btn .toggle-arrow {
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.workflow-node .node-io-toggle-btn .toggle-arrow.up {
  transform: rotate(180deg);
}

/* 下载文件区域样式 */
.download-files-section {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  animation: slideInUp 0.4s ease-out;
}

.download-files-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.download-files-header .section-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.download-files-header .section-title {
  flex: 1;
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  border: none;
  padding: 0;
}

.download-files-header .files-count {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.download-files-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.download-file-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.download-file-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.download-file-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #cbd5e1;
}

.file-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  position: relative;
  background: #f1f5f9;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.file-icon.file-type-pdf {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-color: #f87171;
  color: #dc2626;
}

.file-icon.file-type-document {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #60a5fa;
  color: #2563eb;
}

.file-icon.file-type-image {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #34d399;
  color: #059669;
}

.file-icon.file-type-spreadsheet {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: #4ade80;
  color: #16a34a;
}

.file-icon.file-type-presentation {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border-color: #fbbf24;
  color: #d97706;
}

.file-icon.file-type-archive {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  border-color: #a78bfa;
  color: #7c3aed;
}

.file-icon.file-type-video {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #60a5fa;
  color: #2563eb;
}

.file-icon.file-type-audio {
  background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%);
  border-color: #c084fc;
  color: #9333ea;
}

.file-icon.file-type-default {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-color: #94a3b8;
  color: #64748b;
}

.file-status-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-status-indicator.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.file-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 15px;
  word-break: break-word;
  line-height: 1.4;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.file-meta span {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 8px;
  font-weight: 500;
}

.file-size {
  background: #f1f5f9;
  color: #64748b;
}

.file-type {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-transform: uppercase;
}

.generation-time {
  background: #ecfdf5;
  color: #059669;
}

.file-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.file-actions button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.file-actions .download-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.file-actions .download-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.file-actions .preview-btn.secondary {
  background: #f8fafc;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.file-actions .preview-btn.secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #334155;
  transform: translateY(-1px);
}

.file-actions button svg {
  flex-shrink: 0;
}

/* 下载文件区域动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 文件卡片悬停动画 */
.download-file-card:hover .file-icon {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.download-file-card:hover .file-status-indicator {
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .workspace-section {
    flex: none;
    height: 50vh;
    max-width: 100%;
    min-width: auto;
  }
  
  .workspace-nav {
    flex-wrap: wrap;
    gap: 0.25rem;
  }
  
  .workspace-tab {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .workflow-node-bubble {
    padding: 10px 12px;
  }
  
  .node-header {
    gap: 8px;
  }
  
  .node-icon {
    width: 24px;
    height: 24px;
  }
  
  .icon-emoji {
    font-size: 14px;
  }
  
  .node-name {
    font-size: 13px;
  }
  
  .workflow-title {
    font-size: 11px;
  }
  
  /* 下载文件区域的响应式样式 */
  .download-files-section {
    margin-top: 16px;
    padding: 16px;
  }
  
  .download-files-header .section-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .download-files-header .section-title {
    font-size: 16px;
  }
  
  .download-file-card {
    padding: 12px 16px;
    gap: 12px;
  }
  
  .file-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .file-name {
    font-size: 14px;
  }
  
  .file-meta {
    gap: 8px;
  }
  
  .file-meta span {
    font-size: 11px;
    padding: 1px 6px;
  }
  
  .file-actions {
    flex-direction: column;
    gap: 6px;
  }
  
  .file-actions button {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style> 