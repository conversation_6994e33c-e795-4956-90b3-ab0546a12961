<template>
  <el-popover
    :width="200"
    trigger="click"
    v-model:visible="popoverVisible"
    popper-class="user-popover"
  >
    <template #reference>
      <div class="flex items-center justify-center w-12 h-12 mt-2 rounded-full cursor-pointer btn-primary">
        <IconUser class="w-6 h-6"/>
      </div>
    </template>
    
    <div class="p-4">
      <!-- 用户信息部分 -->
      <div v-if="userStore.userInfo" class="mb-4">
        <div class="mb-2 text-lg font-medium">
          {{ userStore.userInfo.nickName || userStore.userInfo.username }}
        </div>
        <div class="mb-1 text-sm text-gray-500">上次登录：</div>
        <div class="text-sm text-gray-600">
          {{ formatDate(userStore.userInfo.lastLoginTime) }}
        </div>
      </div>
      
      <!-- 分割线 -->
      <el-divider />
      
      <!-- 退出按钮 -->
      <el-button 
        type="danger" 
        class="w-full"
        :loading="loading"
        @click="handleLogout"
      >
        退出登录
      </el-button>
    </div>
  </el-popover>

  <!-- 登录弹窗 -->
  <LoginDialog v-model:visible="showLoginDialog" @login-success="handleLoginSuccess" />
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { logout } from '@/api/auth'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import IconUser from '@/assets/svg/user.svg'
import LoginDialog from '../LoginDialog.vue'
import { useUserStore } from '@/stores/user'
import {loginPassport} from "@/api/auth.js"
const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const popoverVisible = ref(false)
const showLoginDialog = ref(false)

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '暂无记录'
  return new Date(dateStr).toLocaleString()
}

// 处理登录成功
const handleLoginSuccess = async (res) => {
  await userStore.fetchUserInfo()
}

// 退出登录
const handleLogout = async () => {
  try {
    loading.value = true
    await logout()
    // 清除用户信息
    userStore.clearUserInfo()
    
    // 触发退出登录成功事件，通知管理端按钮隐藏
    window.dispatchEvent(new CustomEvent('logout-success'))
    
    // 关闭 popover
    popoverVisible.value = false
    ElMessage.success('退出登录成功')
    // 显示登录弹窗
    showLoginDialog.value = true
  } catch (error) {
    ElMessage.error('退出登录失败')
  } finally {
    loading.value = false
  }
}

// 处理用户信息更新事件
const handleUserInfoUpdate = () => {
  // 强制刷新用户信息
  userStore.fetchUserInfo()
}

// 组件挂载时获取用户信息并添加事件监听器
onMounted(() => {
  userStore.fetchUserInfo()
  
  // 监听用户信息更新事件
  window.addEventListener('user-info-updated', handleUserInfoUpdate)
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('user-info-updated', handleUserInfoUpdate)
})
</script>

<style>
.user-popover {
  padding: 0;
}
</style> 