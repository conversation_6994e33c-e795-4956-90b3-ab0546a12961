<template>
  <div class="w-28 bg-white menu-container">
    <!-- 顶部Logo -->
    <div class="flex flex-col items-center logo-container">
      <img src="@/assets/image/kebao.png" alt="科宝Logo" class="mb-4 w-18 h-18" />
      <div class="w-24 h-0.5 bg-primary"></div>
      <img src="@/assets/image/barTitle.png" class="mt-1 w-18 h-18" />
      <img src="@/assets/image/barTitle2.png" class="w-18 h-18" />
    </div>
    
    <!-- 菜单项 -->
    <div class="menu-items">
      <router-link to="/" class="menu-item group" :class="{ 'menu-active': isActive('/') }">
        <IconHome class="menu-icon"/>
        <span class="menu-txt">主页</span>
      </router-link>
      
      <router-link to="/work-center" class="menu-item group" :class="{ 'menu-active': isActive('/work-center') }">
        <div class="menu-icon">
          <IconWorkCenter class="menu-icon"/>
        </div>
        <span>工作台</span>
      </router-link>
      
      <router-link to="/agent" class="menu-item group" :class="{ 'menu-active': isActive('/agent') }">
        <div class="menu-icon">
          <IconAgent class="menu-icon"/>
        </div>
        <div>智能体</div>
        <div>广场</div>
      </router-link>
      
      <router-link to="/new-dialog" class="menu-item group" :class="{ 'menu-active': isActive('/new-dialog') }">
        <div class="menu-icon">
          <IconDialog class="menu-icon"/>
        </div>
        <span>新会话</span>
      </router-link>
      
<!-- 知识库菜单 -->
<div class="knowledge-menu">
        <div 
          class="cursor-pointer menu-item group" 
          :class="{ 'menu-active': isKnowledgeActive() }"
          @click="toggleKnowledgeMenu"
        >
          <div class="menu-icon">
            <IconDatabase class="menu-icon"/>
          </div>
          <span>知识库</span>
          <div class="expand-icon" :class="{ 'expanded': showKnowledgeSubmenu }">
            <svg width="8" height="8" viewBox="0 0 8 8" fill="currentColor">
              <path d="M1.5 3l2.5 2.5L6.5 3" stroke="currentColor" stroke-width="1" fill="none"/>
            </svg>
          </div>
        </div>
        
        <!-- 子菜单 -->
        <div class="submenu" :class="{ 'submenu-expanded': showKnowledgeSubmenu }">
          <router-link 
            to="/personal-database" 
            class="submenu-item"
            :class="{ 'submenu-active': isActive('/personal-database') }"
          >
            <span>个人知识库</span>
          </router-link>
          <router-link 
            to="/public-database" 
            class="submenu-item"
            :class="{ 'submenu-active': isActive('/public-database') }"
          >
            <span>部门知识库</span>
          </router-link>
        </div>
      </div>

      <router-link to="/history-dialog" class="menu-item group" :class="{ 'menu-active': isActive('/history-dialog') }">
        <div class="menu-icon">
          <IconHistory class="menu-icon"/>
        </div>
        <span>历史会话</span>
      </router-link>

    </div>
    
    <!-- 底部用户信息 -->
    <div class="flex flex-col items-center info-container">
      <!--<div class="flex justify-center items-center w-12 h-12 rounded-full btn-transparent text-txt-dark">
        <IconMessage class="w-6 h-6"/>
      </div>-->
      <!-- 管理员菜单 -->
      <div 
        v-if="userStore.userInfo?.type === 2" 
        class="cursor-pointer"
        @click="goToAdmin"
      >
        <!--<div class="rounded-full">
          <IconAdmin class="w-6 h-6 menu-icon text-txt-dark"/>
        </div>-->
      </div>
      <UserPopover />
      
      <!-- 操作手册按钮 -->
      <div class="mt-2">
        <button 
          class="manual-btn"
          @click="openManual"
          title="操作手册"
        >
          <div class="manual-icon">
            📖
          </div>
          <span class="manual-text">手册</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import IconMessage from '@/assets/svg/message.svg'
import IconUser from '@/assets/svg/user.svg'
import IconHome from '@/assets/svg/home.svg'
import IconWorkCenter from '@/assets/svg/workCenter.svg'
import IconAgent from '@/assets/svg/agent.svg'
import IconDialog from '@/assets/svg/dialog.svg'
import IconHistory from '@/assets/svg/history.svg'
import IconDatabase from '@/assets/svg/database.svg'
import UserPopover from './UserPopover.vue'
import IconAdmin from '@/assets/svg/admin.svg'

const userStore = useUserStore()
const route = useRoute()

const showKnowledgeSubmenu = ref(false)

// 添加跳转到管理后台的方法
const goToAdmin = () => {
  window.location.href = 'https://ai.gxtri.cn/manager/dashboard/Dashboard'
}

// 打开操作手册PDF
const openManual = () => {
  // 使用public目录中的PDF文件，可以直接通过URL访问
  const pdfUrl = '/科宝智能体中台操作手册.pdf'
  window.open(pdfUrl, '_blank')
}

const isActive = (path) => {
  return route.path === path
}

const isKnowledgeActive = () => {
  return route.path === '/personal-database' || route.path === '/public-database'
}

const toggleKnowledgeMenu = () => {
  showKnowledgeSubmenu.value = !showKnowledgeSubmenu.value
}

// 处理用户信息更新事件
const handleUserInfoUpdate = () => {
  // 强制刷新用户信息
  userStore.fetchUserInfo()
}

// 组件挂载时添加事件监听器
onMounted(() => {
  // 监听用户信息更新事件
  window.addEventListener('user-info-updated', handleUserInfoUpdate)
  
  // 如果当前路由是知识库相关页面，自动展开菜单
  if (isKnowledgeActive()) {
    showKnowledgeSubmenu.value = true
  }
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('user-info-updated', handleUserInfoUpdate)
})

console.log(userStore.userInfo)
</script>

<style scoped>
  .menu-container {
    box-shadow: 2px 0 6px 0 rgba(0,0,0,0.25);
    z-index: 100;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .logo-container {
    flex-shrink: 0;
    padding: 1rem 0;
  }
  .info-container {
    flex-shrink: 0;
    padding: 1rem 0;
  }
  .menu-items {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    width: 100%;
    min-height: 0;
    overflow-y: auto;
    padding: 0.5rem 0;
  }
  .menu-item {
    width: 4.5rem;
    height: 4.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #333;
    background: transparent;
    border-radius: 0.25rem;
    text-align: center;
    position: relative;
  }
  .menu-icon{
    width: 1.25rem;
    height: 1.25rem;
    margin-bottom: 2px;
  }
  .menu-active {
    color: #596DF4;
    background-color: rgba(89, 109, 244, 0.14);
  }
  .menu-txt {
    width: 100%;
    text-align: center;
  }

  /* 知识库菜单样式 */
  .knowledge-menu {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .expand-icon {
    position: absolute;
    bottom: 2px;
    right: 4px;
    transition: transform 0.2s ease;
    font-size: 8px;
  }

  .expand-icon.expanded {
    transform: rotate(180deg);
  }

  .submenu {
    width: 100%;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, margin-top 0.3s ease;
    margin-top: 0;
  }

  .submenu-expanded {
    max-height: 200px;
    margin-top: 0.5rem;
  }

  .submenu-item {
    width: 4rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    background: transparent;
    border-radius: 0.25rem;
    text-align: center;
    font-size: 0.75rem;
    margin: 0 auto 0.25rem auto;
    text-decoration: none;
    transition: all 0.2s ease;
  }

  .submenu-item:hover {
    color: #596DF4;
    background-color: rgba(89, 109, 244, 0.08);
  }

  .submenu-active {
    color: #596DF4;
    background-color: #EFF6FF;
  }

  /* 操作手册按钮样式 */
  .manual-btn {
    width: 4rem;
    height: 3rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
    background: transparent;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
  }

  .manual-btn:hover {
    color: #596DF4;
    background-color: rgba(89, 109, 244, 0.08);
  }

  .manual-icon {
    font-size: 1rem;
    margin-bottom: 1px;
  }

  .manual-text {
    font-size: 0.75rem;
    line-height: 1;
  }

  /* 滚动条样式 */
  .menu-container::-webkit-scrollbar,
  .menu-items::-webkit-scrollbar {
    width: 4px;
  }

  .menu-container::-webkit-scrollbar-track,
  .menu-items::-webkit-scrollbar-track {
    background: transparent;
  }

  .menu-container::-webkit-scrollbar-thumb,
  .menu-items::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }

  .menu-container::-webkit-scrollbar-thumb:hover,
  .menu-items::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }

  /* 为小屏幕优化 */
  @media screen and (max-height: 800px) {
    .logo-container {
      padding: 0.5rem 0;
    }
    
    .logo-container img {
      width: 3.5rem;
      height: 3.5rem;
    }
    
    .menu-items {
      gap: 0.5rem;
      padding: 0.25rem 0;
    }
    
    .menu-item {
      width: 4rem;
      height: 4rem;
    }
    
    .info-container {
      padding: 0.5rem 0;
    }
  }

  @media screen and (max-height: 600px) {
    .logo-container {
      padding: 0.25rem 0;
    }
    
    .logo-container img {
      width: 3rem;
      height: 3rem;
    }
    
    .menu-items {
      gap: 0.25rem;
    }
    
    .menu-item {
      width: 3.5rem;
      height: 3.5rem;
      font-size: 0.75rem;
    }
    
    .menu-icon {
      width: 1rem;
      height: 1rem;
    }
  }
</style>