<template>
  <div class="chat-container-pro-wrapper">
    <!-- 消息区域 -->
    <div class="messages-area" ref="messagesContainer">
      <!-- 消息内容 -->
      <div v-for="(message, index) in messages"
          :key="index"
          class="flex mb-6"
          :class="message.sender === 'ai' ? 'justify-start' : 'justify-end'"
      >
        <!-- AI 消息 -->
        <div v-if="message.sender === 'ai'" class="flex items-start gap-3 max-w-[100%] group relative">
          <div class="flex flex-col items-center gap-2">
            <div class="overflow-hidden flex-shrink-0 w-12 h-12 rounded-full">
              <img src="@/assets/image/kebao.png" alt="AI头像" class="object-cover w-full h-full" />
            </div>
            <!-- 工作流展开按钮移到头像下方 -->
            <div v-if="message.workflowNodes && message.workflowNodes.length > 0 && !message.workflowExpanded">
              <button
                @click="handleToggleWorkflowExpanded(message)"
                class="workflow-toggle-btn collapsed"
              >
                <span class="toggle-text">展开</span>
              </button>
            </div>
          </div>
          <div class="flex flex-col gap-3 flex-1 min-w-0">
            <!-- 工作流节点展示组件 -->
            <WorkflowNode
              :message="message"
              @toggle-workflow-expanded="handleToggleWorkflowExpanded"
              @toggle-knowledge-expanded="handleToggleKnowledgeExpanded"
            />

            <!-- AI回答内容 -->
            <div v-if="message.content || message.loading" class="ai-response-bubble">
              <div v-if="message.loading" class="flex gap-2 items-center">
                <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
              </div>
              <!-- 公文公告专用提纲消息框 -->
              <div v-else-if="isAnnouncementWorkflow && message.isOutline" class="outline-wrapper">
                <OutlineMessageBox
                  :content="message.rawContent || message.content"
                  :title="message.outlineTitle"
                  :message="message"
                  :editable="true"
                  @content-changed="handleOutlineContentChanged(message, $event)"
                  @generate-content="handleGenerateContentFromOutline(message, $event)"
                />
              </div>
              <!-- 可编辑内容区域 - 仅对公文公告工作流生效 -->
              <div v-else-if="isAnnouncementWorkflow && message.isEditable" class="relative" style="width: 100%;">
                <!-- 编辑模式 -->
                <div v-if="message.editMode" class="edit-content-wrapper">
                  <el-input
                    v-model="message.editableContent"
                    type="textarea"
                    placeholder="请编辑内容..."
                    class="edit-textarea"
                    :ref="`editTextarea_${index}`"
                    :style="{ minHeight: message.originalHeight || '200px' }"
                    :autosize="{ minRows: 6 }"
                  />
                  <div class="edit-actions mt-2 flex gap-2 justify-end">
                    <el-button size="small" @click="cancelEdit(message)">取消</el-button>
                    <el-button size="small" type="primary" @click="saveEdit(message)">保存</el-button>
                  </div>
                </div>
                <!-- 显示模式 -->
                <div v-else class="editable-content-wrapper">
                  <div
                    class="text-gray-800 ai-content"
                    v-html="message.content || ''"
                    :ref="`contentDiv_${index}`"
                  ></div>
                  <!-- 生成内容按钮 -->
                  <div class="generate-content-btn-wrapper">
                    <el-button
                      size="small"
                      type="primary"
                      class="generate-content-btn"
                      @click="handleGenerateContent(message)"
                    >
                      生成内容
                    </el-button>
                  </div>
                </div>
              </div>
              <!-- 普通显示模式 - 其他工作流或不可编辑的消息 -->
              <div v-else class="text-gray-800 ai-content" v-html="message.content || ''"></div>

              <!-- 参考文档引用展示组件 -->
              <ReferenceDocuments
                :documents="message.documents"
                @download-document="handleDocumentDownload"
              />
            </div>
          </div>

          <!-- 操作按钮组 -->
          <OperationToolbar
            :message="message"
            :message-index="index"
            :messages="messages"
            message-type="ai"
            :allow-regenerate="allowRegenerate"
            :workflow-id="workflowId"
            @regenerate-response="handleRegenerateFromToolbar"
            @edit-message="handleEditMessage"
          />
        </div>

        <!-- 用户消息 -->
        <div v-else class="flex items-start gap-2 max-w-[85%] group relative">
          <div class="p-4 rounded-lg shadow-md bg-primary">
            <div class="text-white" v-html="renderUserMessageContent(message)"></div>
          </div>
          <!-- 操作按钮组 -->
          <OperationToolbar
            :message="message"
            :message-index="index"
            :messages="messages"
            message-type="user"
          />
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-wrapper" :style="inputWrapperStyle">
      <!-- 收起/展开按钮 -->
      <div class="input-toggle-btn-wrapper">
        <button
          @click="toggleInputCollapse"
          class="input-toggle-btn"
          :class="{ 'collapsed': isInputCollapsed }"
          :title="isInputCollapsed ? '展开输入框' : '收起输入框'"
        >
                     <!-- 展开状态：向下箭头 -->
           <svg
             v-if="!isInputCollapsed"
             xmlns="http://www.w3.org/2000/svg"
             width="20"
             height="20"
             viewBox="0 0 24 24"
             fill="none"
             stroke="currentColor"
             stroke-width="2"
             stroke-linecap="round"
             stroke-linejoin="round"
             class="toggle-icon down-arrow"
           >
             <polyline points="6,9 12,15 18,9"></polyline>
           </svg>

           <!-- 收起状态：向上箭头 -->
           <svg
             v-else
             xmlns="http://www.w3.org/2000/svg"
             width="20"
             height="20"
             viewBox="0 0 24 24"
             fill="none"
             stroke="currentColor"
             stroke-width="2"
             stroke-linecap="round"
             stroke-linejoin="round"
             class="toggle-icon up-arrow"
           >
             <polyline points="18,15 12,9 6,15"></polyline>
           </svg>
          <span class="toggle-text">{{ isInputCollapsed ? '展开输入框' : '收起输入框' }}</span>
        </button>
      </div>

      <div
        class="flex relative flex-col p-4 bg-white rounded-lg shadow-md input-container"
        :class="{ 'collapsed': isInputCollapsed }"
        v-show="!isInputCollapsed"
      >
        <!--
        ==========================================
        动态对话框组件区域
        ==========================================
        这里使用动态组件来渲染不同类型的对话框
        支持的对话框类型：
        1. 'chat' - 普通对话框 (chat.vue)
        2. 'office' - 公文办公对话框 (officeChat.vue)

        扩展新对话框的步骤：
        1. 在 chatTypes 中添加新的对话框类型配置
        2. 创建新的对话框组件文件
        3. 在 components 中注册新组件
        4. 在 getCurrentChatType() 方法中添加判断逻辑
        5. 添加对应的事件处理方法
        -->
        <component
          :is="currentChatComponent"
          v-bind="currentChatProps"
          v-on="currentChatEvents"
        />
      </div>

    </div>

    <!-- 知识库选择弹框 -->
    <DatabaseSelection
      v-model="showKnowledgeSelection"
      :selected-ids="selectedKnowledgeList.map(item => item.id)"
      @confirm="handleKnowledgeSelection"
    />

    <!-- 预览组件 -->
    <Preview ref="preview" />
  </div>
</template>

<script>
import { marked } from 'marked'
import { handleSendMessagePro, getAgentDetail, handleWorkflowStreamRun } from '@/api/ai'
import { ElMessage } from 'element-plus'
import DatabaseSelection from './DatabaseSelection.vue'
import dayjs from 'dayjs'

// 引入对话框组件
import ChatInput from './chatcontainer/chat.vue'
import OfficeChatInput from './chatcontainer/officeChat.vue'
import OfficialAnnouncementInput from './chatcontainer/officialAnnouncement.vue'
import OperationToolbar from './chatcontainer/chatComponents/OperationToolbar.vue'
import WorkflowNode from './chatcontainer/chatComponents/WorkflowNode.vue'
import ReferenceDocuments from './chatcontainer/chatComponents/ReferenceDocuments.vue'
import Preview from './chatcontainer/chatComponents/Preview.vue'
import OutlineMessageBox from './chatcontainer/chatComponents/OutlineMessageBox.vue'

// 引入预览功能模块
import {
  previewFile,
  previewQuestionImage,
  previewMessageFile,
  previewReportFile,
  previewFileById,
  getQuestionImage,
  getFilePreviewUrl,
  getFileIcon,
  getFileType,
  renderUserMessageContent,
  escapeRegExp
} from './chatcontainer/chatComponents/PreviewUtils.js'

//
// ==========================================
// 对话框类型配置系统
// ==========================================
// 这里定义了所有支持的对话框类型及其配置
// 扩展新对话框时，请在此处添加新的配置项
//

// 对话框类型枚举
const CHAT_TYPES = {
  NORMAL: 'chat',           // 普通对话框
  OFFICE: 'office',         // 公文办公对话框
  ANNOUNCEMENT: 'announcement', // 公文公告对话框
  //
  // 扩展示例：
  // CUSTOM: 'custom',      // 自定义对话框
  // ANALYSIS: 'analysis',  // 分析对话框
  // WORKFLOW: 'workflow',  // 工作流对话框
  //
}

// 对话框配置映射
const CHAT_TYPE_CONFIG = {
  [CHAT_TYPES.NORMAL]: {
    component: 'ChatInput',
    name: '普通对话框',
    description: '支持文件上传、知识库选择、联网搜索等功能的通用对话框'
  },
  [CHAT_TYPES.OFFICE]: {
    component: 'OfficeChatInput',
    name: '公文办公对话框',
    description: '专门用于公文写作的对话框，包含表单字段和文件上传'
  },
  [CHAT_TYPES.ANNOUNCEMENT]: {
    component: 'OfficialAnnouncementInput',
    name: '公文公告对话框',
    description: '专门用于公文公告写作的对话框，包含公告专用表单字段和文件上传'
  },
  //
  // 扩展示例：
  // [CHAT_TYPES.CUSTOM]: {
  //   component: 'CustomChatInput',
  //   name: '自定义对话框',
  //   description: '自定义功能的对话框'
  // },
  //
}

export default {
  name: 'ChatContainerPro',
  components: {
    DatabaseSelection,
    // 注册对话框组件
    ChatInput,
    OfficeChatInput,
    OfficialAnnouncementInput,
    OperationToolbar,
    WorkflowNode,
    ReferenceDocuments,
    Preview,
    OutlineMessageBox,
    //
    // 扩展新对话框时，请在此处注册新组件：
    // CustomChatInput,
    // AnalysisChatInput,
    //
  },
  props: {
    agentId: {
      type: String,
      required: true
    },
    userId: {
      type: String,
      required: true
    },
    // 智能体名称
    agentName: {
      type: String,
      default: '科宝'
    },
    // 初始消息
    initialMessages: {
      type: Array,
      default: () => []
    },
    inputStyle: {
      type: Object,
      default: () => ({})
    },
    allowRegenerate: {
      type: Boolean,
      default: true
    },
    // 工作流ID，用于判断使用哪种对话框
    workflowId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 公文办公表单数据
      officeForm: {
        title: '',              // 标题
        subjectContent: '公告', // 写作类型，默认选中"公告"
        issuingUnit: '',        // 发文单位
        toPrimary: '',          // 主送机关
        createDate: new Date(), // 创建日期
        releaseDate: new Date(),// 发布日期
        documentNumber: '',     // 发文字号
        contentRequirements: '',// 正文内容要求
        referenceTemplate: [],  // 参考模板
        referenceMaterials: []  // 相关依据
      },

      // 公文公告表单数据
      announcementForm: {
        title: '',              // 标题
        subjectContent: '公告', // 主题内容，默认选中"公告"
        issuingUnit: '',        // 发文单位
        toPrimary: '',          // 主送机关
        documentNumber: '',     // 发文字号
        releaseDate: '',        // 发布日期
        createDate: '',         // 成文日期
        contentRequirements: '',// 内容要求
        referenceMaterials: [], // 内容依据文档
        referenceTemplate: []   // 格式参考文档
      },

      messages: [...this.initialMessages],
      userInput: '',
      isLoading: false,
      currentConversationId: '',
      uploadedFiles: [],
      activeModes: [], // 激活的模式（深度思考等）
      suggestedQuestions: [], // 预设问题列表
      showSearchMode: true, // 是否显示联网搜索按钮
      showKnowledgeSelectionButton: true, // 是否显示知识库选择按钮
      showFileUpload: true, // 是否显示文件上传按钮
      showKnowledgeSelection: false, // 控制知识库选择弹窗显示
      selectedKnowledgeList: [], // 选择的知识库列表
      selectedKnowledgeIds: [], // 选择的知识库ID列表
      selectedDocumentIds: [], // 选择的文档ID列表
      specialAgent: null, // 新增：特殊智能体标识
      isUserScrolling: false, // 用户是否正在手动滚动
      userScrollTimer: null, // 用户滚动计时器
      isInputCollapsed: false // 输入框是否收起
    }
  },
  computed: {
    inputWrapperStyle() {
      return {
        ...this.inputStyle
      }
    },
    uploadData() {
      return {
        userId: this.userId,
        agentId: this.agentId
      }
    },
    // 是否为公文公告工作流
    isAnnouncementWorkflow() {
      return this.workflowId === '1947848702138769410'
    },

    //
    // ==========================================
    // 动态对话框组件计算属性
    // ==========================================
    //

    /**
     * 获取当前对话框类型
     * 根据智能体ID或其他条件判断使用哪种对话框
     */
    currentChatType() {
      return this.getCurrentChatType()
    },

    /**
     * 获取当前对话框组件名称
     */
    currentChatComponent() {
      const config = CHAT_TYPE_CONFIG[this.currentChatType]
      return config ? config.component : 'ChatInput'
    },

    /**
     * 获取当前对话框组件的 props
     */
    currentChatProps() {
      const chatType = this.currentChatType

      if (chatType === CHAT_TYPES.OFFICE) {
        return {
          officeFormData: this.officeForm,
          uploadData: this.uploadData,
          isLoading: this.isLoading,
          userId: this.userId,
          agentId: this.agentId
        }
      } else if (chatType === CHAT_TYPES.ANNOUNCEMENT) {
        return {
          announcementFormData: this.announcementForm,
          uploadData: this.uploadData,
          isLoading: this.isLoading,
          userId: this.userId,
          agentId: this.agentId
        }
      } else {
        // 普通对话框的 props
        return {
          userInput: this.userInput,
          uploadedFiles: this.uploadedFiles,
          suggestedQuestions: this.suggestedQuestions,
          activeModes: this.activeModes,
          showSearchMode: this.showSearchMode,
          showKnowledgeSelectionButton: this.showKnowledgeSelectionButton,
          showFileUpload: this.showFileUpload,
          selectedKnowledgeList: this.selectedKnowledgeList,
          uploadData: this.uploadData,
          isLoading: this.isLoading,
          isReportAnalysisAgent: this.isReportAnalysisAgent()
        }
      }
    },

    /**
     * 获取当前对话框组件的事件监听器
     */
    currentChatEvents() {
      const chatType = this.currentChatType

      if (chatType === CHAT_TYPES.OFFICE) {
        return {
          'update-form-field': this.handleOfficeFormUpdate,
          'handle-tag-template-delete': this.handleTagTemplateDelete,
          'handle-tag-reference-delete': this.handleTagReferenceDelete,
          'preview-file': previewFile,
          'template-upload-success': this.handleTemplateUploadSuccess,
          'reference-upload-success': this.handleReferenceUploadSuccess,
          'upload-error': this.handleUploadError,
          'before-upload': this.beforeUpload,
          'handle-enter': this.handleEnterPress,
          'send-office-message': this.sendOfficeMessage,
          'preset-values-filled': this.handlePresetValuesFilled
        }
      } else if (chatType === CHAT_TYPES.ANNOUNCEMENT) {
        return {
          'update-form-field': this.handleAnnouncementFormUpdate,
          'handle-tag-template-delete': this.handleAnnouncementTagTemplateDelete,
          'handle-tag-reference-delete': this.handleAnnouncementTagReferenceDelete,
          'preview-file': previewFile,
          'template-upload-success': this.handleAnnouncementTemplateUploadSuccess,
          'reference-upload-success': this.handleAnnouncementReferenceUploadSuccess,
          'upload-error': this.handleUploadError,
          'before-upload': this.beforeUpload,
          'handle-enter': this.handleEnterPress,
          'send-announcement-message': this.sendAnnouncementMessage,
          'preset-values-filled': this.handlePresetValuesFilled
        }
      } else {
        // 普通对话框的事件
        return {
          'update:userInput': (value) => { this.userInput = value },
          'preview-file': previewFile,
          'remove-file': this.removeFile,
          'select-question': this.selectSuggestedQuestion,
          'preview-question-image': previewQuestionImage,
          'preview-report-file': previewReportFile,
          'handle-enter': this.handleEnterPress,
          'toggle-mode': this.toggleMode,
          'show-knowledge-selection': () => { this.showKnowledgeSelection = true },
          'upload-success': this.handleUploadSuccess,
          'upload-error': this.handleUploadError,
          'before-upload': this.beforeUpload,
          'send-message': this.sendMessage
        }
      }
    }
  },
  emits: ['message-sent', 'message-received', 'error', 'mode-changed', 'knowledge-selected', 'node-execution'],
  mounted() {
    this.$nextTick(() => {
      console.log('🎉 组件初始化完成，强制滚动到底部')
      this.forceScrollToBottom()
    })

    // 获取agent详情，设置预设问题
    if (this.agentId) {
      this.getAgentInfo()
    }



    // 将预览文件方法暴露到全局，以便在HTML中调用
    window.chatContainerPro = {
      previewMessageFile: previewMessageFile
    };

    // 监听滚动事件
    if (this.$refs.messagesContainer) {
      this.$refs.messagesContainer.addEventListener('scroll', this.handleUserScroll)
    }
  },
  beforeUnmount() {
    // 清理全局引用
    if (window.chatContainerPro) {
      delete window.chatContainerPro;
    }

    // 清理滚动事件监听器
    if (this.$refs.messagesContainer) {
      this.$refs.messagesContainer.removeEventListener('scroll', this.handleUserScroll)
    }

    // 清理计时器
    if (this.userScrollTimer) {
      clearTimeout(this.userScrollTimer)
    }
  },
  methods: {
    //
    // ==========================================
    // 对话框类型管理方法
    // ==========================================
    //

    /**
     * 获取当前对话框类型
     * 根据工作流ID或智能体ID判断使用哪种对话框
     *
     * 扩展新对话框时，请在此方法中添加判断逻辑：
     * if (this.workflowId === 'your-workflow-id') {
     *   return CHAT_TYPES.YOUR_NEW_TYPE
     * }
     */
    getCurrentChatType() {
      // 优先根据工作流ID判断
      if (this.workflowId) {
        // 公文办公工作流ID：1945775535429709826
        if (this.workflowId === '1945775535429709826') {
          return CHAT_TYPES.OFFICE
        }
        // 公文公告工作流ID：1947848702138769410
        if (this.workflowId === '1947848702138769410') {
          return CHAT_TYPES.ANNOUNCEMENT
        }
      }

      // 兼容旧的智能体ID判断方式
      if (this.agentId === '799592ca740609d28525d878d539fdc1') {
        return CHAT_TYPES.OFFICE
      }

      //
      // 扩展示例：
      // if (this.workflowId === 'analysis-workflow-id') {
      //   return CHAT_TYPES.ANALYSIS
      // }
      // if (this.workflowId === 'custom-workflow-id') {
      //   return CHAT_TYPES.CUSTOM
      // }
      //

      // 默认使用普通对话框
      return CHAT_TYPES.NORMAL
    },

    /**
     * 处理公文表单字段更新
     */
    handleOfficeFormUpdate({ field, value }) {
      this.officeForm[field] = value
    },

    /**
     * 处理公文公告表单字段更新
     */
    handleAnnouncementFormUpdate({ field, value }) {
      this.announcementForm[field] = value
    },

    /**
     * 处理预设值填入事件
     */
    handlePresetValuesFilled(presetFormData) {
      // 更新表单数据
      this.officeForm = { ...presetFormData }
    },

    /**
     * 处理模板文件上传成功
     */
    handleTemplateUploadSuccess(response, file) {
      this.handleUploadSuccess(response, file, 'template')
    },
    /**
     * 发送公文办公消息
     * @param {Object} externalFormData - 可选的外部表单数据，如果提供则使用该数据，否则使用组件内部的表单数据
     */
    sendOfficeMessage(externalFormData = null) {
      if (this.isLoading) return

      // 使用传入的表单数据或组件内部的表单数据
      const currentFormData = externalFormData || this.officeForm

      // 验证必要字段
      if (!currentFormData.title || !currentFormData.contentRequirements) {
        ElMessage.warning('请填写标题和写作要求')
        return
      }

      this.isLoading = true

      // 如果传入了外部表单数据，先更新组件内部的表单数据
      if (externalFormData) {
        Object.keys(externalFormData).forEach(key => {
          if (this.officeForm.hasOwnProperty(key)) {
            this.officeForm[key] = externalFormData[key]
          }
        })
      }

      // 格式化表单数据
      let formData = {...currentFormData}
      
      // 统一日期字段名称和格式
      if (formData.documentDate && dayjs(formData.documentDate).isValid()) {
        formData.createDate = dayjs(formData.documentDate).format('YYYY-MM-DD')
        formData.releaseDate = dayjs(formData.documentDate).format('YYYY-MM-DD')
      } else if (formData.createDate || formData.releaseDate) {
        // 如果有单独的createDate或releaseDate，保持原值但确保格式正确
        if (formData.createDate && dayjs(formData.createDate).isValid()) {
          formData.createDate = dayjs(formData.createDate).format('YYYY-MM-DD')
        }
        if (formData.releaseDate && dayjs(formData.releaseDate).isValid()) {
          formData.releaseDate = dayjs(formData.releaseDate).format('YYYY-MM-DD')
        }
      } else {
        // 只有在完全没有日期时才使用当前日期
        formData.createDate = dayjs().format('YYYY-MM-DD')
        formData.releaseDate = dayjs().format('YYYY-MM-DD')
      }

      // 处理文件引用
      if (formData.referenceTemplate && formData.referenceTemplate.length > 0) {
        formData.referenceTemplateFileIds = formData.referenceTemplate.map(file => file.fileId)
      }
      if (formData.referenceMaterials && formData.referenceMaterials.length > 0) {
        formData.referenceMaterialFileIds = formData.referenceMaterials.map(file => file.fileId)
      }

      // 构建用户消息内容
      let messageContent = `请帮我写一篇${formData.subjectContent}：\n\n`
      messageContent += `标题：${formData.title}\n`
      if (formData.documentNumber) messageContent += `发文字号：${formData.documentNumber}\n`
      if (formData.issuingUnit) messageContent += `发文单位：${formData.issuingUnit}\n`
      if (formData.toPrimary) messageContent += `主送机关：${formData.toPrimary}\n`
      messageContent += `成文日期：${formData.createDate}\n`
      messageContent += `发布日期：${formData.releaseDate}\n`
      messageContent += `\n写作要求：\n${formData.contentRequirements}`

      // 添加用户消息
      const userMessage = {
        sender: 'user',
        content: messageContent
      }
      this.messages.push(userMessage)

      // 清空表单数据（注意：现在清空的是父组件的表单）
      this.resetOfficeForm()

      // 强制滚动到底部
      this.forceScrollToBottom()

      // 添加AI消息占位
      const aiMessage = {
        sender: 'ai',
        content: '',
        loading: true,
        workflowNodes: []
      }
      this.messages.push(aiMessage)

      // 调用流式工作流接口，直接传递格式化后的表单数据
      handleWorkflowStreamRun('1945775535429709826', formData, {
        onWorkflowEvent: (eventData) => {
          this.handleWorkflowEvent(eventData)
        },
        onMessageFlow: (text, messageId, conversationId) => {
          this.handleMessageFlow(text, messageId, conversationId)
        },
        onCompleted: (text, metadata) => {
          this.handleCompleted(text, metadata)
        },
        onError: (error) => {
          this.handleError(error)
        }
      })
    },

    /**
     * 发送公文公告消息
     * @param {Object} formData - 可选的表单数据，如果提供则使用该数据，否则使用组件内部的表单数据
     */
    sendAnnouncementMessage(formData = null) {
      if (this.isLoading) return

      // 使用传入的表单数据或组件内部的表单数据
      const currentFormData = formData || this.announcementForm

      // 验证必要字段
      if (!currentFormData.title || !currentFormData.contentRequirements) {
        ElMessage.warning('请填写标题和内容要求')
        return
      }

      this.isLoading = true

      // 如果传入了外部表单数据，先更新组件内部的表单数据
      if (formData) {
        Object.keys(formData).forEach(key => {
          if (this.announcementForm.hasOwnProperty(key)) {
            this.announcementForm[key] = formData[key]
          }
        })
      }

      // 格式化表单数据
      let processedFormData = {...currentFormData}

      // 处理文件引用
      if (processedFormData.referenceTemplate && processedFormData.referenceTemplate.length > 0) {
        processedFormData.referenceTemplateFileIds = processedFormData.referenceTemplate.map(file => file.fileId)
      }
      if (processedFormData.referenceMaterials && processedFormData.referenceMaterials.length > 0) {
        processedFormData.referenceMaterialFileIds = processedFormData.referenceMaterials.map(file => file.fileId)
      }

      // 构建用户消息内容
      let messageContent = `请根据以下信息生成公文公告：\n\n`
      if (processedFormData.subjectContent) messageContent += `主题内容：${processedFormData.subjectContent}\n`
      messageContent += `标题：${processedFormData.title}\n`
      if (processedFormData.documentNumber) messageContent += `发文字号：${processedFormData.documentNumber}\n`
      if (processedFormData.issuingUnit) messageContent += `发文单位：${processedFormData.issuingUnit}\n`
      if (processedFormData.toPrimary) messageContent += `主送机关：${processedFormData.toPrimary}\n`
      if (processedFormData.releaseDate) messageContent += `发布日期：${processedFormData.releaseDate}\n`
      if (processedFormData.createDate) messageContent += `成文日期：${processedFormData.createDate}\n`
      messageContent += `\n内容要求：\n${processedFormData.contentRequirements}`

      // 添加用户消息
      const userMessage = {
        sender: 'user',
        content: messageContent
      }
      this.messages.push(userMessage)

      // 清空表单数据
      this.resetAnnouncementForm()

      // 强制滚动到底部
      this.forceScrollToBottom()

      // 添加AI消息占位
      const aiMessage = {
        sender: 'ai',
        content: '',
        loading: true,
        workflowNodes: []
      }
      this.messages.push(aiMessage)

      // 调用流式工作流接口，使用公文公告工作流ID
      handleWorkflowStreamRun('1947848702138769410', processedFormData, {
        onWorkflowEvent: (eventData) => {
          this.handleWorkflowEvent(eventData)
        },
        onMessageFlow: (text, messageId, conversationId) => {
          this.handleMessageFlow(text, messageId, conversationId)
        },
        onCompleted: (text, metadata) => {
          this.handleCompleted(text, metadata)
        },
        onError: (error) => {
          this.handleError(error)
        }
      })
    },

    /**
     * 重置公文办公表单
     */
    resetOfficeForm() {
      this.officeForm = {
        title: '',
        subjectContent: '公告',
        issuingUnit: '',
        toPrimary: '',
        createDate: new Date(),
        releaseDate: new Date(),
        documentNumber: '',
        contentRequirements: '',
        referenceTemplate: [],
        referenceMaterials: []
      }
    },

    /**
     * 重置公文公告表单
     */
    resetAnnouncementForm() {
      this.announcementForm = {
        title: '',
        subjectContent: '公告',
        issuingUnit: '',
        toPrimary: '',
        documentNumber: '',
        releaseDate: '',
        createDate: '',
        contentRequirements: '',
        referenceMaterials: [],
        referenceTemplate: []
      }
    },



    /**
     * 处理参考模板文件删除
     */
    handleTagTemplateDelete(file) {
      this.officeForm.referenceTemplate = this.officeForm.referenceTemplate.filter(f => f.fileId !== file.fileId)
      this.uploadedFiles = this.uploadedFiles.filter(f => f.fileId !== file.fileId)
    },

    /**
     * 处理相关参考文件删除
     */
    handleTagReferenceDelete(file) {
      this.officeForm.referenceMaterials = this.officeForm.referenceMaterials.filter(f => f.fileId !== file.fileId)
      this.uploadedFiles = this.uploadedFiles.filter(f => f.fileId !== file.fileId)
    },

    /**
     * 处理公文公告参考模板文件删除
     */
    handleAnnouncementTagTemplateDelete(file) {
      this.announcementForm.referenceTemplate = this.announcementForm.referenceTemplate.filter(f => f.fileId !== file.fileId)
      this.uploadedFiles = this.uploadedFiles.filter(f => f.fileId !== file.fileId)
    },

    /**
     * 处理公文公告相关参考文件删除
     */
    handleAnnouncementTagReferenceDelete(file) {
      this.announcementForm.referenceMaterials = this.announcementForm.referenceMaterials.filter(f => f.fileId !== file.fileId)
      this.uploadedFiles = this.uploadedFiles.filter(f => f.fileId !== file.fileId)
    },

    /**
     * 截断文件名以保证显示美观
     */
    truncateFileName(fileName, maxLength = 12) {
      if (!fileName) return ''

      // 如果文件名长度小于等于最大长度，直接返回
      if (fileName.length <= maxLength) {
        return fileName
      }

      // 获取文件扩展名
      const dotIndex = fileName.lastIndexOf('.')
      const extension = dotIndex !== -1 ? fileName.substring(dotIndex) : ''
      const nameWithoutExt = dotIndex !== -1 ? fileName.substring(0, dotIndex) : fileName

      // 计算可用于显示文件名的长度（预留扩展名和省略号的空间）
      const availableLength = maxLength - extension.length - 3 // 3 是省略号的长度

      if (availableLength <= 0) {
        // 如果可用长度不够，只显示省略号和扩展名
        return '...' + extension
      }

      // 截断文件名并添加省略号
      return nameWithoutExt.substring(0, availableLength) + '...' + extension
    },

    /**
     * 处理相关参考文件上传成功
     */
    handleReferenceUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = getFileType(file.name)

        const fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name,
          raw: file.raw,
          size: file.size
        }

        this.officeForm.referenceMaterials.push(fileInfo)

        ElMessage.success({
          message: '文件上传成功',
          duration: 2000
        })
      } else {
        ElMessage.error('文件上传失败')
      }
    },

    /**
     * 处理公文公告模板文件上传成功
     */
    handleAnnouncementTemplateUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = getFileType(file.name)

        const fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name,
          raw: file.raw,
          size: file.size
        }

        this.announcementForm.referenceTemplate.push(fileInfo)

        ElMessage.success({
          message: '格式参考文档上传成功',
          duration: 2000
        })
      } else {
        ElMessage.error('文件上传失败')
      }
    },

    /**
     * 处理公文公告相关参考文件上传成功
     */
    handleAnnouncementReferenceUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = getFileType(file.name)

        const fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name,
          raw: file.raw,
          size: file.size
        }

        this.announcementForm.referenceMaterials.push(fileInfo)

        ElMessage.success({
          message: '内容依据文档上传成功',
          duration: 2000
        })
      } else {
        ElMessage.error('文件上传失败')
      }
    },

    /**
     * 文件上传前验证
     */
    beforeUpload(file) {
      // 检查是否已经上传了文件
      if (this.uploadedFiles.length >= 1) {
        ElMessage.error('每次对话只能上传一个文件!');
        return false;
      }

      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        ElMessage.error('文件大小不能超过 50MB!');
        return false;
      }
      return true;
    },

    /**
     * 文件上传成功处理
     */
    handleUploadSuccess(response, file, uploadType) {
      if (response.code === 200) {
        const { type, extension } = getFileType(file.name);

        const fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name,
          raw: file.raw,
          size: file.size
        };

        if (uploadType === 'template') {
          this.officeForm.referenceTemplate.push(fileInfo);
        } else {
          // 将文件信息添加到数组中
          this.uploadedFiles.push(fileInfo);
        }

        ElMessage.success({
          message: '文件上传成功',
          duration: 2000
        });
      } else {
        ElMessage.error('文件上传失败');
      }
    },

    /**
     * 文件上传失败处理
     */
    handleUploadError() {
      ElMessage.error('文件上传失败，请重试');
    },





    /**
     * 处理回车按键
     */
    handleEnterPress(event) {
      if (event.shiftKey) {
        return
      }

      if (this.getCurrentChatType() === CHAT_TYPES.OFFICE) {
        if (this.officeForm.title && this.officeForm.contentRequirements) {
          this.sendOfficeMessage()
        }
      } else if (this.getCurrentChatType() === CHAT_TYPES.ANNOUNCEMENT) {
        if (this.announcementForm.title && this.announcementForm.contentRequirements) {
          this.sendAnnouncementMessage()
        }
      } else {
        if (this.userInput.trim()) {
          this.sendMessage()
        }
      }
    },

    /**
     * 删除文件
     */
    removeFile(index) {
      this.uploadedFiles.splice(index, 1);
    },

    sendMessage(messageContent = null, questionFiles = []) {
      // 基本验证：如果没有内容且没有文件，或者正在加载中，则不发送
      if ((!this.userInput.trim() && !messageContent && this.uploadedFiles.length === 0 && questionFiles.length === 0) || this.isLoading) return

      this.isLoading = true

      // 准备消息内容
      let messageText = messageContent || this.userInput.trim();

      // 检查是否是事件对象，防止传递错误的消息内容
      if (messageText && typeof messageText === 'object' && messageText.isTrusted !== undefined) {
        console.error('错误：传递了事件对象而不是文本内容', messageText);
        messageText = this.userInput.trim();
      } else if (typeof messageText === 'object' && !(messageText instanceof Array)) {
        // 如果是其他对象但不是数组，转为JSON字符串
        console.warn('警告：尝试发送对象作为消息', messageText);
        messageText = JSON.stringify(messageText);
      }

      // 准备文件信息，合并上传的文件和预设问题的文件
      const files = [
        ...this.uploadedFiles.map(file => ({
          fileId: file.fileId,
          fileType: file.fileType,
          extension: file.extension,
          fileName: file.fileName
        })),
        ...questionFiles.map(file => ({
          fileId: file.fileId,
          fileType: file.fileType,
          extension: file.extension,
          fileName: file.fileName
        }))
      ];

      // 如果有文件，添加分割线和文件列表
      if (this.uploadedFiles.length > 0 || questionFiles.length > 0) {
        const uploadedFilesList = this.uploadedFiles
          .map(file => `${getFileIcon(file.fileType)} ${file.fileName}`)
          .join('\n');
        const questionFilesList = questionFiles
          .map(file => `${getFileIcon(file.fileType)} ${file.fileName}`)
          .join('\n');

        const allFiles = [uploadedFilesList, questionFilesList].filter(Boolean).join('\n');
        if (allFiles) {
          messageText += `\n\n附件:\n${allFiles}`;
        }
      }

      // 添加用户消息
      const userMessage = {
        sender: 'user',
        content: messageText,
        files: files  // 保存文件信息用于点击预览
      }
      this.messages.push(userMessage)
      console.log('添加用户消息:', userMessage)
      console.log('当前消息列表:', this.messages)

      // 检查是否启用深度思考和联网搜索
      const deepResearch = this.activeModes.includes('deep');
      const onlineSearch = this.activeModes.includes('search');

      // 准备知识库和文档ID信息
      let knowledgeIds = [...(this.selectedKnowledgeIds || [])];
      let documentIds = [...(this.selectedDocumentIds || [])];

      console.log('发送消息参数 - 深度思考:', deepResearch, '联网搜索:', onlineSearch)
      console.log('发送消息参数 - 知识库IDs:', knowledgeIds, '文档IDs:', documentIds)

      // 如果没有单独的ID列表，则从selectedKnowledgeList中提取
      if (knowledgeIds.length === 0 && documentIds.length === 0 && this.selectedKnowledgeList.length > 0) {
        this.selectedKnowledgeList.forEach(item => {
          if (item.type === 'knowledge_folder') {
            knowledgeIds.push(item.id);
          } else if (item.type === 'file') {
            documentIds.push(item.id);
            // 如果是文件，还要添加其所属的知识库ID
            if (item.datasetId && !knowledgeIds.includes(item.datasetId)) {
              knowledgeIds.push(item.datasetId);
            }
          }
        });
      }

      // 发射消息发送事件
      this.$emit('message-sent', {
        message: userMessage,
        files,
        deepResearch,
        onlineSearch,
        knowledgeIds,
        documentIds,
        activeModes: [...this.activeModes]
      });

      // 清空输入框和文件列表
      this.userInput = ''
      this.uploadedFiles = []

      // 强制滚动到底部，确保用户消息可见
      this.forceScrollToBottom()

      // 添加AI消息占位
      const aiMessage = {
        sender: 'ai',
        content: '',
        loading: true,
        workflowNodes: []
      }
      this.messages.push(aiMessage)
      console.log('添加AI消息占位:', aiMessage)
      console.log('当前消息列表:', this.messages)

      // 检查是否是公文办公相关的消息，如果是则调用工作流接口
      const isOfficeMessage = this.checkIfOfficeMessage(userMessage.content, files)

      if (isOfficeMessage) {
        console.log('检测到公文办公消息，调用工作流接口')

        // 构建工作流参数
        const workflowData = this.buildWorkflowData(userMessage.content, files, {
          deepResearch,
          onlineSearch,
          knowledgeIds,
          documentIds
        })

        // 使用工作流接口发送消息
        handleWorkflowStreamRun(workflowData.workflowId, workflowData.variables, {
          onWorkflowEvent: (eventData) => {
            // 处理工作流事件
            this.handleWorkflowEvent(eventData)
          },
          onMessageFlow: (text, messageId, conversationId) => {
            // 处理消息流
            this.handleMessageFlow(text, messageId, conversationId)
          },
          onCompleted: (text, metadata) => {
            // 完成处理
            this.handleCompleted(text, metadata)
          },
          onError: (error) => {
            // 错误处理
            this.handleError(error)
          }
        })
      } else {
        console.log('普通消息，调用标准接口')

        // 发送消息
        handleSendMessagePro(userMessage.content, {
          agentId: this.agentId,
          userId: this.userId,
          conversationId: this.currentConversationId,
          files: files,
          deepResearch: deepResearch, // 深度思考参数
          onlineSearch: onlineSearch, // 联网搜索参数
          knowledgeIds: knowledgeIds, // 知识库ID列表
          documentIds: documentIds, // 文档ID列表
          inputs: {}, // 默认输入参数
          onWorkflowEvent: (eventData) => {
            // 处理工作流事件
            this.handleWorkflowEvent(eventData)
          },
          onMessageFlow: (text, messageId, conversationId) => {
            // 处理消息流
            this.handleMessageFlow(text, messageId, conversationId)
          },
          onCompleted: (text, metadata) => {
            // 完成处理
            this.handleCompleted(text, metadata)
          },
          onError: (error) => {
            // 错误处理
            this.handleError(error)
          }
        })
      }
    },

    // 检查数据是否有效（不为空且不是空JSON）
    isValidData(data) {
      if (!data) return false

      if (typeof data === 'string') {
        if (data.trim() === '' || data === '{}' || data === '[]') return false
        try {
          const parsed = JSON.parse(data)
          if (typeof parsed === 'object' && parsed !== null) {
            if (Array.isArray(parsed)) return parsed.length > 0
            return Object.keys(parsed).length > 0
          }
          return true
        } catch {
          return true // 如果不是JSON，当作普通字符串
        }
      }

      if (typeof data === 'object' && data !== null) {
        if (Array.isArray(data)) return data.length > 0
        return Object.keys(data).length > 0
      }

      return true
    },

    handleWorkflowEvent(eventData) {
      console.log('处理工作流事件:', eventData)

      const aiMessageIndex = this.messages.length - 1
      if (aiMessageIndex < 0) return

      const aiMessage = this.messages[aiMessageIndex]

      // 确保workflowNodes存在
      if (!aiMessage.workflowNodes) {
        aiMessage.workflowNodes = []
      }

      // 默认收起工作流节点，用户可以手动展开查看执行过程
      if (aiMessage.workflowExpanded === undefined) {
        aiMessage.workflowExpanded = false
      }

      // 查找是否已存在该节点
      const existingNodeIndex = aiMessage.workflowNodes.findIndex(
        node => node.nodeType === eventData.nodeType
      )

      // 创建节点对象 - 保留完整的eventData数据，并增强显示信息
      const nodeData = {
        // 基础字段
        nodeName: eventData.nodeName,
        displayName: eventData.displayName || eventData.nodeName, // 使用解析器提供的显示名称
        workflowTitle: eventData.workflowTitle,
        nodeType: eventData.nodeType,
        nodeStatus: eventData.nodeStatus || eventData.status, // 兼容两种字段名
        workflowId: eventData.workflowId,
        isExpanded: false,  // 默认收起状态

        // 增强字段（来自解析器）
        category: eventData.category || 'general',
        workflowType: eventData.workflowType || 'unknown',
        timestamp: eventData.timestamp || new Date().toISOString(),

        // 关键：保留完整的输入输出数据
        parameters: eventData.parameters || null,
        result: eventData.result || null,

        // 保留调试信息
        debugInfo: eventData.debugInfo || null,

        // 保留所有其他字段
        ...eventData
      }

      // 发送事件到工作空间组件（用于右侧工作空间显示）
      this.$emit('node-execution', {
        id: Date.now() + Math.random(),
        name: nodeData.displayName || nodeData.nodeName,
        type: nodeData.nodeType,
        status: nodeData.nodeStatus || eventData.status || 'running',
        category: nodeData.category,
        workflowType: nodeData.workflowType,
        timestamp: nodeData.timestamp,
        parameters: nodeData.parameters,
        result: nodeData.result
      })



      // 如果是知识库节点且有结果数据，解析文档信息
      if (this.isKnowledgeNodeByName(eventData.nodeName) && eventData.result) {
        try {
          const resultData = JSON.parse(eventData.result)
          if (resultData.documents && Array.isArray(resultData.documents)) {
            nodeData.documents = resultData.documents
            // 保持默认收起状态，用户可手动展开查看文档
          }
        } catch (error) {
          console.error('解析知识库节点结果失败:', error)
        }
      }

      if (existingNodeIndex !== -1) {
        // 更新现有节点状态，保留已有的documents数据和所有字段
        const existingNode = aiMessage.workflowNodes[existingNodeIndex]

        // 智能合并parameters和result字段：
        // - 保留有效的parameters（通常来自START状态）
        // - 保留有效的result（通常来自END状态）
        const mergedParameters = this.isValidData(nodeData.parameters) ? nodeData.parameters :
                                (this.isValidData(existingNode.parameters) ? existingNode.parameters : null)
        const mergedResult = this.isValidData(nodeData.result) ? nodeData.result :
                           (this.isValidData(existingNode.result) ? existingNode.result : null)

        aiMessage.workflowNodes[existingNodeIndex] = {
          ...existingNode,
          ...nodeData,
          // 特别处理parameters和result字段
          parameters: mergedParameters,
          result: mergedResult,
          // 特别保留documents数据（如果存在）
          documents: existingNode.documents || nodeData.documents
        }

              } else {
        // 添加新节点
        aiMessage.workflowNodes.push(nodeData)
      }
    },

    handleMessageFlow(text, messageId, conversationId) {
      console.log('🔄 处理消息流:', { text, messageId, conversationId, textLength: text?.length })
      const aiMessageIndex = this.messages.length - 1
      if (aiMessageIndex < 0) {
        console.warn('⚠️ 没有找到AI消息，无法更新内容')
        return
      }

      const aiMessage = this.messages[aiMessageIndex]
      aiMessage.loading = false

      // 保存原始内容
      aiMessage.rawContent = text

      console.log('📝 更新AI消息内容:', {
        messageIndex: aiMessageIndex,
        contentLength: text?.length,
        hasContent: !!text
      })

      // 发送消息接收事件到工作空间
      this.$emit('message-received', {
        content: text,
        messageId,
        conversationId,
        timestamp: new Date().toISOString()
      })

      // 初始化thinking相关状态（如果还没有初始化）
      if (!aiMessage.thinkingState) {
        aiMessage.thinkingState = {
          isInThinking: false,
          thinkingContent: '',
          actualContent: '',
          hasThinkingStarted: false
        };
      }

      const state = aiMessage.thinkingState;

      // 检查是否开始thinking
      if (text.includes('<think>') && !state.hasThinkingStarted) {
        console.log('🎯 开始thinking模式');
        state.isInThinking = true;
        state.hasThinkingStarted = true;

        // 提取<think>标签之前的内容和之后的内容
        const thinkStart = text.indexOf('<think>');
        const beforeThink = text.substring(0, thinkStart);
        const afterThinkStart = text.substring(thinkStart + 7); // 7是'<think>'的长度

        // 初始化状态
        state.actualContent = beforeThink;
        state.thinkingContent = afterThinkStart;

        // 显示thinking过程（展开状态）
        const thinkingHtml = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>Thinking...</summary>\n${state.thinkingContent}\n</details>`;
        aiMessage.content = this.parseMarkdown(state.actualContent + thinkingHtml);
      }
      // 如果正在thinking中
      else if (state.isInThinking) {
        // 检查是否结束thinking
        if (text.includes('</think>')) {
          console.log('🎯 结束thinking模式');
          state.isInThinking = false;

          // 提取</think>标签之前和之后的内容
          const thinkEnd = text.indexOf('</think>');
          const beforeThinkEnd = text.substring(0, thinkEnd);
          const afterThinkEnd = text.substring(thinkEnd + 8); // 8是'</think>'的长度

          // 更新thinking内容（完整的思考过程）
          state.thinkingContent = beforeThinkEnd;
          // 开始accumulate实际内容
          state.actualContent += afterThinkEnd;

          // 显示完整的thinking过程（保持展开状态）和实际内容
          const thinkingHtml = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>Thinking...</summary>\n${state.thinkingContent}\n</details>`;
          aiMessage.content = this.parseMarkdown(thinkingHtml + state.actualContent);
        } else {
          // 继续累积thinking内容（这里text是完整的累积内容）
          // 需要提取thinking部分
          const thinkStart = text.indexOf('<think>');
          if (thinkStart !== -1) {
            const beforeThink = text.substring(0, thinkStart);
            const afterThinkStart = text.substring(thinkStart + 7);

            state.actualContent = beforeThink;
            state.thinkingContent = afterThinkStart;

            // 实时更新thinking显示（展开状态）
            const thinkingHtml = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>Thinking...</summary>\n${state.thinkingContent}\n</details>`;
            aiMessage.content = this.parseMarkdown(state.actualContent + thinkingHtml);
          }
        }
      }
      // 如果thinking已结束，继续追加实际内容
      else if (state.hasThinkingStarted && !state.isInThinking) {
        // 提取thinking之前的内容和thinking之后的内容
        const thinkStart = text.indexOf('<think>');
        const thinkEnd = text.indexOf('</think>');

        if (thinkStart !== -1 && thinkEnd !== -1) {
          // 如果text中还包含完整的think标签，提取实际内容
          const beforeThink = text.substring(0, thinkStart);
          const afterThink = text.substring(thinkEnd + 8);
          state.actualContent = beforeThink + afterThink;
        } else {
          // 如果text中没有think标签，说明是纯正文内容
          state.actualContent = text;
        }

        // 保持thinking展开状态，显示实际内容
        const thinkingHtml = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>Thinking...</summary>\n${state.thinkingContent}\n</details>`;
        aiMessage.content = this.parseMarkdown(thinkingHtml + state.actualContent);
      }
      // 处理其他情况（原有逻辑）
      else if (text.includes('Thinking...')) {
        const thinkingContent = text.match(/<details[^>]*>([\s\S]*?)<\/details>/)?.[1] || '';
        if (!thinkingContent.trim() || thinkingContent.includes('</summary>') && thinkingContent.split('</summary>')[1].trim() === '') {
          const newText = text.replace(/<details[^>]*>[\s\S]*?<\/details>/, '');
          aiMessage.content = this.parseMarkdown(newText
            .replace(/<\/details>\n+/, '</details>')); // 处理 details 标签后的换行
        } else {
          aiMessage.content = this.parseMarkdown(text
            .replace('<details open', '<details')
            .replace(/<\/details>\n+/, '</details>'));
        }
      } else if (text.includes('<think>')) {
        // 处理 <think> 标签（旧逻辑，保留作为兜底）
        console.log('🎯 ChatContainerPro检测到think标签')
        console.log('🎯 原始文本:', text)

        // 提取thinking内容
        const thinkingMatch = text.match(/<think>([\s\S]*?)<\/think>/);
        if (thinkingMatch) {
          const thinkingContent = thinkingMatch[1];
          console.log('🎯 thinking内容:', thinkingContent)

          // 获取thinking后面的实际内容
          const afterThinking = text.split('</think>')[1] || '';
          console.log('🎯 实际内容:', afterThinking)

          // 将thinking内容包装为details格式以便UI显示（保持展开状态）
          const wrappedThinking = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>Thinking...</summary>\n${thinkingContent}\n</details>`;
          const finalText = wrappedThinking + afterThinking;

          console.log('🎯 最终文本:', finalText)
          aiMessage.content = this.parseMarkdown(finalText);
        } else {
          // 如果没有完整的think标签，直接显示原文本
          aiMessage.content = this.parseMarkdown(text.replace(/^\n+/, ''));
        }
      } else {
        console.log('📄 处理普通消息内容:', {
          originalText: text,
          cleanedText: text.replace(/^\n+/, ''),
          textLength: text?.length
        });
        aiMessage.content = this.parseMarkdown(text.replace(/^\n+/, ''));
        console.log('✅ 设置AI消息内容完成:', {
          contentLength: aiMessage.content?.length,
          hasContent: !!aiMessage.content
        });
      }

      aiMessage.messageId = messageId

      if (conversationId) {
        this.currentConversationId = conversationId
      }

      console.log('更新后的AI消息:', aiMessage)
      console.log('当前所有消息:', this.messages)

      // 自动滚动到底部，实时跟踪AI消息更新
      this.autoScrollToBottom()

      // 备用滚动方案 - 确保即使主方案失败也能滚动
      this.$nextTick(() => {
        setTimeout(() => {
          this.forceScrollToBottom()
        }, 100)
      })
    },

    handleCompleted(text, metadata) {
      this.isLoading = false

      if (metadata && metadata.retriever_resources) {
        const aiMessageIndex = this.messages.length - 1
        this.messages[aiMessageIndex].documents = metadata.retriever_resources
      }

      // 为公文公告工作流的AI消息添加可编辑标识和提纲检测
      if (this.isAnnouncementWorkflow) {
        const aiMessageIndex = this.messages.length - 1
        if (aiMessageIndex >= 0) {
          const aiMessage = this.messages[aiMessageIndex]
          const rawContent = aiMessage.rawContent || text

          // 检测是否为提纲内容
          if (this.isOutlineContent(rawContent)) {
            aiMessage.isOutline = true
            aiMessage.outlineTitle = this.extractOutlineTitle(rawContent)
          } else {
            aiMessage.isEditable = true
            aiMessage.editMode = false
            aiMessage.editableContent = this.extractTextFromHtml(rawContent)
          }
        }
      }

      // AI消息完成后，确保滚动到底部显示完整内容
      console.log('✅ AI消息完成，触发自动滚动')
      this.autoScrollToBottom()
    },

    handleError(error) {
      this.isLoading = false

      const aiMessageIndex = this.messages.length - 1
      if (aiMessageIndex >= 0) {
        const aiMessage = this.messages[aiMessageIndex]
        aiMessage.loading = false
        aiMessage.content = '抱歉，出现了一些错误，请稍后重试。'

        // 确保错误消息可见
        console.log('❌ 发生错误，触发自动滚动')
        this.autoScrollToBottom()
      }

      this.$emit('error', {
        type: 'api',
        message: '发送消息失败',
        error
      })
    },



    // 处理来自OperationToolbar的重新生成事件
    handleRegenerateFromToolbar(userMessage) {
      if (!this.allowRegenerate) return

      this.userInput = userMessage
      this.sendMessage()
    },



    parseMarkdown(content) {
      if (!content) return ''
      try {
        let processedContent = content.replace(/(\d+)\.(\s|$)/g, '$1\\.$2')
        processedContent = processedContent.replace(/!\[([^\]]*)\]\(\/files\//g, '![$1](https://ai.gxgeq.com/files/')
        processedContent = processedContent.replace(/(<\/[^>]+>)([^\n\r])/g, '$1\n\n$2')
        
        // 处理一级标题：#1、#2、#3等格式，保持为一级标题
        processedContent = processedContent.replace(/^#(\d+)\s+(.+)$/gm, '# $1. $2')
        
        // 处理二级标题：##(1)、##(2)等格式，保持为二级标题
        processedContent = processedContent.replace(/^##\((\d+)\)(.+)$/gm, '## ($1)$2')
        
        // 确保标题前后有适当的空行
        processedContent = processedContent.replace(/^(#{1,2})/gm, '\n$1')
        processedContent = processedContent.replace(/(#{1,2}.*$)/gm, '$1\n')

        return marked(processedContent, {
          breaks: true,
          sanitize: false,
          gfm: true,
          pedantic: false,
        })
      } catch (e) {
        console.error('Markdown parsing error:', e)
        return content
      }
    },



    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        const container = this.$refs.messagesContainer
        this.$nextTick(() => {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          })
        })
      }
    },

    /**
     * 自动滚动到底部 - 只有当用户没有手动滚动时才执行
     */
    autoScrollToBottom() {
      // 如果用户正在手动滚动，则不自动滚动
      if (this.isUserScrolling) {
        return
      }

      if (this.$refs.messagesContainer) {
        const container = this.$refs.messagesContainer
        this.$nextTick(() => {
          // 检查是否已经接近底部（允许一些误差）
          const isNearBottom = container.scrollTop >= container.scrollHeight - container.clientHeight - 50

          console.log('📏 滚动检查:', {
            scrollTop: container.scrollTop,
            scrollHeight: container.scrollHeight,
            clientHeight: container.clientHeight,
            isNearBottom: isNearBottom
          })

          // 只有当用户在底部附近时才自动滚动
          if (isNearBottom) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth'
            })
          }
        })
      } else {
        console.log('❌ messagesContainer 引用不存在')
      }
    },

    /**
     * 处理用户滚动事件
     */
    handleUserScroll() {
      if (this.$refs.messagesContainer) {
        const container = this.$refs.messagesContainer

        // 设置用户正在滚动的标记
        this.isUserScrolling = true
        console.log('👆 用户开始滚动')

        // 清除之前的计时器
        if (this.userScrollTimer) {
          clearTimeout(this.userScrollTimer)
        }

        // 500ms后重置滚动标记
        this.userScrollTimer = setTimeout(() => {
          this.isUserScrolling = false
          console.log('⏰ 用户滚动计时器重置')

          // 如果用户滚动到底部附近，重新启用自动滚动
          const isNearBottom = container.scrollTop >= container.scrollHeight - container.clientHeight - 50
          if (isNearBottom) {
            this.isUserScrolling = false
            console.log('🎯 用户滚动到底部，重新启用自动滚动')
          }
        }, 500)
      }
    },

    /**
     * 强制滚动到底部（用于发送新消息等场景）
     */
    forceScrollToBottom() {
      console.log('🚀 强制滚动到底部')
      this.isUserScrolling = false // 重置滚动标记
      this.scrollToBottom()
    },



    /**
     * 切换模式（深度思考等）
     */
    toggleMode(mode) {
      const index = this.activeModes.indexOf(mode);
      if (index === -1) {
        // 如果模式不在数组中，添加它
        this.activeModes.push(mode);
      } else {
        // 如果模式已经在数组中，移除它
        this.activeModes.splice(index, 1);
      }

      // 发射模式变化事件
      this.$emit('mode-changed', {
        mode,
        active: this.activeModes.includes(mode),
        activeModes: [...this.activeModes]
      });
    },
    /**
     * 处理文档下载事件
     */
    handleDocumentDownload(docGroup) {
      // 可以在这里添加额外的下载处理逻辑
      console.log('文档下载完成:', docGroup);
    },





    /**
     * 选择预设问题
     */
    async selectSuggestedQuestion(question, index) {
      // 兼容新旧格式：question可能是对象或字符串
      const questionText = typeof question === 'object' ? (question.content || question.description || question) : question;
      const questionFileId = typeof question === 'object' ? question.fileId : null;

      this.userInput = questionText;

      // 准备预设问题的图片文件
      let questionFiles = [];

      // 检查是否是检测报告分析智能体的特殊预设问题
      if ((this.specialAgent === 'report_analysis' || this.agentId === '81b027e060dcf65c68054c16577ee0f4') &&
          typeof question === 'object' && question.fileName) {

        console.log('检测到检测报告分析智能体预设问题，准备上传文件:', question.fileName);

        // 显示加载状态
        const loadingMessage = ElMessage({
          message: '正在上传文件，请稍候...',
          type: 'info',
          duration: 0, // 不自动消失
          showClose: false
        });

        try {
          // 自动上传文件
          const fileId = await this.uploadFileFromPublic(question.fileName);

          // 关闭加载消息
          loadingMessage.close();

          if (fileId) {
            // 获取文件类型
            const { type, extension } = getFileType(question.fileName);

            questionFiles.push({
              fileId: fileId,
              fileType: type,
              extension: extension,
              fileName: question.fileName
            });

            console.log('文件上传成功，准备发送消息:', questionFiles);
            ElMessage.success('文件上传成功');
          } else {
            ElMessage.error('文件上传失败，将发送纯文本消息');
          }
        } catch (error) {
          // 关闭加载消息
          loadingMessage.close();
          console.error('上传文件时发生错误:', error);
          ElMessage.error('文件上传失败，将发送纯文本消息');
        }
      } else if (questionFileId && questionFileId.trim()) {
        // 处理普通预设问题的图片
        questionFiles.push({
          fileId: questionFileId,
          fileType: 'IMAGE', // 假设预设问题中的文件都是图片
          extension: 'jpg', // 默认扩展名，可根据实际情况调整
          fileName: `预设问题图片${index + 1}` // 提供一个默认文件名
        });
      }

      // 直接发送消息，将文件信息传递给sendMessage
      this.sendMessage(null, questionFiles);
    },

    /**
     * 检查是否是公文办公相关的消息
     */
    checkIfOfficeMessage(messageContent, files) {
      // 检查消息内容是否包含公文办公相关的关键词
      const officeKeywords = [
        '标题：', '发文字号：', '发文单位：', '主送机关：', '成文日期：',
        '写作要求：', '请根据', '起草一份', '通知', '公告', '通报'
      ]

      const hasOfficeKeywords = officeKeywords.some(keyword =>
        messageContent.includes(keyword)
      )

      // 检查是否来自DocumentOffice页面
      const fromDocumentOffice = this.$route.query.fromDocumentOffice === 'true'

      // 检查是否有公文办公相关的文件
      const hasOfficeFiles = files.some(file =>
        file.fileName && (
          file.fileName.includes('通知') ||
          file.fileName.includes('公告') ||
          file.fileName.includes('发展和改革委员会')
        )
      )

      console.log('公文办公检查结果:', {
        hasOfficeKeywords,
        fromDocumentOffice,
        hasOfficeFiles,
        messageContent: messageContent.substring(0, 100) + '...'
      })

      return hasOfficeKeywords || fromDocumentOffice || hasOfficeFiles
    },

    /**
     * 构建工作流数据
     */
    buildWorkflowData(messageContent, files, options = {}) {
      const { deepResearch, onlineSearch, knowledgeIds, documentIds } = options

      // 尝试从消息内容中解析公文信息
      const parsedData = this.parseOfficeMessageContent(messageContent)
      console.log('解析后的公文信息:', parsedData)

      // 构建工作流变量
      const variables = {
        // 基本字段
        title: parsedData.title || messageContent.split('\n')[0] || messageContent.substring(0, 50),
        subjectContent: parsedData.subjectContent || "通知",
        issuingUnit: parsedData.issuingUnit || "",
        toPrimary: parsedData.toPrimary || "",
        createDate: (parsedData.createDate && dayjs(parsedData.createDate).isValid()) ? dayjs(parsedData.createDate).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        releaseDate: (parsedData.releaseDate && dayjs(parsedData.releaseDate).isValid()) ? dayjs(parsedData.releaseDate).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        documentNumber: parsedData.documentNumber || "",
        contentRequirements: parsedData.contentRequirements || messageContent,

        // 文件相关字段
        referenceTemplate: [],
        referenceMaterials: files || [],
        referenceMaterialFileIds: files ? files.map(file => file.fileId) : [],
      }

      return {
        workflowId: "1945775535429709826",
        variables: variables
      }
    },

    /**
     * 解析公文办公消息内容
     */
    parseOfficeMessageContent(messageContent) {
      const result = {}

      // 解析标题
      const titleMatch = messageContent.match(/标题：(.+?)(?:\n|$)/)
      if (titleMatch) result.title = titleMatch[1].trim()

      // 解析写作类型
      const validTypes = ['通知', '公告', '通报', '演讲稿']
      const subjectMatch = messageContent.match(/请帮我写一篇(.+?)：/) ||
                          messageContent.match(/请根据.*起草一份(.+?)/)
      if (subjectMatch) {
        const matched = subjectMatch[1].trim()
        // 在匹配的文本中查找有效的类型
        const foundType = validTypes.find(type => matched.includes(type))
        if (foundType) {
          result.subjectContent = foundType
        }
      }

      // 解析发文字号
      const docNumberMatch = messageContent.match(/发文字号：(.+?)(?:\n|$)/)
      if (docNumberMatch) result.documentNumber = docNumberMatch[1].trim()

      // 解析发文单位
      const issuingUnitMatch = messageContent.match(/发文单位：(.+?)(?:\n|$)/)
      if (issuingUnitMatch) result.issuingUnit = issuingUnitMatch[1].trim()

      // 解析主送机关
      const toPrimaryMatch = messageContent.match(/主送机关：(.+?)(?:\n|$)/)
      if (toPrimaryMatch) result.toPrimary = toPrimaryMatch[1].trim()

      // 解析成文日期
      const createDateMatch = messageContent.match(/成文日期：(.+?)(?:\n|$)/)
      if (createDateMatch) result.createDate = createDateMatch[1].trim()

      // 解析写作要求
      const requirementsMatch = messageContent.match(/写作要求：([\s\S]+?)(?:\n\n|$)/)
      if (requirementsMatch) result.contentRequirements = requirementsMatch[1].trim()

      return result
    },

    /**
     * 获取agent详情
     */
    async getAgentInfo() {
      try {
        // 正常获取agent详情
        const response = await getAgentDetail({ agentId: this.agentId });
        if (response && response.data) {
          // 根据agentType解析预设问题数据结构
          this.parseSuggestedQuestions(response.data.agentType, response.data.suggestedQuestions || []);

          // 如果onlineSearch为0，则不显示联网搜索按钮
          this.showSearchMode = response.data.onlineSearch;

          // 如果selectKnow为0，则不显示知识库选择按钮
          this.showKnowledgeSelectionButton = response.data.selectKnow !== 0;

          // 如果fileUpload为false，则不显示文件上传按钮
          this.showFileUpload = response.data.fileUpload !== false;

          console.log('联网搜索开关:', this.showSearchMode);
          console.log('知识库选择开关:', this.showKnowledgeSelectionButton);
          console.log('文件上传开关:', this.showFileUpload);
        }

        // 如果是检测报告分析智能体，设置特殊的预设问题（在获取基本配置后）
        if (this.specialAgent === 'report_analysis' || this.agentId === '81b027e060dcf65c68054c16577ee0f4') {
          this.setReportAnalysisQuestions()
        }
      } catch (error) {
        console.error('Failed to get agent detail:', error);
      }
    },

    /**
     * 解析预设问题数据结构
     */
    parseSuggestedQuestions(agentType, suggestedQuestions) {
      if (!suggestedQuestions || !Array.isArray(suggestedQuestions)) {
        this.suggestedQuestions = [];
        return;
      }

      try {
        //现在是对象数组格式
        this.suggestedQuestions = suggestedQuestions.map(questionItem => {
          // 如果是字符串，尝试解析JSON（兼容旧格式）
          if (typeof questionItem === 'string') {
            try {
              const parsed = JSON.parse(questionItem);
              return {
                content: parsed.content || parsed.description || '',
                fileId: parsed.fileId || null,
                key: parsed.key || ''
              };
            } catch (parseError) {
              console.warn('解析预设问题JSON失败:', parseError, questionItem);
              // 如果解析失败，作为普通字符串处理
              return {
                content: questionItem,
                fileId: null,
                key: ''
              };
            }
          } else if (typeof questionItem === 'object' && questionItem !== null) {
            // 如果是对象，直接使用（新格式）
            return {
              content: questionItem.content || questionItem.description || '',
              fileId: questionItem.fileId || null,
              key: questionItem.key || ''
            };
          } else {
            // 其他情况，转为字符串处理
            return {
              content: String(questionItem),
              fileId: null,
              key: ''
            };
          }
        }).filter(item => item.content); // 过滤掉空内容的项目

      } catch (error) {
        console.error('解析预设问题数据失败:', error);
        this.suggestedQuestions = [];
      }

      console.log('解析后的预设问题:', this.suggestedQuestions);
    },

    /**
     * 为检测报告分析智能体设置特殊的预设问题
     */
    setReportAnalysisQuestions() {
      this.suggestedQuestions = [
        {
          content: '分析该份检测报告存在的问题',
          fileId: null, // 将在点击时动态上传文件
          fileName: '混凝土试块抗渗性能检测报告.pdf',
          filePath: 'public/混凝土试块抗渗性能检测报告.pdf',
          key: 'concrete_report'
        },
        {
          content: '分析该份检测报告存在的问题',
          fileId: null, // 将在点击时动态上传文件
          fileName: '砂浆试块抗压强度检验报告.pdf',
          filePath: 'public/砂浆试块抗压强度检验报告.pdf',
          key: 'mortar_report'
        }
      ];

      // 设置其他配置
      this.showSearchMode = true;
      this.showKnowledgeSelectionButton = true;

      console.log('设置检测报告分析智能体预设问题:', this.suggestedQuestions);
    },

    /**
     * 从public目录上传文件
     */
    async uploadFileFromPublic(fileName) {
      try {
        // 先获取文件内容
        const response = await fetch(`/${fileName}`);
        if (!response.ok) {
          throw new Error(`无法获取文件: ${fileName}`);
        }

        const blob = await response.blob();

        // 创建FormData
        const formData = new FormData();
        formData.append('file', blob, fileName);
        formData.append('userId', this.userId);
        formData.append('agentId', this.agentId);

        // 调用上传接口
        const uploadResponse = await fetch('/api/v1/public/file/upload', {
          method: 'POST',
          body: formData
        });

        const uploadResult = await uploadResponse.json();

        if (uploadResult.code === 200) {
          console.log('文件上传成功:', uploadResult);
          return uploadResult.data.fileId;
        } else {
          throw new Error(uploadResult.msg || '文件上传失败');
        }
      } catch (error) {
        console.error('上传文件失败:', error);
        ElMessage.error(`上传文件失败: ${error.message}`);
        return null;
      }
    },















    /**
     * 处理知识库选择
     */
    handleKnowledgeSelection(selection) {
      this.selectedKnowledgeList = selection.list
      this.selectedKnowledgeIds = selection.knowledgeIds || []
      this.selectedDocumentIds = selection.documentIds || []
      console.log('选择的知识库:', selection)
      console.log('知识库IDs:', this.selectedKnowledgeIds)
      console.log('文档IDs:', this.selectedDocumentIds)

      // 发射知识库选择事件
      this.$emit('knowledge-selected', {
        selectedKnowledgeList: this.selectedKnowledgeList,
        selectedKnowledgeIds: this.selectedKnowledgeIds,
        selectedDocumentIds: this.selectedDocumentIds
      });
    },

    /**
     * 处理工作流展开/收起事件
     */
    handleToggleWorkflowExpanded(message) {
      message.workflowExpanded = !message.workflowExpanded;
    },

    /**
     * 处理知识库节点展开/收起事件
     */
    handleToggleKnowledgeExpanded(node, nodeIndex) {
      node.isExpanded = !node.isExpanded;
    },

    /**
     * 通过节点名称判断是否为知识库节点
     * 这个方法需要在 handleWorkflowEvent 中使用，所以保留在父组件中
     */
    isKnowledgeNodeByName(nodeName) {
      if (!nodeName) return false;
      const knowledgeKeywords = ['知识库', 'knowledge', '检索', 'retriever', '召回', 'search'];
      const lowerNodeName = nodeName.toLowerCase();
      return knowledgeKeywords.some(keyword =>
        lowerNodeName.includes(keyword.toLowerCase())
      );
    },

    /**
     * 判断是否为检测报告分析智能体
     */
    isReportAnalysisAgent() {
      return this.specialAgent === 'report_analysis' || this.agentId === '81b027e060dcf65c68054c16577ee0f4';
    },

    /**
     * 渲染用户消息内容 - 调用导入的函数
     */
    renderUserMessageContent(message) {
      return renderUserMessageContent(message);
    },





    /**
     * 切换输入框收起/展开状态
     */
    toggleInputCollapse() {
      this.isInputCollapsed = !this.isInputCollapsed;

      // 如果展开了输入框，延迟一下再滚动到底部，确保动画完成
      if (!this.isInputCollapsed) {
        setTimeout(() => {
          this.forceScrollToBottom();
        }, 300);
      }
    },

    /**
     * 处理编辑消息事件
     */
    handleEditMessage(message, messageIndex) {
      if (!message.isEditable) return

      // 先计算原内容的高度
      const contentDiv = this.$refs[`contentDiv_${messageIndex}`]
      let originalHeight = 200 // 默认最小高度

      if (contentDiv) {
        // contentDiv可能是数组也可能是单个元素
        const element = Array.isArray(contentDiv) ? contentDiv[0] : contentDiv
        if (element && element.offsetHeight) {
          originalHeight = Math.max(element.offsetHeight, 200)
        }
      }

      // 保存原始高度
      message.originalHeight = originalHeight + 'px'

      // 确保editableContent有值
      if (!message.editableContent) {
        message.editableContent = this.extractTextFromHtml(message.rawContent || message.content)
      }

      // 进入编辑模式
      message.editMode = true

      // 在下一个tick中调整编辑框高度
      this.$nextTick(() => {
        setTimeout(() => {
          const textarea = this.$refs[`editTextarea_${messageIndex}`]
          if (textarea) {
            // textarea可能是数组也可能是单个元素
            const textareaComponent = Array.isArray(textarea) ? textarea[0] : textarea
            if (textareaComponent && textareaComponent.$el) {
              const textareaEl = textareaComponent.$el.querySelector('textarea')
              if (textareaEl) {
                // 自动调整高度以适应内容
                textareaEl.style.height = 'auto'
                const newHeight = Math.max(textareaEl.scrollHeight, originalHeight)
                textareaEl.style.height = newHeight + 'px'
              }
            }
          }
        }, 100) // 稍微延迟确保DOM已更新
      })
    },

    /**
     * 取消编辑
     */
    cancelEdit(message) {
      message.editMode = false
      // 恢复原始内容
      message.editableContent = this.extractTextFromHtml(message.rawContent || message.content)
    },

    /**
     * 保存编辑
     */
    saveEdit(message) {
      // 更新消息内容
      message.content = this.parseMarkdown(message.editableContent)
      message.editMode = false

      // 可以在这里添加保存到服务器的逻辑
      ElMessage.success('内容已保存')
    },

    /**
     * 处理生成内容按钮点击
     */
    handleGenerateContent(message) {
      // 这里先留空，按照需求说明
      console.log('生成内容按钮被点击', message)
      ElMessage.info('生成内容功能待实现')
    },

    /**
     * 从HTML中提取纯文本内容
     */
    extractTextFromHtml(htmlContent) {
      if (!htmlContent) return ''

      // 创建一个临时div元素来解析HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlContent

      // 获取纯文本内容
      return tempDiv.textContent || tempDiv.innerText || ''
    },

    /**
     * 检测内容是否为提纲格式
     */
    isOutlineContent(content) {
      if (!content) return false

      // 提取纯文本内容
      const textContent = this.extractTextFromHtml(content)

      // 检测是否包含多个标题标识符
      const headerPattern = /^(#{1,6})\s*(\d+\.?)?\s*(\([^)]+\))?\s*(.+)$/gm
      const matches = textContent.match(headerPattern)

      // 如果有3个或以上的标题，认为是提纲
      return matches && matches.length >= 3
    },

    /**
     * 提取提纲标题
     */
    extractOutlineTitle(content) {
      if (!content) return ''

      const textContent = this.extractTextFromHtml(content)
      const lines = textContent.split('\n').filter(line => line.trim())

      // 查找第一个一级标题作为提纲标题
      for (const line of lines) {
        const match = line.match(/^#\s*(\d+\.?)?\s*(\([^)]+\))?\s*(.+)$/)
        if (match) {
          return match[3].trim()
        }
      }

      return '公文提纲'
    },

    /**
     * 处理提纲内容变更
     */
    handleOutlineContentChanged(message, newContent) {
      // 更新消息的原始内容
      message.rawContent = newContent
      message.content = this.parseMarkdown(newContent)
    },

    /**
     * 处理从提纲生成内容
     */
    handleGenerateContentFromOutline(message, outlineContent) {
      console.log('从提纲生成内容:', outlineContent)
      ElMessage.info('基于提纲生成文档功能待实现')

      // 这里可以调用API生成完整文档
      // 例如：
      // this.generateDocumentFromOutline(outlineContent)
    }
  },
  watch: {
    initialMessages: {
      handler(newMessages) {
        // 只在初始化或清空时更新
        if (this.messages.length === 0 || newMessages.length === 0) {
          this.messages = [...newMessages];
        }
      },
      deep: true
    },
    'messages.length'() {
      // 当消息数量变化时（通常是新增消息），强制滚动到底部
      console.log('📈 消息数量变化，强制滚动到底部')
      this.forceScrollToBottom()
    }
  },
  beforeUnmount() {
    // 清理全局引用
    if (window.chatContainerPro) {
      delete window.chatContainerPro;
    }

    // 清理滚动事件监听器
    if (this.$refs.messagesContainer) {
      this.$refs.messagesContainer.removeEventListener('scroll', this.handleUserScroll)
    }

    // 清理计时器
    if (this.userScrollTimer) {
      clearTimeout(this.userScrollTimer)
    }
  }
}
</script>

<style scoped>
.chat-container-pro-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
}

.messages-area {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scroll-behavior: smooth;
  min-height: 0;
  height: 100%;
}

.messages-area::-webkit-scrollbar {
  width: 0;
}

.messages-area {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.input-wrapper {
  position: sticky;
  bottom: 0;
  width: 100%;
  z-index: 20;
  background: linear-gradient(to top, rgba(255,255,255,1) 70%, rgba(255,255,255,0.9) 90%, transparent);
}

.input-container {
  min-height: 200px;
  max-height: 400px;
  display: flex;
  flex-direction: column;
}


/* AI回答气泡样式 */
.ai-response-bubble {
  padding: 16px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.ai-content {
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
}

/* 提纲消息框样式 */
.outline-wrapper {
  margin: 0;
  padding: 0;
}

/* 可编辑内容样式 */
.editable-content-wrapper {
  position: relative;
}

.generate-content-btn-wrapper {
  position: absolute;
  bottom: -45px;
  right: 0px;
  z-index: 10;
}

.generate-content-btn {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 编辑模式样式 */
.edit-content-wrapper {
  margin-top: 8px;
  width: 100%;
}

.edit-textarea {
  width: 100%;
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  padding: 12px;
  resize: vertical;
  transition: height 0.3s ease;
}

/* 确保编辑框内的textarea也有相同的样式 */
.edit-textarea :deep(.el-textarea__inner) {
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  padding: 12px;
  resize: vertical;
  transition: height 0.3s ease;
}

.edit-textarea:focus,
.edit-textarea :deep(.el-textarea__inner):focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.edit-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 8px;
}

/* 加载动画 */
.loading-spinner svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 完成状态动画 */
.completed-icon {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 预设问题图片样式 */
.question-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.question-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 工作流展开按钮样式 - 仅用于头像下方的展开按钮 */
.workflow-toggle-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 16px;
  cursor: pointer;
  color: #64748b;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.workflow-toggle-btn:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #94a3b8;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.workflow-toggle-btn.collapsed {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
  border-color: #c7d2fe;
  color: #6366f1;
}

.workflow-toggle-btn.collapsed:hover {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  border-color: #a5b4fc;
  color: #4f46e5;
}

.toggle-text {
  white-space: nowrap;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 输入框收起/展开按钮样式 */
.input-toggle-btn-wrapper {
  display: flex;
  justify-content: center;
  z-index: 21;
  position: relative;
}

.input-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 2px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  color: #64748b;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  justify-content: center;
}

.input-toggle-btn:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-color: #cbd5e1;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.input-toggle-btn.collapsed {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-color: #0ea5e9;
  color: #0369a1;
}

.input-toggle-btn.collapsed:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #0284c7;
  color: #0c4a6e;
}

.input-toggle-btn .toggle-icon {
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.input-toggle-btn .down-arrow {
  animation: bounceDown 0.3s ease-out;
}

.input-toggle-btn .up-arrow {
  animation: bounceUp 0.3s ease-out;
}

@keyframes bounceDown {
  0% {
    transform: translateY(-3px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(1px);
    opacity: 0.9;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceUp {
  0% {
    transform: translateY(3px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-1px);
    opacity: 0.9;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.input-toggle-btn .toggle-text {
  white-space: nowrap;
  transition: opacity 0.3s ease;
}

/* 输入容器收起状态 */
.input-container.collapsed {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

/* 输入容器展开动画 */
.input-container {
  transition: all 0.3s ease;
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  .input-toggle-btn {
    padding: 6px 12px;
    font-size: 12px;
    gap: 6px;
    min-width: 100px;
  }

  .input-toggle-btn .toggle-icon {
    width: 16px;
    height: 16px;
  }

  .input-toggle-btn .toggle-text {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .input-toggle-btn {
    padding: 5px 10px;
    font-size: 11px;
    gap: 4px;
    min-width: 80px;
  }

  .input-toggle-btn .toggle-icon {
    width: 14px;
    height: 14px;
  }

  .input-toggle-btn .toggle-text {
    display: none; /* 在小屏幕上隐藏文字，只显示图标 */
  }
}

</style>