<template>
  <div class="chat-section">
    <div class="chat-wrapper">
      <chat-container
        :agent-id="agentId"
        :agent-name="agentName"
        :user-id="userId"
        :initial-messages="initialMessages"
        :conversation-id="conversationId"
        @update:conversation-id="$emit('update:conversation-id', $event)"
        @error="$emit('error', $event)"
        @message-sent="$emit('message-sent', $event)"
        @message-received="$emit('message-received', $event)"
        @node-execution="$emit('node-execution', $event)"
        @process-update="$emit('process-update', $event)"
        ref="chatContainer"
      />
    </div>
  </div>
</template>

<script>
import ChatContainer from '@/components/ChatContainerPro.vue'

export default {
  name: 'ChatSection',
  components: {
    ChatContainer
  },
  props: {
    agentId: {
      type: String,
      required: true
    },
    agentName: {
      type: String,
      default: '科宝'
    },
    userId: {
      type: String,
      required: false
    },
    initialMessages: {
      type: Array,
      default: () => []
    },
    conversationId: {
      type: String,
      default: ''
    }
  },
  emits: [
    'update:conversation-id',
    'error',
    'message-sent',
    'message-received',
    'node-execution',
    'process-update'
  ],
  methods: {
    // 暴露 ChatContainer 的方法给父组件使用
    sendMessage(message) {
      return this.$refs.chatContainer?.sendMessage(message)
    },
    
    setUploadedFiles(files) {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.uploadedFiles = files
      }
    },
    
    setActiveModes(modes) {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.activeModes = modes
      }
    },
    
    setSelectedKnowledgeList(knowledgeList) {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.selectedKnowledgeList = knowledgeList
      }
    },
    
    setSelectedKnowledgeIds(knowledgeIds) {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.selectedKnowledgeIds = knowledgeIds
      }
    },
    
    setSelectedDocumentIds(documentIds) {
      if (this.$refs.chatContainer) {
        this.$refs.chatContainer.selectedDocumentIds = documentIds
      }
    },
    
    getMessages() {
      return this.$refs.chatContainer?.messages || []
    },
    
    // 暴露滚动相关方法
    autoScrollToBottom() {
      return this.$refs.chatContainer?.autoScrollToBottom()
    },
    
    forceScrollToBottom() {
      return this.$refs.chatContainer?.forceScrollToBottom()
    },
    
    scrollToBottom() {
      return this.$refs.chatContainer?.scrollToBottom()
    }
  }
}
</script>

<style scoped>
/* 左侧对话区域 */
.chat-section {
  /* 移除 flex: 1，使用父组件设置的宽度 */
  display: flex;
  flex-direction: column;
  padding: 1rem;
  /* 移除固定的宽度限制，让父组件控制 */
  overflow: hidden;
  flex-shrink: 0; /* 防止被压缩 */
  flex-grow: 0;   /* 防止扩展 */
}

.chat-wrapper {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .chat-section {
    flex: none;
    height: 50vh;
    max-width: 100%;
    min-width: auto;
  }
}
</style> 