<template>
  <div class="chat-container-wrapper">
    <!-- 消息区域 -->
    <div class="messages-area" ref="messagesContainer">
      <!-- 消息内容 -->
      <div v-for="(message, index) in messages"
          :key="index"
          class="flex mb-8"
          :class="message.sender === 'ai' ? 'justify-start' : 'justify-end'"
      >
        <!-- AI 消息 -->
        <div v-if="message.sender === 'ai'" class="flex items-start gap-2 max-w-[100%] group relative">
          <div class="overflow-hidden flex-shrink-0 w-16 h-16 rounded-full">
            <img src="@/assets/image/kebao.png" alt="AI头像" class="object-cover w-full h-full" />
          </div>
          <div class="flex flex-col gap-2 flex-1 min-w-0">
            <!-- AI回答内容 -->
            <div class="p-4 bg-white rounded-lg shadow-md w-full">
              <div v-if="message.loading" class="flex gap-2 items-center">
                <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
              </div>
              <div v-else class="text-gray-800 ai-content" v-html="message.content || ''"></div>
              
              <!-- 添加参考文档引用展示 -->
              <div v-if="message.documents && message.documents.length > 0" class="pt-2 mt-4 border-t border-gray-200">
                <p class="text-sm text-gray-500">参考文档：</p>
                <ul class="mt-1 space-y-1">
                  <li v-for="(docGroup, i) in getGroupedDocuments(message.documents)" :key="i" class="flex items-center justify-between text-sm text-primary">
                    <div class="flex items-center">
                      <template v-if="getDocumentIconType(docGroup.name) === 'pdf'">
                        <img src="@/assets/image/PDF.png" alt="PDF" class="mr-1 w-4 h-4 text-red-500" />
                      </template>
                      <template v-else-if="getDocumentIconType(docGroup.name) === 'word'">
                        <img src="@/assets/image/wps.png" alt="Word" class="mr-1 w-4 h-4 text-blue-600" />
                      </template>
                      <template v-else>
                        <img src="@/assets/image/txt.png" alt="其他" class="mr-1 w-4 h-4 text-primary" />
                      </template>
                      <span class="cursor-pointer hover:underline">{{ docGroup.name }}</span>
                    </div>
                    
                    <!-- 分块列表下拉 -->
                    <div class="relative" v-if="docGroup.chunks.length > 1">
                      <el-dropdown 
                        trigger="click" 
                        placement="bottom-end"
                        :popper-options="{ strategy: 'fixed' }"
                      >
                        <span class="cursor-pointer text-xs px-2 py-1 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors">
                          {{ docGroup.chunks.length }} 个分块
                          <svg class="inline w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                          </svg>
                        </span>
                        <template #dropdown>
                          <el-dropdown-menu class="chunk-dropdown-menu">
                            <el-dropdown-item 
                              v-for="(chunk, chunkIndex) in docGroup.chunks" 
                              :key="chunkIndex"
                              class="chunk-dropdown-item"
                            >
                              <el-popover 
                                popper-class="doc-tooltip"
                                placement="left" 
                                :content="chunk.content || '暂无分块内容'" 
                                :disabled="!chunk.content"
                                :enterable="true"
                                :show-after="300"
                                width="25vw"
                                trigger="hover"
                              >
                                <template #reference>
                                  <span class="block w-full text-left">
                                    分块 {{ chunkIndex + 1 }}
                                    <span class="text-xs text-gray-400 block mt-1">
                                      {{ chunk.content ? chunk.content.substring(0, 30) + '...' : '暂无内容' }}
                                    </span>
                                  </span>
                                </template>
                              </el-popover>
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                    
                    <!-- 单个分块时直接显示内容 -->
                    <div v-else-if="docGroup.chunks.length === 1" class="flex items-center">
                      <el-popover 
                        popper-class="doc-tooltip"
                        placement="top" 
                        :content="docGroup.chunks[0].content || '暂无文档内容'" 
                        :disabled="!docGroup.chunks[0].content"
                        :enterable="true"
                        :show-after="500"
                        width="25vw"
                        trigger="hover"
                      >
                        <template #reference>
                          <span class="cursor-pointer text-xs px-2 py-1 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors">
                            查看内容
                          </span>
                        </template>
                      </el-popover>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- 操作按钮组 -->
          <div class="flex absolute -bottom-8 left-16 gap-2 items-center p-1 px-2 bg-white rounded-full border border-gray-100 shadow-sm opacity-0 transition-all duration-200 group-hover:opacity-100">
            <!-- 复制按钮 -->
            <button 
              @click="copyMessage(message.content, message)"
              class="p-1 text-gray-400 rounded-full transition-colors duration-200 hover:text-primary hover:bg-gray-50"
              title="复制消息"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
            </button>
            
            <!-- 重新生成按钮 -->
            <button 
              @click="regenerateResponse(index)"
              class="p-1 text-gray-400 rounded-full transition-colors duration-200 hover:text-primary hover:bg-gray-50"
              title="重新生成"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
            </button>
            
            <!-- 点赞按钮 -->
            <button 
              @click="handleLike(message, true)"
              :class="[
                'p-1 transition-colors duration-200 rounded-full hover:bg-gray-50',
                message.liked ? 'text-primary' : 'text-gray-400 hover:text-primary'
              ]"
              title="点赞"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M7 10v12"/><path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"/></svg>
            </button>
            
            <!-- 点踩按钮 -->
            <button 
              @click="handleLike(message, false)"
              :class="[
                'p-1 transition-colors duration-200 rounded-full hover:bg-gray-50',
                message.disliked ? 'text-red-500' : 'text-gray-400 hover:text-red-500'
              ]"
              title="点踩"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 14V2"/><path d="M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z"/></svg>
            </button>
            
            <!-- 下载按钮 -->
            <button 
              @click="downloadMessage(message)"
              class="p-1 text-gray-400 rounded-full transition-colors duration-200 hover:text-primary hover:bg-gray-50"
              title="下载消息"
              :disabled="message.downloadLoading"
            >
              <svg v-if="!message.downloadLoading" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7,10 12,15 17,10"/><line x1="12" y1="15" x2="12" y2="3"/></svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="animate-spin"><path d="M21 12a9 9 0 1 1-6.219-8.56"/></svg>
            </button>
          </div>
        </div>
        
        <!-- 用户消息 -->
        <div v-else class="flex items-start gap-2 max-w-[85%] group relative">
          <div class="p-4 rounded-lg shadow-md bg-primary">
            <div class="text-white" v-html="renderUserMessageContent(message)"></div>
          </div>
          <!-- 用户消息复制按钮 -->
          <div class="flex absolute right-0 -bottom-8 gap-2 items-center p-1 px-2 bg-white rounded-full border border-gray-100 shadow-sm opacity-0 transition-all duration-200 group-hover:opacity-100">
            <button 
              @click="copyMessage(message.content, message)"
              class="p-1 text-gray-400 rounded-full transition-colors duration-200 hover:text-primary hover:bg-gray-50"
              title="复制消息"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-wrapper" :style="inputWrapperStyle">
      <div class="flex relative flex-col p-4 bg-white rounded-lg shadow-md input-container">
        <!-- 文件预览区域 -->
        <div class="file-preview-container">
          <div v-if="uploadedFiles.length > 0" class="flex flex-wrap gap-2 mb-3 uploaded-files-preview">
            <div 
              v-for="(file, index) in uploadedFiles" 
              :key="file.fileId"
              class="flex items-center p-2 bg-gray-50 rounded-lg file-preview-item"
            >
              <span class="mr-2">{{ getFileIcon(file.fileType) }}</span>
              <span class="text-sm text-gray-600 cursor-pointer hover:text-primary" @click="previewFile(file)">
                {{ file.fileName }}
              </span>
              <button 
                class="ml-2 text-gray-400 hover:text-red-500"
                @click="removeFile(index)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 预设问题区域 -->
        <div v-if="suggestedQuestions.length > 0 && !isOfficeAgent" class="suggested-questions-container">
          <div class="suggested-questions-wrapper">
            <div 
              v-for="(question, index) in suggestedQuestions" 
              :key="index"
              class="suggested-question-item"
              @click="selectSuggestedQuestion(question, index)"
            >
              <img 
                v-if="question.fileId && question.fileId.trim()"
                :src="getQuestionImage(question.fileId)" 
                :alt="`预设问题图片${index + 1}`"
                class="question-image"
                title="点击放大图片"
                @click.stop="previewQuestionImage(question.fileId)"
              />
              <span class="question-text" title="点击发送消息">{{ question.content || question }}</span>
            </div>
          </div>
        </div>
        
        <!-- 公文办公区域 -->
        <div v-if="isOfficeAgent" class="office-area">
          <el-form :model="officeForm" size="small" class="office-form" style="padding: 10px 10px 0 10px;" label-position="left">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="根据以下信息，写一篇">
                  <el-select v-model="officeForm.subjectContent" placeholder="请选择">
                    <el-option label="公告" value="公告" />
                    <el-option label="通知" value="通知" />
                    <el-option label="通报" value="通报" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="标题">
                  <el-input v-model="officeForm.title" placeholder="输入标题" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发文字号">
                  <el-input v-model="officeForm.documentNumber" placeholder="输入发文字号" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="发文单位">
                  <el-input v-model="officeForm.issuingUnit" placeholder="输入发文单位" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="主送机关">
                  <el-input v-model="officeForm.toPrimary" placeholder="输入主送单位" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="成文日期">
                  <el-date-picker v-model="officeForm.createDate" type="date" placeholder="选择日期" style="width: 100%;" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="发布日期">
                  <el-date-picker v-model="officeForm.releaseDate" type="date" placeholder="选择日期" style="width: 100%;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item label="参考内容模板" style="margin-bottom: 0">
                  <el-tag
                    v-for="file in officeForm.referenceTemplate"
                    class="file-tag"
                    :key="file.fileId"
                    @close="handleTagTemplateDelete(file)"
                    closable
                  >
                    {{ file.fileName }}
                  </el-tag>
                  <el-upload
                    v-if="officeForm.referenceTemplate.length === 0"
                    action="/api/v1/public/chat/uploadFile"
                    :data="uploadData"
                    :on-success="(response, file) => handleUploadSuccess(response, file, 'template')"
                    :on-error="handleUploadError"
                    :before-upload="beforeUpload"
                    :file-list="officeForm.referenceTemplate"
                    :limit="1"
                    list-type="text"
                    :show-file-list="false"
                    >
                    <el-button size="small" type="primary" text>选择文件</el-button>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="相关参考" style="margin-bottom: 0">
                  <el-upload
                    class="upload-demo"
                    action="/api/v1/public/chat/uploadFile"
                    :data="uploadData"
                    :on-success="handleReferenceUploadSuccess"
                    :on-error="handleUploadError"
                    :before-upload="beforeUpload"
                    :file-list="officeForm.referenceMaterials"
                    multiple
                    :limit="5"
                    :show-file-list="true"
                  >
                    <el-button size="small" type="primary" text>选择文件</el-button>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 输入框区域 -->
        <div class="input-area">
          <textarea
            v-if="isOfficeAgent"
            v-model="officeForm.contentRequirements"
            placeholder="请输入写作要求"
            class="p-2 w-full text-gray-700 align-top outline-none resize-none input-textarea"
            @keyup.enter.prevent="handleEnterPress"
            rows="3"
          ></textarea>
          <textarea
            v-else
            v-model="userInput"
            placeholder="你可以问我任何问题，或说出我如何帮助你..."
            class="p-2 w-full text-gray-700 align-top outline-none resize-none input-textarea"
            @keyup.enter.prevent="handleEnterPress"
            rows="3"
          ></textarea>
          
        </div>

        <!-- 底部工具栏 -->
        <div class="bottom-toolbar">
          <div class="flex justify-between items-center">
            <!-- 左侧标签按钮 -->
            <div class="flex gap-2 items-center m-4">
              <!--<button 
                @click="toggleMode('deep')" 
                class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
                :class="[
                  activeModes.includes('deep')
                    ? 'bg-primary text-white' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <div class="flex gap-1 items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12z"></path><path d="M12 8v8"></path><path d="M8 12h8"></path></svg>
                  深度思考
                </div>
              </button>-->
              <template v-if="!isOfficeAgent">
                <button 
                  v-if="showSearchMode"
                  @click="toggleMode('search')" 
                  class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
                  :class="[
                    activeModes.includes('search')
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  ]"
                >
                  <div class="flex gap-1 items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                    联网搜索
                  </div>
                </button>
                <button 
                  v-if="showKnowledgeSelectionButton"
                  @click="showKnowledgeSelection = true" 
                  class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
                  :class="[
                    selectedKnowledgeList.length > 0
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  ]"
                >
                  <div class="flex gap-1 items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/><path d="M16 8V6H8v2"/><path d="M16 12V10H8v2"/><path d="M16 16V14H8v2"/></svg>
                    知识库选择
                    <span v-if="selectedKnowledgeList.length > 0" class="ml-1 px-1.5 py-0.5 text-xs bg-white bg-opacity-30 rounded-full">
                      {{ selectedKnowledgeList.length }}
                    </span>
                  </div>
                </button>
              </template>
               <div class="px-2 text-xs text-gray-400">内容由 AI 生成，请仔细甄别</div>
            </div>

            <!-- 右侧工具栏 -->
            <div class="flex gap-2 items-center m-4 text-gray-500 func-container">
              <el-upload
                v-if="showFileUpload && !isOfficeAgent"
                class="upload-button"
                :action="`/api/v1/public/chat/uploadFile`"
                :data="uploadData"
                :show-file-list="false"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :before-upload="beforeUpload"
              >
                <button class="p-2 rounded-full hover:bg-gray-100">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-paperclip"><path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"/></svg>
                </button>
              </el-upload>
              
              <!-- 配置按钮 -->
              <button 
                v-if="userInputForm.length > 0 && !isOfficeAgent"
                @click="formDialogVisible = true" 
                class="p-2 rounded-full hover:bg-gray-100"
                title="配置参数"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>
              </button>
              
              <button 
                @click="isOfficeAgent ? sendOfficeMessage() : sendMessage()" 
                class="p-2 text-white rounded-full bg-primary hover:bg-primary-middle"
                :disabled="isLoading"
              >
                <svg v-if="!isLoading" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="animate-spin"><path d="M21 12a9 9 0 1 1-6.219-8.56"/></svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图片预览组件 -->
    <el-image
      v-if="previewVisible"
      :src="previewUrl"
      :preview-src-list="[previewUrl]"
      hide-on-click-modal
      @close="previewVisible = false"
      class="preview-image"
    />

    <!-- 知识库选择弹框 -->
    <DatabaseSelection
      v-model="showKnowledgeSelection"
      :selected-ids="selectedKnowledgeList.map(item => item.id)"
      @confirm="handleKnowledgeSelection"
    />

    <!-- 表单弹窗 -->
    <el-dialog
      v-model="formDialogVisible"
      title="请填写配置项"
      width="30%"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
    >
      <el-form label-position="top">
        <el-form-item 
          v-for="item in userInputForm" 
          :key="item.variable"
          :label="item.label + (item.required ? ' *' : '')"
          :required="item.required"
        >
          <!-- 文本输入框 -->
          <el-input 
            v-if="item.type === 'text-input'" 
            v-model="formValues[item.variable]"
            :placeholder="'请输入' + item.label"
            :maxlength="item.maxLength"
          />
          
          <!-- 段落输入框 -->
          <el-input 
            v-else-if="item.type === 'paragraph'" 
            v-model="formValues[item.variable]"
            type="textarea"
            :rows="4"
            :placeholder="'请输入' + item.label"
            :maxlength="item.maxLength"
          />
          
          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="item.type === 'number-input'"
            v-model="formValues[item.variable]"
            :placeholder="'请输入' + item.label"
            :min="item.min"
            :max="item.max"
            :step="item.step || 1"
          />
          
          <!-- 下拉选择框 -->
          <el-select
            v-else-if="item.type === 'select'"
            v-model="formValues[item.variable]"
            :placeholder="'请选择' + item.label"
            style="width: 100%"
          >
            <el-option
              v-for="option in item.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <!-- <el-button @click="closeFormDialog">取消</el-button> -->
          <el-button type="primary" @click="saveFormValues">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { marked } from 'marked'
import { handleSendMessage, getAgentDetail, textToFile, sendOfficeMessage} from '@/api/ai'
import { ElMessage, ElMessageBox, ElImageViewer} from 'element-plus'
import DatabaseSelection from './DatabaseSelection.vue'
import { createVNode, render } from 'vue'
import { useFormStore } from '@/stores/form'
import dayjs from 'dayjs'

export default {
  name: 'ChatContainer',
  components: {
    DatabaseSelection
  },
  props: {
    // 基础配置
    agentId: {
      type: String,
      required: true
    },
    userId: {
      type: String,
      required: true
    },
    // 初始消息和会话ID
    initialMessages: {
      type: Array,
      default: () => []
    },
    conversationId: {
      type: String,
      default: ''
    },
    // 样式配置
    inputStyle: {
      type: Object,
      default: () => ({})
    },
    // 功能控制
    allowRegenerate: {
      type: Boolean,
      default: true
    },
    allowLike: {
      type: Boolean,
      default: true
    },
    // 父组件类型标识
    parentType: {
      type: String,
      default: 'new',  // 默认为new，可选值: 'new' 或 'history'
      validator: (value) => ['new', 'history'].includes(value)
    }
  },
  data() {
    return {
      officeForm: {
        title: '',              // 标题  
        subjectContent: '公告', // 写作类型，默认选中"公告"
        issuingUnit: '',        // 发文单位
        toPrimary: '',             // 收文单位
        createDate: new Date(), // 创建日期
        releaseDate: new Date(),// 发布日期
        documentNumber: '',     // 发文字号
        contentRequirements: '',// 正文内容要求
        referenceTemplate: [],  // 参考模板
        referenceMaterials: []  // 相关依据
      },
      isOfficeAgent: false,
      fileReference: [],
      messages: [...this.initialMessages],
      userInput: '',
      currentConversationId: this.conversationId,
      isLoading: false,
      uploadedFiles: [],
      previewVisible: false,
      previewUrl: '',
      activeModes: [], // 使用数组存储激活的模式
      scrollTimer: null,
      showSearchMode: true, // 是否显示联网搜索按钮
      showKnowledgeSelectionButton: true, // 是否显示知识库选择按钮
      showFileUpload: true, // 是否显示文件上传按钮
      userInputForm: [], // 存储用户需要填写的表单
      formValues: {}, // 存储用户填写的表单值
      formDialogVisible: false, // 控制表单弹窗显示
      formStore: null, // 存储Pinia store实例
      homeSendMessage: '', // 首页发送的消息
      showKnowledgeSelection: false, // 控制知识库选择弹窗显示
      selectedKnowledgeList: [], // 选择的知识库列表
      selectedKnowledgeIds: [], // 选择的知识库ID列表
      selectedDocumentIds: [], // 选择的文档ID列表
      suggestedQuestions: [], // 存储预设问题
      resizeObserver: null // ResizeObserver 实例
    }
  },
  computed: {
    uploadData() {
      return {
        userId: this.userId,
        agentId: this.agentId
      }
    },
    inputWrapperStyle() {
      return {
        ...this.inputStyle
      }
    },
    // 计算是否存在必填项
    hasRequiredFields() {
      if (!this.userInputForm || this.userInputForm.length === 0) {
        return false;
      }
      return this.userInputForm.some(item => item.required);
    }
  },
  emits: ['update:conversationId', 'message-sent', 'message-received', 'error', 'like'],
  watch: {
    initialMessages: {
      handler(newMessages) {
        // 只在初始化或清空时更新
        if (this.messages.length === 0 || newMessages.length === 0) {
          this.messages = [...newMessages];
        }
      },
      deep: true
    },
    conversationId(newId) {
      this.currentConversationId = newId;
    },
    'messages.length'() {
      this.scrollToBottom();
    },
    messages: {
      deep: true,
      handler() {
        this.scrollToBottom();
      }
    }
  },
  mounted() {
    // 初始化Pinia store
    this.formStore = useFormStore();
    
    this.$nextTick(() => {
      this.scrollToBottom();
      this.updateMessagesAreaHeight();
    });
    
    // 获取agent详情，判断是否显示联网搜索按钮
    if(this.agentId){
      this.getAgentInfo();
    }

    // 判断是否为公文办公智能体
    this.isOfficeAgent = this.agentId === '799592ca740609d28525d878d539fdc1'? true : false;
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.updateMessagesAreaHeight);
    
    // 使用 MutationObserver 监听输入容器高度变化
    this.observeInputContainer();
    
    // 将预览文件方法暴露到全局，以便在HTML中调用
    window.chatContainer = {
      previewMessageFile: this.previewMessageFile.bind(this)
    };
  },
  methods: {
    /**
     * 获取agent详情
     */
    async getAgentInfo() {
      try {
        const response = await getAgentDetail({ agentId: this.agentId });
        if (response && response.data) {
          // 如果onlineSearch为0，则不显示联网搜索按钮
          this.showSearchMode = response.data.onlineSearch;
          // 如果selectKnow为0，则不显示知识库选择按钮
          this.showKnowledgeSelectionButton = response.data.selectKnow !== 0;
          // 如果fileUpload为false，则不显示文件上传按钮
          this.showFileUpload = response.data.fileUpload !== false;
          // 根据agentType解析预设问题数据结构
          this.parseSuggestedQuestions(response.data.agentType, response.data.suggestedQuestions || []);
          // 更新消息区域高度
          this.$nextTick(() => {
            this.updateMessagesAreaHeight();
          });
          // 检查是否有用户输入表单配置
          if (response.data.userInputForm && response.data.userInputForm.length > 0) {
            console.log('接收到表单配置：', response.data.userInputForm);
            // 处理表单配置数据，将字符串数组转换为对象数组
            this.userInputForm = response.data.userInputForm.map(item => {
              // 如果是下拉选择框且options是字符串数组，需要转换为对象数组
              if (item.type === 'select' && item.options && Array.isArray(item.options)) {
                const convertedOptions = item.options.map(option => {
                  // 如果已经是对象，直接返回
                  if (typeof option === 'object' && option !== null && option.value !== undefined) {
                    return option;
                  }
                  // 如果是字符串，转换为对象
                  return {
                    value: option,
                    label: option
                  };
                });
                return {
                  ...item,
                  options: convertedOptions
                };
              }
              return item;
                         });
            console.log('转换后的表单配置：', this.userInputForm);
            // 检查是否已有保存的表单配置
            if (this.formStore.hasAgentForm(this.agentId)) {
              // 从store中获取表单值
              this.formValues = this.formStore.getAgentFormValues(this.agentId);
            } else {
              // 初始化表单值
              this.initFormValues();
              // 有表单配置时立即显示弹窗，仅当有必填项时
              if(this.parentType === 'new' && this.hasRequiredFields){
                this.formDialogVisible = true;
              }
            }
          }
          if(this.parentType === 'new' && this.homeSendMessage){
            this.sendMessage(this.homeSendMessage);
          }
        }
      } catch (error) {
        console.error('Failed to get agent detail:', error);
      }
    },
    
    /**
     * 初始化表单值
     */
    initFormValues() {
      this.formValues = {};
      this.userInputForm.forEach(item => {
        // 根据不同类型设置不同的默认值
        if (item.type === 'text-input' || item.type === 'paragraph') {
          this.formValues[item.variable] = '';
        } else if (item.type === 'number-input') {
          // 对于数字输入框，设置默认值
          this.formValues[item.variable] = item.min !== undefined ? item.min : null;
        } else if (item.type === 'select') {
          // 对于下拉框，检查是否有选项
          if (item.options && item.options.length > 0) {
            // 如果是必填项，默认选择第一项，否则置空
            this.formValues[item.variable] = item.required ? item.options[0].value : '';
          } else {
            console.warn(`表单字段 ${item.label} 的选项为空，无法设置默认值`);
            this.formValues[item.variable] = '';
          }
        } else {
          // 其他类型默认为空字符串
          this.formValues[item.variable] = '';
        }
      });
    },
    
    /**
     * 提交表单
     */
    saveFormValues() {
      // 打印当前表单值，用于调试
      console.log('表单提交值：', this.formValues);
      console.log('formStore对象：', this.formStore);
      console.log('saveAgentForm方法：', this.formStore.saveAgentForm);
      
      // 验证必填项
      const invalidFields = this.validateFormValues();
      if (invalidFields.length > 0) {
        ElMessage.warning(`请正确填写以下字段: ${invalidFields.join(', ')}`);
        return false; // 返回false表示验证失败
      }
      
      try {
        // 保存表单配置到Pinia store
        this.formStore.saveAgentForm(this.agentId, this.formValues, this.userInputForm);
        console.log('表单配置已保存到store');
        
        // 只关闭弹窗，不发送消息
        this.formDialogVisible = false;
        
        // 显示表单保存成功提示
        ElMessage.success('参数配置已保存')
        return true; // 返回true表示验证通过
      } catch (error) {
        console.error('保存表单配置时出错：', error);
        ElMessage.error('保存配置失败，请重试');
        return false;
      }
    },
    
    /**
     * 验证表单值
     * @returns {Array} 验证失败的字段标签数组
     */
    validateFormValues() {
      const invalidFields = [];
      
      this.userInputForm.forEach(item => {
        const value = this.formValues[item.variable];
        
        // 验证必填项
        if (item.required) {
          if ((item.type === 'text-input' || item.type === 'paragraph') && 
              (!value || value.trim() === '')) {
            invalidFields.push(item.label);
          }
          
          if (item.type === 'select' && (!value || value === '')) {
            invalidFields.push(item.label);
          }
          
          if (item.type === 'number-input' && (value === undefined || value === null)) {
            invalidFields.push(item.label);
          }
        }
        
        // 验证选择项是否有效
        if (item.type === 'select' && (!item.options || item.options.length === 0)) {
          console.warn(`表单字段 ${item.label} 的选项为空`);
        }
        
        // 验证文本长度
        if ((item.type === 'text-input' || item.type === 'paragraph') && 
            item.maxLength && value && value.length > item.maxLength) {
          invalidFields.push(`${item.label} (超出最大长度)`);
        }
      });
      
      return invalidFields;
    },

    /**
     * 解析预设问题数据结构
     */
    parseSuggestedQuestions(agentType, suggestedQuestions) {
      if (!suggestedQuestions || !Array.isArray(suggestedQuestions)) {
        this.suggestedQuestions = [];
        return;
      }

      try {
        
          //现在是对象数组格式
          this.suggestedQuestions = suggestedQuestions.map(questionItem => {
            // 如果是字符串，尝试解析JSON（兼容旧格式）
            if (typeof questionItem === 'string') {
              try {
                const parsed = JSON.parse(questionItem);
                return {
                  content: parsed.content || parsed.description || '',
                  fileId: parsed.fileId || null,
                  key: parsed.key || ''
                };
              } catch (parseError) {
                console.warn('解析预设问题JSON失败:', parseError, questionItem);
                // 如果解析失败，作为普通字符串处理
                return {
                  content: questionItem,
                  fileId: null,
                  key: ''
                };
              }
            } else if (typeof questionItem === 'object' && questionItem !== null) {
              // 如果是对象，直接使用（新格式）
              return {
                content: questionItem.content || questionItem.description || '',
                fileId: questionItem.fileId || null,
                key: questionItem.key || ''
              };
            } else {
              // 其他情况，转为字符串处理
              return {
                content: String(questionItem),
                fileId: null,
                key: ''
              };
            }
          }).filter(item => item.content); // 过滤掉空内容的项目
        
      } catch (error) {
        console.error('解析预设问题数据失败:', error);
        this.suggestedQuestions = [];
      }

      console.log('解析后的预设问题:', this.suggestedQuestions);
    },

    /**
     * 选择预设问题
     */
    selectSuggestedQuestion(question, index) {
      // 兼容新旧格式：question可能是对象或字符串
      const questionText = typeof question === 'object' ? (question.content || question.description || question) : question;
      const questionFileId = typeof question === 'object' ? question.fileId : null;
      
      this.userInput = questionText;
      
      // 准备预设问题的图片文件
      const questionFiles = [];
      if (questionFileId && questionFileId.trim()) {
        questionFiles.push({
          fileId: questionFileId,
          fileType: 'IMAGE', // 假设预设问题中的文件都是图片
          extension: 'jpg', // 默认扩展名，可根据实际情况调整
          fileName: `预设问题图片${index + 1}` // 提供一个默认文件名
        });
      }
      
      // 直接发送消息，将图片ID传递给sendMessage
      this.sendMessage(null, questionFiles);
    },

    /**
     * 获取预设问题图片URL
     */
    getQuestionImage(fileId) {
      if (!fileId || fileId.trim() === '') return '';
      // 拼接图片URL，使用base_url
      const baseURL = import.meta.env.VITE_API_BASE_URL || '';
      return `${baseURL}/v1/public/file/get?fileId=${fileId}`;
    },

    /**
     * 预览预设问题图片
     */
    previewQuestionImage(fileId) {
      if (!fileId || fileId.trim() === '') return;
      
      const imageUrl = this.getQuestionImage(fileId);
      
      // 创建图片预览实例
      const container = document.createElement('div');
      const vnode = createVNode(ElImageViewer, {
        urlList: [imageUrl],
        onClose: () => {
          render(null, container);
          if (document.body.contains(container)) {
            document.body.removeChild(container);
          }
        }
      });
      
      document.body.appendChild(container);
      render(vnode, container);
    },

    /**
     * 渲染用户消息内容，将文件名转换为可点击链接
     */
    renderUserMessageContent(message) {
      if (!message.content) return '';
      
      let content = message.content;
      
      // 如果消息中有文件信息，处理文件名点击
      if (message.files && message.files.length > 0) {
        message.files.forEach(file => {
          // 检查文件对象是否有效
          if (!file || !file.fileId || !file.fileName || !file.fileType) {
            console.warn('文件信息不完整:', file);
            return;
          }
          
          // 创建文件图标和名称的HTML
          const fileIcon = this.getFileIcon(file.fileType);
          const originalText = `${fileIcon} ${file.fileName}`;
          
          // 只对图片类型的文件创建可点击链接
          if (file.fileType === 'IMAGE' || file.fileType === 'image') {
            // 转义文件名中的特殊字符以避免HTML注入
            const escapedFileName = file.fileName.replace(/'/g, '&#39;').replace(/"/g, '&quot;');
            const clickableLink = `<span class="file-link cursor-pointer hover:underline" onclick="window.chatContainer.previewMessageFile('${file.fileId}', '${file.fileType}')" title="点击预览图片">${fileIcon} ${escapedFileName}</span>`;
            
            // 使用正则表达式进行更精确的替换
            const regex = new RegExp(`${this.escapeRegExp(fileIcon)}\\s+${this.escapeRegExp(file.fileName)}`, 'g');
            content = content.replace(regex, clickableLink);
          }
        });
      }
      
      // 转换换行符为HTML
      return content.replace(/\n/g, '<br>');
    },

    /**
     * 转义正则表达式中的特殊字符
     */
    escapeRegExp(string) {
      if (typeof string !== 'string') {
        return '';
      }
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    },

    /**
     * 预览消息中的文件
     */
    previewMessageFile(fileId, fileType) {
      if (!fileId || fileId.trim() === '') return;
      
      // 只处理图片类型的文件
      if (fileType === 'IMAGE' || fileType === 'image') {
        const imageUrl = this.getQuestionImage(fileId);
        
        // 创建图片预览实例
        const container = document.createElement('div');
        const vnode = createVNode(ElImageViewer, {
          urlList: [imageUrl],
          onClose: () => {
            render(null, container);
            if (document.body.contains(container)) {
              document.body.removeChild(container);
            }
          }
        });
        
        document.body.appendChild(container);
        render(vnode, container);
      }
    },

    /**
     * 处理知识库选择
     */
    handleKnowledgeSelection(selection) {
      this.selectedKnowledgeList = selection.list
      this.selectedKnowledgeIds = selection.knowledgeIds || []
      this.selectedDocumentIds = selection.documentIds || []
      console.log('选择的知识库:', selection)
      console.log('知识库IDs:', this.selectedKnowledgeIds)
      console.log('文档IDs:', this.selectedDocumentIds)
    },

    /**
     * 发送公文办公消息
     */
    sendOfficeMessage(){
      let formData = {...this.officeForm}
      formData.createDate = dayjs(formData.createDate).format('YYYY-MM-DD')
      formData.releaseDate = dayjs(formData.releaseDate).format('YYYY-MM-DD')
      
      let params = {
        workflowId: '1945775535429709826',
        variables: formData
      }

      sendOfficeMessage(params).then(res => {
        
      })
    },
    
    /**
     * 处理发送消息
     */
    sendMessage(messageContent = null, questionFiles = []) {
      console.log('发送消息');
      
      // 基本验证：如果没有内容且没有文件，或者正在加载中，则不发送
      if ((!this.userInput.trim() && !messageContent && this.uploadedFiles.length === 0 && questionFiles.length === 0) || this.isLoading) return;
      
      // 表单验证：如果有表单配置且有必填项，需要先验证必填项
      if (this.userInputForm && this.userInputForm.length > 0 && this.hasRequiredFields) {
        const invalidFields = this.validateFormValues();
        if (invalidFields.length > 0) {
          // 如果有未填写的必填项，显示表单对话框并提示
          this.formDialogVisible = true;
          ElMessage.warning(`请正确填写以下字段: ${invalidFields.join(', ')}`);
          return; // 阻止发送消息
        }
      }
      
      this.isLoading = true;
      
      // 准备消息内容
      let messageText = messageContent || this.userInput.trim();
      
      // 检查是否是事件对象，防止传递错误的消息内容
      if (messageText && typeof messageText === 'object' && messageText.isTrusted !== undefined) {
        console.error('错误：传递了事件对象而不是文本内容', messageText);
        messageText = this.userInput.trim();
      } else if (typeof messageText === 'object' && !(messageText instanceof Array)) {
        // 如果是其他对象但不是数组，转为JSON字符串
        console.warn('警告：尝试发送对象作为消息', messageText);
        messageText = JSON.stringify(messageText);
      }
      
      // 准备表单输入参数
      let inputs = {};
      // 只有当表单配置存在且长度大于0时，才使用表单值
      if (this.userInputForm && this.userInputForm.length > 0) {
        inputs = {...this.formValues};
        console.log('发送消息的表单值：', inputs);
      }
      
      let onlineSearch = false
      let deepResearch = false
      
      // 准备文件信息，合并上传的文件和预设问题的文件
      const files = [
        ...this.uploadedFiles.map(file => ({
          fileId: file.fileId,
          fileType: file.fileType,
          extension: file.extension,
          fileName: file.fileName
        })),
        ...questionFiles.map(file => ({
          fileId: file.fileId,
          fileType: file.fileType,
          extension: file.extension,
          fileName: file.fileName
        }))
      ];
      
      // 如果有文件，添加分割线和文件列表
      if (this.uploadedFiles.length > 0 || questionFiles.length > 0) {
        const uploadedFilesList = this.uploadedFiles
          .map(file => `${this.getFileIcon(file.fileType)} ${file.fileName}`)
          .join('\n');
        const questionFilesList = questionFiles
          .map(file => `${this.getFileIcon(file.fileType)} ${file.fileName}`)
          .join('\n');
        
        const allFiles = [uploadedFilesList, questionFilesList].filter(Boolean).join('\n');
        if (allFiles) {
          messageText += `\n\n附件:\n${allFiles}`;
        }
      }
      
      // 添加用户消息
      const userMessage = {
        sender: 'user',
        content: messageText,
        files: files  // 保存文件信息用于点击预览
      };
      
      // 调试信息：检查文件信息是否完整
      if (files && files.length > 0) {
        console.log('发送消息的文件信息:', files);
        files.forEach((file, index) => {
          if (!file.fileName) {
            console.warn(`文件 ${index} 缺少 fileName 属性:`, file);
          }
        });
      }
      
      this.messages.push(userMessage);
      
      // 清理输入框和文件列表
      if (messageText) {
        this.userInput = '';
        this.uploadedFiles = [];
      }
      if(this.activeModes.includes('search')){
        onlineSearch = true
      } else {
        onlineSearch = false
      }
      if(this.activeModes.includes('deep')){
        deepResearch = true
      } else {
        deepResearch = false
      }
      // 触发消息发送事件
      this.$emit('message-sent', {
        message: userMessage,
        files,
        inputs,
        onlineSearch,
        deepResearch
      });

      // 添加AI消息占位
      this.messages.push({
        sender: 'ai',
        content: '',
        loading: true
      });

      // 设置超时处理
      let hasReceivedResponse = false;
      const timeout = setTimeout(() => {
        if (!hasReceivedResponse) {
          this.isLoading = false;
          const aiMessageIndex = this.messages.length - 1;
          this.messages[aiMessageIndex].loading = false;
          this.messages[aiMessageIndex].content = '响应超时，请稍后重试。';
          
          this.$emit('error', {
            type: 'timeout',
            message: '请求超时，请稍后重试'
          });
        }
      }, 120000); // 2分钟超时

      // 准备知识库和文档ID信息
      let knowledgeIds = [...(this.selectedKnowledgeIds || [])];
      let documentIds = [...(this.selectedDocumentIds || [])];
      
      // 如果没有单独的ID列表，则从selectedKnowledgeList中提取
      if (knowledgeIds.length === 0 && documentIds.length === 0 && this.selectedKnowledgeList.length > 0) {
        this.selectedKnowledgeList.forEach(item => {
          if (item.type === 'knowledge_folder') {
            knowledgeIds.push(item.id);
          } else if (item.type === 'file') {
            documentIds.push(item.id);
            // 如果是文件，还要添加其所属的知识库ID
            if (item.datasetId && !knowledgeIds.includes(item.datasetId)) {
              knowledgeIds.push(item.datasetId);
            }
          }
        });
      }

      console.log('发送消息时的知识库IDs:', knowledgeIds);
      console.log('发送消息时的文档IDs:', documentIds);

      // 发送消息到API
      handleSendMessage(messageText, {
        agentId: this.agentId,
        conversationId: this.currentConversationId,
        userId: this.userId,
        files: files,
        inputs,
        onlineSearch,
        deepResearch,
        knowledgeIds: knowledgeIds, // 知识库ID列表
        documentIds: documentIds, // 文档ID列表
        onData: (text, messageId, conversationId, metadata) => {
          hasReceivedResponse = true;
          clearTimeout(timeout);
          
          const aiMessageIndex = this.messages.length - 1;
          this.messages[aiMessageIndex].loading = false;
          
          // 处理文档引用
          if (metadata?.retriever_resources) {
            this.messages[aiMessageIndex].documents = metadata.retriever_resources;
          }
          
          // 保存原始内容用于下载
          this.messages[aiMessageIndex].rawContent = text;
          
          // 初始化thinking相关状态（如果还没有初始化）
          if (!this.messages[aiMessageIndex].thinkingState) {
            this.messages[aiMessageIndex].thinkingState = {
              isInThinking: false,
              thinkingContent: '',
              actualContent: '',
              hasThinkingStarted: false
            };
          }
          
          const state = this.messages[aiMessageIndex].thinkingState;
          
          // 检查是否开始thinking
          if (text.includes('<think>') && !state.hasThinkingStarted) {
            console.log('🎯 开始thinking模式');
            state.isInThinking = true;
            state.hasThinkingStarted = true;
            
            // 提取<think>标签之前的内容和之后的内容
            const thinkStart = text.indexOf('<think>');
            const beforeThink = text.substring(0, thinkStart);
            const afterThinkStart = text.substring(thinkStart + 7); // 7是'<think>'的长度
            
            // 初始化状态
            state.actualContent = beforeThink;
            state.thinkingContent = afterThinkStart;
            
            // 显示thinking过程（展开状态）
            const thinkingHtml = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>思考过程...</summary>\n${state.thinkingContent}\n</details>`;
            this.messages[aiMessageIndex].content = this.parseMarkdown(state.actualContent + thinkingHtml);
          }
          // 如果正在thinking中
          else if (state.isInThinking) {
            // 检查是否结束thinking
            if (text.includes('</think>')) {
              console.log('🎯 结束thinking模式');
              state.isInThinking = false;
              
              // 提取</think>标签之前和之后的内容
              const thinkEnd = text.indexOf('</think>');
              const beforeThinkEnd = text.substring(0, thinkEnd);
              const afterThinkEnd = text.substring(thinkEnd + 8); // 8是'</think>'的长度
              
              // 更新thinking内容（完整的思考过程）
              state.thinkingContent = beforeThinkEnd;
              // 开始accumulate实际内容
              state.actualContent += afterThinkEnd;
              
              // 显示完整的thinking过程（保持展开状态）和实际内容
              const thinkingHtml = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>Thinking...</summary>\n${state.thinkingContent}\n</details>`;
              this.messages[aiMessageIndex].content = this.parseMarkdown(thinkingHtml + state.actualContent);
            } else {
              // 继续累积thinking内容（这里text是完整的累积内容）
              // 需要提取thinking部分
              const thinkStart = text.indexOf('<think>');
              if (thinkStart !== -1) {
                const beforeThink = text.substring(0, thinkStart);
                const afterThinkStart = text.substring(thinkStart + 7);
                
                state.actualContent = beforeThink;
                state.thinkingContent = afterThinkStart;
                
                // 实时更新thinking显示（展开状态）
                const thinkingHtml = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>Thinking...</summary>\n${state.thinkingContent}\n</details>`;
                this.messages[aiMessageIndex].content = this.parseMarkdown(state.actualContent + thinkingHtml);
              }
            }
          }
          // 如果thinking已结束，继续追加实际内容
          else if (state.hasThinkingStarted && !state.isInThinking) {
            // 提取thinking之前的内容和thinking之后的内容
            const thinkStart = text.indexOf('<think>');
            const thinkEnd = text.indexOf('</think>');
            
            if (thinkStart !== -1 && thinkEnd !== -1) {
              // 如果text中还包含完整的think标签，提取实际内容
              const beforeThink = text.substring(0, thinkStart);
              const afterThink = text.substring(thinkEnd + 8);
              state.actualContent = beforeThink + afterThink;
            } else {
              // 如果text中没有think标签，说明是纯正文内容
              state.actualContent = text;
            }
            
            // 保持thinking展开状态，显示实际内容
            const thinkingHtml = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>思考过程...</summary>\n${state.thinkingContent}\n</details>`;
            this.messages[aiMessageIndex].content = this.parseMarkdown(thinkingHtml + state.actualContent);
          }
          // 处理其他情况（原有逻辑）
          else if (text.includes('Thinking...')) {
            const thinkingContent = text.match(/<details[^>]*>([\s\S]*?)<\/details>/)?.[1] || '';
            if (!thinkingContent.trim() || thinkingContent.includes('</summary>') && thinkingContent.split('</summary>')[1].trim() === '') {
              const newText = text.replace(/<details[^>]*>[\s\S]*?<\/details>/, '');
              this.messages[aiMessageIndex].content = this.parseMarkdown(newText
                .replace(/<\/details>\n+/, '</details>')); // 处理 details 标签后的换行
            } else {
              this.messages[aiMessageIndex].content = this.parseMarkdown(text
                .replace('<details open', '<details')
                .replace(/<\/details>\n+/, '</details>'));
            }
          } else if (text.includes('<think>')) {
            // 处理 <think> 标签（旧逻辑，保留作为兜底）
            console.log('🎯 ChatContainer检测到think标签')
            console.log('🎯 原始文本:', text)
            
            // 提取thinking内容
            const thinkingMatch = text.match(/<think>([\s\S]*?)<\/think>/);
            if (thinkingMatch) {
              const thinkingContent = thinkingMatch[1];
              console.log('🎯 thinking内容:', thinkingContent)
              
              // 获取thinking后面的实际内容
              const afterThinking = text.split('</think>')[1] || '';
              console.log('🎯 实际内容:', afterThinking)
              
              // 将thinking内容包装为details格式以便UI显示（保持展开状态）
              const wrappedThinking = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>思考过程...</summary>\n${thinkingContent}\n</details>`;
              const finalText = wrappedThinking + afterThinking;
              
              console.log('🎯 最终文本:', finalText)
              this.messages[aiMessageIndex].content = this.parseMarkdown(finalText);
            } else {
              // 如果没有完整的think标签，直接显示原文本
              this.messages[aiMessageIndex].content = this.parseMarkdown(text.replace(/^\n+/, ''));
            }
          } else {
            this.messages[aiMessageIndex].content = this.parseMarkdown(text.replace(/^\n+/, '')); 
          }
          
          this.messages[aiMessageIndex].messageId = messageId;

          // 更新会话ID
          if (conversationId && conversationId !== this.currentConversationId) {
            this.currentConversationId = conversationId;
            this.$emit('update:conversationId', conversationId);
          }
          
          // 触发消息接收事件
          this.$emit('message-received', {
            message: this.messages[aiMessageIndex],
            messageId,
            conversationId,
            metadata
          });
          
          // 滚动到底部
          this.debouncedScroll();
        },
        onCompleted: (text, metadata) => {
          hasReceivedResponse = true;
          clearTimeout(timeout);
          
          // 如果有metadata和引用文档信息，添加到最后一条消息中
          if (metadata && metadata.retriever_resources && metadata.retriever_resources.length > 0) {
            const aiMessageIndex = this.messages.length - 1;
            this.messages[aiMessageIndex].documents = metadata.retriever_resources;
          }
          
          this.isLoading = false;
        },
        onError: (error) => {
          hasReceivedResponse = true;
          clearTimeout(timeout);
          
          const aiMessageIndex = this.messages.length - 1;
          this.messages[aiMessageIndex].loading = false;
          this.messages[aiMessageIndex].content = '抱歉，出现了一些错误，请稍后重试。';
          
          this.isLoading = false;
          
          this.$emit('error', {
            type: 'api',
            message: '发送消息失败',
            error
          });
        }
      });
    },
    
    /**
     * 处理回车按键
     */
    handleEnterPress(event) {
      event.preventDefault();
      if (event.shiftKey) {
        // Shift + Enter 换行
        return;
      }
      if (this.userInput.trim()) {
        // 表单验证：如果有表单配置且有必填项，需要先验证必填项
        if (this.userInputForm && this.userInputForm.length > 0 && this.hasRequiredFields) {
          const invalidFields = this.validateFormValues();
          if (invalidFields.length > 0) {
            // 如果有未填写的必填项，显示表单对话框并提示
            this.formDialogVisible = true;
            ElMessage.warning(`请正确填写以下字段: ${invalidFields.join(', ')}`);
            return; // 阻止发送消息
          }
        }
        
        this.sendMessage();
      }
    },
    
    /**
     * 重新生成响应
     */
    regenerateResponse(index) {
      if (!this.allowRegenerate) return;
      
      // 获取当前 AI 消息的上一条用户消息
      let userMessage = null;
      for (let i = index - 1; i >= 0; i--) {
        if (this.messages[i].sender === 'user') {
          userMessage = this.messages[i].content;
          break;
        }
      }
      
      if (!userMessage) {
        ElMessage.warning('无法重新生成此消息');
        return;
      }
      
      // 发送用户消息以重新生成
      this.sendMessage(userMessage);
    },
    
    /**
     * 处理点赞/点踩
     */
    handleLike(message, isLike) {
      if (!this.allowLike) return;
      
      if (isLike) {
        message.liked = !message.liked;
        if (message.liked) {
          message.disliked = false;
        }
      } else {
        message.disliked = !message.disliked;
        if (message.disliked) {
          message.liked = false;
        }
      }
      
      this.$emit('like', {
        messageId: message.messageId,
        isLike,
        message
      });
    },
    
    /**
     * 切换模式
     */
    toggleMode(mode) {
      const index = this.activeModes.indexOf(mode);
      if (index === -1) {
        // 如果模式不在数组中，添加它
        this.activeModes.push(mode);
      } else {
        // 如果模式已经在数组中，移除它
        this.activeModes.splice(index, 1);
      }
    },
    
    /**
     * 滚动到底部
     */
    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        const container = this.$refs.messagesContainer;
        this.$nextTick(() => {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          });
        });
      }
    },
    
    /**
     * 防抖滚动
     */
    debouncedScroll() {
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }
      
      this.scrollTimer = setTimeout(() => {
        this.scrollToBottom();
      }, 50);
    },
    
    /**
     * 复制消息
     */
    copyMessage(content, message = null) {
      let textContent;
      
      // 如果是AI消息且有原始内容，优先使用原始内容并过滤thinking
      if (message && message.rawContent) {
        textContent = message.rawContent
          .replace(/<think\b[^<]*(?:(?!<\/think>)<[^<]*)*<\/think>/gi, '') // 移除think标签及其内容
          .replace(/<details[^>]*>.*?思考过程.*?<\/details>/gis, '') // 移除包含思考过程的details标签
          .replace(/<details[^>]*>.*?Thinking.*?<\/details>/gis, '') // 移除包含Thinking的details标签
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
          .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // 移除style标签
          .replace(/<[^>]*>/g, '') // 移除其他HTML标签
          .replace(/&nbsp;/g, ' ') // 替换HTML空格
          .replace(/&lt;/g, '<') // 解码HTML实体
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'")
          .trim();
      } else {
        // 对于用户消息或没有rawContent的情况，使用现有逻辑但过滤thinking内容
        const tempElement = document.createElement('div');
        tempElement.innerHTML = content;
        
        // 移除thinking相关的元素
        const thinkingElements = tempElement.querySelectorAll('details');
        thinkingElements.forEach(el => {
          const summary = el.querySelector('summary');
          if (summary && (summary.textContent.includes('思考过程') || summary.textContent.includes('Thinking'))) {
            el.remove();
          }
        });
        
        textContent = tempElement.textContent || tempElement.innerText;
      }
      
      if (!textContent.trim()) {
        ElMessage.warning('没有可复制的内容');
        return;
      }
      
      navigator.clipboard.writeText(textContent).then(() => {
        ElMessage({
          message: '复制成功',
          type: 'success',
          duration: 2000
        });
      }).catch(() => {
        ElMessage({
          message: '复制失败，请手动复制',
          type: 'error',
          duration: 2000
        });
      });
    },
    
    /**
     * 下载消息
     */
    async downloadMessage(message) {
      if (!message.content || message.downloadLoading) return;
      
      // 设置下载状态
      message.downloadLoading = true;
      
      try {
        // 获取原始内容或提取纯文本内容
        let textContent;
        if (message.rawContent) {
          // 使用原始内容，简单清理HTML标签但保留格式
          textContent = message.rawContent
            .replace(/<think\b[^<]*(?:(?!<\/think>)<[^<]*)*<\/think>/gi, '') // 移除think标签及其内容
            .replace(/<details[^>]*>.*?Thinking.*?<\/details>/gis, '') // 移除包含Thinking的details标签
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
            .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // 移除style标签
            .replace(/<[^>]*>/g, '') // 移除其他HTML标签
            .replace(/&nbsp;/g, ' ') // 替换HTML空格
            .replace(/&lt;/g, '<') // 解码HTML实体
            .replace(/&gt;/g, '>')
            .replace(/&amp;/g, '&')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'");
        } 
        // else {
        //   // 回退方案：从处理后的content中提取纯文本
        //   const tempElement = document.createElement('div');
        //   tempElement.innerHTML = message.content;
        //   textContent = tempElement.textContent || tempElement.innerText;
        // }
        console.log('textContent:',textContent)
        
        // 第一步：调用文本转文件接口
        ElMessage({
          message: '正在生成文件...',
          type: 'info',
          duration: 2000
        });
        
        // 当消息内容为空时，使用messageId；否则使用text
        const params = (!textContent || !textContent.trim()) ? 
          { messageId: message.messageId } : 
          { text: textContent };
        
        const textToFileResponse = await textToFile(params);
        
        if (!textToFileResponse) {
          throw new Error('文件生成失败');
        }
        const fileId = textToFileResponse;
        
        // 第二步：调用下载文件接口
        ElMessage({
          message: '正在下载文件...',
          type: 'info',
          duration: 2000
        });
        
        // 创建 Blob 对象
        const blob = new Blob([fileId], { 
          type: 'application/octet-stream'
        });
        
        // 创建下载链接
        const downloadLink = document.createElement('a');
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = `AI消息_${new Date().getTime()}.docx`;
        
        // 触发下载
        document.body.appendChild(downloadLink);
        downloadLink.click();
        
        // 清理
        document.body.removeChild(downloadLink);
        URL.revokeObjectURL(downloadLink.href);
        
        ElMessage({
          message: '下载成功',
          type: 'success',
          duration: 2000
        });
        
      } catch (error) {
        console.error('下载消息失败:', error);
        ElMessage({
          message: error.message || '下载失败，请重试',
          type: 'error',
          duration: 3000
        });
      } finally {
        // 清除下载状态
        message.downloadLoading = false;
      }
    },
    
    /**
     * 文件上传前验证
     */
    beforeUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        ElMessage.error('文件大小不能超过 50MB!');
        return false;
      }
      return true;
    },
    
    /**
     * 文件上传成功处理
     */
    handleUploadSuccess(response, file, uploadType) {
      if (response.code === 200) {
        const { type, extension } = this.getFileType(file.name);

        let fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name,
          raw: file.raw, // 保存原始文件对象用于预览
          size: file.size
        }
        
        if (uploadType === 'template') {
          this.officeForm.referenceTemplate.push(fileInfo);
        }else if(uploadType === 'reference'){
          this.officeForm.referenceMaterials.push(fileInfo)
        }
        
        // 将文件信息添加到数组中
        this.uploadedFiles.push(fileInfo);
        
        ElMessage.success({
          message: '文件上传成功',
          duration: 2000
        });
        
        // 更新消息区域高度
        this.updateMessagesAreaHeight();
      } else {
        ElMessage.error('文件上传失败');
      }
    },
    
    /**
     * 文件上传失败处理
     */
    handleUploadError() {
      ElMessage.error('文件上传失败，请重试');
    },
    
    /**
     * 获取文件类型
     */
    getFileType(filename) {
      const extension = filename.split('.').pop().toUpperCase();
      
      const typeMap = {
        DOCUMENT: ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
        IMAGE: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
        AUDIO: ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
        VIDEO: ['MP4', 'MOV', 'MPEG', 'MPGA']
      }
      
      for (const [type, extensions] of Object.entries(typeMap)) {
        if (extensions.includes(extension)) {
          return {
            type,
            extension
          }
        }
      }
      
      return {
        type: 'CUSTOM',
        extension
      }
    },

    /**
     * 获取文件图标
     */
    getFileIcon(fileType) {
      const icons = {
        'DOCUMENT': '📄',
        'IMAGE': '🖼️',
        'AUDIO': '🎵',
        'VIDEO': '🎥',
        'CUSTOM': '📎'
      }
      return icons[fileType] || '📎'
    },

    /**
     * 预览文件
     */
    previewFile(file) {
      // 图片预览
      if (file.fileType === 'IMAGE') {
        const img = new Image()
        img.src = URL.createObjectURL(file.raw)
        
        // 创建图片预览实例
        const container = document.createElement('div')
        const vnode = createVNode(ElImageViewer, {
          urlList: [img.src],
          onClose: () => {
            render(null, container)
            document.body.removeChild(container)
          }
        })
        
        document.body.appendChild(container)
        render(vnode, container)
      } 
      // PDF预览
      else if (file.fileType === 'DOCUMENT' && ['PDF'].includes(file.extension)) {
        window.open(URL.createObjectURL(file.raw))
      } 
      // 文本文件预览
      else if (file.fileType === 'DOCUMENT' && ['TXT', 'MD', 'HTML', 'CSV'].includes(file.extension)) {
        const reader = new FileReader()
        reader.onload = (e) => {
          ElMessageBox.alert(e.target.result, '文件预览', {
            customClass: 'text-preview-dialog',
            dangerouslyHtmlString: file.extension === 'HTML'
          })
        }
        reader.readAsText(file.raw)
      }
      // 音频预览
      else if (file.fileType === 'AUDIO') {
        const audio = new Audio(URL.createObjectURL(file.raw))
        audio.play()
      }
      // 视频预览
      else if (file.fileType === 'VIDEO') {
        ElMessageBox.alert(
          `<video controls style="max-width: 100%">
            <source src="${URL.createObjectURL(file.raw)}" type="video/${file.extension.toLowerCase()}">
          </video>`,
          '视频预览',
          {
            dangerouslyHtmlString: true,
            customClass: 'video-preview-dialog'
          }
        )
      }
      else {
        ElMessage.info('该文件类型暂不支持预览')
      }
    },

    /**
     * 删除文件
     */
    removeFile(index) {
      this.uploadedFiles.splice(index, 1);
      // 更新消息区域高度
      this.updateMessagesAreaHeight();
    },
    handleTagTemplateDelete(file) {
      this.officeForm.referenceTemplate = [];
      this.uploadedFiles = this.uploadedFiles.filter(f => f.fileId !== file.fileId);
      this.updateMessagesAreaHeight();
    },
    
    /**
     * Markdown 解析
     */
    parseMarkdown(content) {
      if (!content) return ''
      try {
        // 替换图片相对路径为绝对路径
        let processedContent = content.replace(/!\[([^\]]*)\]\(\/files\//g, '![$1](https://ai.gxgeq.com/files/');
        
        // 修复标题格式：确保 ### 后面有空格，将 ###1. 转换为 ### 1.
        processedContent = processedContent.replace(/^(#+)(\d+\.)/gm, '$1 $2');
        
        // 修复标题格式：确保 ### 后面有空格，将 ###文字 转换为 ### 文字
        processedContent = processedContent.replace(/^(#+)([^#\s])/gm, '$1 $2');
        
        // 在HTML标签后面添加空行，确保markdown解析器能正确识别后面的markdown语法
        processedContent = processedContent.replace(/(<\/[^>]+>)([^\n\r])/g, '$1\n\n$2');
        
        // 只对行首的数字列表进行转义，但排除标题中的数字
        processedContent = processedContent.replace(/^(\d+)\.(\s)/gm, (match, num, space, offset, string) => {
          // 检查这一行是否是标题（前面有#）
          const lineStart = string.lastIndexOf('\n', offset) + 1;
          const beforeNum = string.substring(lineStart, offset);
          if (beforeNum.match(/^#+\s+/)) {
            return match; // 如果是标题，不转义
          }
          return num + '\\.' + space; // 否则转义
        });
        
        return marked(processedContent, {
          breaks: true, // 支持换行
          sanitize: false, // 允许HTML标签
          gfm: true, // 启用 GitHub 风格的 markdown
          pedantic: false, // 不那么严格的解析
        })
      } catch (e) {
        console.error('Markdown parsing error:', e)
        return content
      }
    },
    
    /**
     * 清空消息
     */
    clearMessages() {
      this.messages = [];
    },
    
    /**
     * 添加消息
     */
    addMessage(message) {
      this.messages.push(message);
    },

    /**
     * 获取文档图标类型
     */
    getDocumentIconType(fileName) {
      if (!fileName) return 'text';
      
      const extension = fileName.split('.').pop().toLowerCase();
      
      if (extension === 'pdf') {
        return 'pdf';
      } else if (['doc', 'docx', 'docs'].includes(extension)) {
        return 'word';
      } else {
        return 'text';
      }
    },

    /**
     * 将文档按名称分组，合并同一文档的多个分块
     */
    getGroupedDocuments(documents) {
      if (!documents || !Array.isArray(documents)) {
        return [];
      }
      
      const groupedMap = new Map();
      
      documents.forEach(doc => {
        const docName = doc.document_name || '未知文档';
        
        if (!groupedMap.has(docName)) {
          groupedMap.set(docName, {
            name: docName,
            chunks: []
          });
        }
        
        groupedMap.get(docName).chunks.push({
          content: doc.content,
          // 保留原始文档的其他属性
          ...doc
        });
      });
      
      // 转换为数组并按文档名排序
      return Array.from(groupedMap.values()).sort((a, b) => a.name.localeCompare(b.name));
    },

    /**
     * 表单弹窗关闭前的处理程序
     * @param {Function} done 关闭对话框的回调函数
     */
    handleDialogClose(done) {
      // 只有当有必填字段时才进行验证
      if (this.hasRequiredFields) {
        // 验证表单值
        const invalidFields = this.validateFormValues();
        if (invalidFields.length > 0) {
          ElMessage.warning(`请正确填写以下字段: ${invalidFields.join(', ')}`);
          return; // 阻止关闭
        }
      }
      
      // 验证通过或没有必填字段，保存表单数据
      this.formStore.saveAgentForm(this.agentId, this.formValues, this.userInputForm);
      
      // 关闭弹窗
      done();
    },

    /**
     * 关闭表单弹窗
     */
    closeFormDialog() {
      // 验证表单值
      // const invalidFields = this.validateFormValues();
      // if (invalidFields.length > 0) {
      //   ElMessage.warning(`请正确填写以下字段: ${invalidFields.join(', ')}`);
      //   return; // 阻止关闭
      // }
      
      this.formDialogVisible = false;
    },

    /**
     * 动态更新消息区域高度
     */
    updateMessagesAreaHeight() {
      this.$nextTick(() => {
        const inputWrapper = document.querySelector('.input-wrapper');
        const messagesContainer = this.$refs.messagesContainer;
        
        if (inputWrapper && messagesContainer) {
          const inputWrapperHeight = inputWrapper.offsetHeight;
          const viewportHeight = window.innerHeight;
          const availableHeight = viewportHeight - inputWrapperHeight - 70; // 70px为额外间距
          
          messagesContainer.style.maxHeight = `${availableHeight}px`;
        }
      });
    },

    /**
     * 监听输入容器高度变化
     */
    observeInputContainer() {
      this.$nextTick(() => {
        const inputWrapper = document.querySelector('.input-wrapper');
        if (inputWrapper) {
          // 创建 ResizeObserver 监听输入容器大小变化
          this.resizeObserver = new ResizeObserver(() => {
            this.updateMessagesAreaHeight();
          });
          
          this.resizeObserver.observe(inputWrapper);
        }
      });
    },
  },
  beforeUnmount() {
    // 清理事件监听器
    window.removeEventListener('resize', this.updateMessagesAreaHeight);
    
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    
    // 清理全局引用
    if (window.chatContainer) {
      delete window.chatContainer;
    }
  }
}
</script>

<style scoped>
.chat-container-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  left:-2.1rem;
}

.messages-area {
  flex: 1;
  overflow-y: auto;
  padding-top: 1rem;
  scroll-behavior: smooth;
}

/* 隐藏滚动条但允许滚动 */
.messages-area::-webkit-scrollbar {
  width: 0;
}

.messages-area {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.messages-area::-webkit-scrollbar-thumb {
  background: transparent;
}

.input-wrapper {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50%;
  z-index: 20;
  padding-bottom: 20px;
}

.input-container {
  min-height: 200px;
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.file-preview-container {
  max-height: 80px;
  overflow-y: auto;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.input-area {
  flex: 1;
  min-height: 80px;
}

.office-area{
  flex: 1;
  /* min-height: 90px; */
  /* border: 1px solid green; */
}

.input-textarea {
  height: 100%;
  min-height: 60px;
}

.bottom-toolbar {
  margin-top: auto;
  flex-shrink: 0;
  min-height: 60px;
}

/* 文件预览项样式 */
.file-preview-item {
  display: inline-flex;
  align-items: center;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  margin: 2px;
}

.file-preview-item:hover {
  background: #e5e7eb;
}

.func-container {
  bottom: 0;
  right: 0;
}

/* 添加上传按钮样式 */
.upload-button {
  display: inline-block;
  position: relative;
}

.upload-button :deep(.el-upload) {
  display: block;
}

.upload-button :deep(.el-upload-list) {
  display: none;
}

/* 添加文件类型图标样式 */
.file-icon {
  margin-right: 4px;
}

.uploaded-files-preview {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.file-preview-item {
  transition: all 0.3s ease;
}

.file-preview-item:hover {
  background-color: #f3f4f6;
}

.file-preview-item button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.file-preview-item:hover button {
  opacity: 1;
}

/* 修改预览相关样式 */
:deep(.preview-image) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2100;
}

/* 添加按钮过渡效果 */
button {
  transition: all 0.3s ease;
}

button:active {
  transform: scale(0.95);
}

/* 修改输入框滚动条样式 */
.input-textarea::-webkit-scrollbar {
  width: 0;
}

.input-textarea {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.input-textarea::-webkit-scrollbar-thumb {
  background: transparent;
}

/* 消息样式调整 */
.flex.mb-8 {
  width: 100%;
  font-size: 1em; /* 放大字体 */
}

/* 文档工具提示样式 */
:deep(.doc-tooltip) {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  padding: 8px 12px;
}

:deep(.el-popper.is-light) {
  max-height: 40vh;
  overflow-y: auto;
}

/* AI回答内容字体样式 */
.ai-content {
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 预设问题区域样式 */
.suggested-questions-container {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.suggested-questions-wrapper {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 4px 0;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.suggested-questions-wrapper::-webkit-scrollbar {
  height: 6px;
}

.suggested-questions-wrapper::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.suggested-questions-wrapper::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.suggested-questions-wrapper::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.suggested-question-item {
  flex-shrink: 0;
  padding: 12px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  font-size: 14px;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  user-select: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 180px;
}

.suggested-question-item:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.suggested-question-item:active {
  transform: translateY(0);
}

/* 预设问题图片样式 */
.question-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.question-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 预设问题文本样式 */
.question-text {
  text-align: center;
  font-weight: 500;
  line-height: 1.4;
  white-space: normal;
  word-break: break-word;
}

/* 分块下拉菜单样式 */
:deep(.chunk-dropdown-menu) {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.chunk-dropdown-item) {
  padding: 8px 12px !important;
  min-height: auto !important;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

:deep(.chunk-dropdown-item:last-child) {
  border-bottom: none;
}

:deep(.chunk-dropdown-item:hover) {
  background-color: #f8fafc !important;
  color: #1f2937 !important;
}

:deep(.chunk-dropdown-item span) {
  color: #374151;
  font-size: 13px;
  line-height: 1.4;
}

:deep(.chunk-dropdown-item .text-gray-400) {
  color: #9ca3af !important;
  font-size: 11px !important;
}

/* 文档引用区域样式优化 */
.document-reference-item {
  padding: 6px 0;
  border-bottom: 1px solid #f3f4f6;
}

.document-reference-item:last-child {
  border-bottom: none;
}

/* 分块按钮样式 */
.chunk-button {
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  background-color: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.chunk-button:hover {
  background-color: #e2e8f0;
  border-color: #cbd5e1;
}

/* 用户消息中的文件链接样式 */
:deep(.file-link) {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease;
  border-bottom: 1px solid transparent;
}

:deep(.file-link:hover) {
  color: #ffffff;
  border-bottom-color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
}

.file-tag{
  max-width: 100%;
  display: inline-flex;
  align-items: center;
  /* margin-right: 4px; */
  vertical-align: middle;
  /* border: 1px solid red; */


}

:deep(.el-tag__content){
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  /* border: 1px solid black; */
}

</style>