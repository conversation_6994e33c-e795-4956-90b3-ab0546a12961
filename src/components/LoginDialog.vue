<template>
  <el-dialog
    v-model="dialogVisible"
    title="登录"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    class="login-dialog"
  >
    <el-form
      :model="loginForm"
      label-width="80px"
      :rules="rules"
      ref="formRef"
    >
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="loginForm.username"
          placeholder="请输入用户名"
          prefix-icon="User"
          @keyup.enter="handleLogin"
        />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          placeholder="请输入密码"
          prefix-icon="Lock"
          show-password
          @keyup.enter="handleLogin"
        />
      </el-form-item>
      <!--<el-form-item label="登录类型" prop="type">
        <el-radio-group v-model="loginForm.type">
          <el-radio :label="1">普通用户</el-radio>
          <el-radio :label="2">管理员</el-radio>
        </el-radio-group>
      </el-form-item>-->
    </el-form>
    <template #footer>
      <div class="flex justify-center">
        <el-button
          type="primary"
          @click="handleLogin"
          :loading="loading"
          class="w-64 custom-login-btn"
        >
          {{ loading ? '登录中...' : '登录' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed } from 'vue'
import { login, getUserPermit } from '@/api/auth'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import {setToken} from "@/utils/auth.js";
import {loginUserInfo} from "@/api/auth.js";
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'login-success'])
const formRef = ref(null)
const loading = ref(false)

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const loginForm = ref({
  username: 'user',
  password: '',
  type: 1
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择登录类型', trigger: 'change' }
  ]
}

const handleLogin = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true
    const res = await login(loginForm.value)
    localStorage.setItem('token', res.data.tokenValue)
    localStorage.setItem('userInfo', JSON.stringify(res.data))
    // 根据返回的 code 判断登录状态
    if (res.code === 200) {
      ElMessage.success('登录成功')
      // 使用统一的token管理，保存tokenValue到access_token
      setToken(res.data.tokenValue)
      await userStore.fetchUserInfo()
      const permit = await getUserPermit()
      localStorage.setItem('userPermit', JSON.stringify(permit.data))

      // 触发登录成功事件，通知管理端按钮刷新权限
      window.dispatchEvent(new CustomEvent('login-success'))
      
      // 发射登录成功事件给父组件
      emit('login-success', res)

      dialogVisible.value = false
      // 清空表单
      loginForm.value.password = ''
    } else {
      // 如果 code 不是 200，抛出错误
      throw new Error(res.msg || '登录失败')
    }
  } catch (error) {
    // 处理错误信息
    if (error.response?.status === 500) {
      ElMessage.error('服务器错误，请稍后重试')
    } else {
      ElMessage.error(error.message || '登录失败，请重试')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-dialog :deep(.el-dialog__header) {
  margin-right: 0;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.login-dialog :deep(.el-dialog__title) {
  font-size: 20px;
  color: #333;
  font-weight: 600;
}

.login-dialog :deep(.el-dialog__body) {
  padding: 30px 20px;
}

.login-dialog :deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
}

.login-dialog :deep(.custom-login-btn) {
  height: 40px;
  font-size: 16px;
  background-color: #4e54c8; /* 紫蓝色 */
  border-color: #4e54c8;
  width: 200px; /* 设置按钮宽度为中等 */
}

.login-dialog :deep(.custom-login-btn:hover) {
  background-color: #3f44b5; /* 悬停时的颜色 */
  border-color: #3f44b5;
}

.login-dialog :deep(.el-input__wrapper) {
  height: 40px;
}

.login-dialog :deep(.el-radio-group) {
  display: flex;
  gap: 20px;
}

.login-dialog :deep(.el-radio__label) {
  font-size: 14px;
}

/* 调整单选按钮的颜色为紫蓝色 */
.login-dialog :deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #4e54c8;
  border-color: #4e54c8;
}

.login-dialog :deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #4e54c8;
}
</style>
