<template>
  <el-dialog
    v-model="visible"
    :title="false"
    width="80%"
    max-width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <!-- 自定义头部 -->
    <template #header>
      <div class="custom-header">
        <div class="header-tabs">
          <el-button
            :type="currentTab === 'feedback' ? 'primary' : ''"
            :plain="currentTab !== 'feedback'"
            @click="switchTab('feedback')"
          >
            吐槽
          </el-button>
          <el-button
            :type="currentTab === 'history' ? 'primary' : ''"
            :plain="currentTab !== 'history'"
            @click="switchTab('history')"
          >
            历史查询
          </el-button>
        </div>
      </div>
    </template>

    <!-- 意见吐槽页面 -->
    <div v-if="currentTab === 'feedback'">
      <el-form
        ref="feedbackForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="feedback-form"
      >
        <!-- 现有的表单内容保持不变 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="功能点" prop="featurePoint">
              <el-select
                v-model="formData.featurePoint"
                placeholder="请选择吐槽的功能点"
                style="width: 100%"
              >
                <el-option
                  v-for="item in dictData.featurePoints"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="吐槽人" prop="reporterName">
              <el-input
                v-model="formData.reporterName"
                placeholder="请输入吐槽人姓名"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系手机" prop="contactPhone">
              <el-input
                v-model="formData.contactPhone"
                placeholder="请输入联系手机"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="问题分类" prop="problemClassification">
              <el-select
                v-model="formData.problemClassification"
                placeholder="请选择问题分类"
                style="width: 100%"
              >
                <el-option
                  v-for="item in dictData.problemClassifications"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="问题描述" prop="problemDescription">
          <el-input
            v-model="formData.problemDescription"
            type="textarea"
            :rows="4"
            placeholder="请详细描述您遇到的问题"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="图片">
          <el-upload
            ref="imageUpload"
            :file-list="imageFileList"
            :on-change="handleImageChange"
            :on-remove="handleImageRemove"
            accept="image/*"
            action="#"
            :auto-upload="false"
            list-type="picture-card"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>

    <!-- 历史查询页面 -->
    <div v-if="currentTab === 'history'">
      <!-- 查询条件 -->
      <div class="query-container">
        
          <!--<div class="query-title">
            <el-icon class="query-icon"><Search /></el-icon>
            <span>查询条件</span>
          </div>-->
          <el-button
            type="text"
            @click="toggleQueryForm"
            class="collapse-btn"
          >
            <el-icon>
              <component :is="showQueryForm ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
        
        
        <el-collapse-transition>
          <el-form
            v-show="showQueryForm"
            ref="queryForm"
            :model="queryParams"
            class="query-form"
            label-width="80px"
          >
            <!-- 查询条件 -->
            <div class="query-group">
              <div class="group-title">
                <el-icon class="group-icon"><Search /></el-icon>
                <span>筛选条件</span>
              </div>
              <!-- 查询条件行 -->
              <el-row :gutter="12">
                <el-col :span="6">
                  <el-form-item label="功能点">
                    <el-select
                      v-model="queryParams.featurePoint"
                      placeholder="请选择功能点"
                      clearable
                      style="width: 100%"
                      size="small"
                    >
                      <el-option
                        v-for="item in dictData.featurePoints"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="问题分类">
                    <el-select
                      v-model="queryParams.problemClassification"
                      placeholder="请选择问题分类"
                      clearable
                      style="width: 100%"
                      size="small"
                    >
                      <el-option
                        v-for="item in dictData.problemClassifications"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="处理状态">
                    <el-select
                      v-model="queryParams.processingStatus"
                      placeholder="请选择处理状态"
                      clearable
                      style="width: 100%"
                      size="small"
                    >
                      <el-option
                        v-for="item in dictData.processingStatuses"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label=" ">
                    <div class="query-actions-inline">
                      <el-button 
                        type="primary" 
                        @click="handleQuery"
                        :icon="Search"
                        class="query-btn-compact"
                        size="small"
                      >
                        查询
                      </el-button>
                      <el-button 
                        @click="handleReset"
                        :icon="Refresh"
                        class="reset-btn-compact"
                        size="small"
                      >
                        重置
                      </el-button>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-collapse-transition>
      </div>

      <!-- 历史列表 -->
      <el-table
        v-loading="historyLoading"
        :data="historyList"
        style="width: 100%"
        class="history-table"
      >
        <el-table-column prop="featurePoint" label="功能点" width="100" />
        <el-table-column prop="problemClassification" label="问题分类" width="100" />
        <el-table-column prop="problemDescription" label="问题描述" show-overflow-tooltip />
        <el-table-column prop="createTime" label="吐槽时间" width="150">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="processingStatus" label="处理状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.processingStatus === 1 ? 'success' : 'warning'">
              {{ scope.row.processingStatus === 1 ? '已处理' : '未处理' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="satisfaction" label="满意度" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.satisfaction === 1" type="success">满意</el-tag>
            <el-tag v-else-if="scope.row.satisfaction === 0" type="danger">不满意</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              v-if="scope.row.processingStatus === 1 && !scope.row.satisfaction"
              type="success"
              size="small"
              @click="handleRateSatisfaction(scope.row)"
            >
              评价
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="吐槽详情"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="detailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="功能点">
            {{ detailData.featurePoint }}
          </el-descriptions-item>
          <el-descriptions-item label="问题分类">
            {{ detailData.problemClassification }}
          </el-descriptions-item>
          <el-descriptions-item label="吐槽人姓名">
            {{ detailData.reporterName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系手机">
            {{ detailData.contactPhone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="吐槽时间" :span="2">
            {{ formatDate(detailData.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="问题描述" :span="2">
            {{ detailData.problemDescription }}
          </el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="detailData.processingStatus === 1 ? 'success' : 'warning'">
              {{ detailData.processingStatus === 1 ? '已处理' : '未处理' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="满意度">
            <el-tag v-if="detailData.satisfaction === 1" type="success">满意</el-tag>
            <el-tag v-else-if="detailData.satisfaction === 0" type="danger">不满意</el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 相关图片 -->
        <div v-if="detailData.imageIds && detailData.imageIds.trim()" class="image-section">
          <h4>相关图片</h4>
          <div class="image-preview">
            <el-image
              v-for="(imageId, index) in detailData.imageIds.split(',')"
              :key="index"
              :src="getImageUrl(imageId.trim())"
              :preview-src-list="detailData.imageIds.split(',').map(id => getImageUrl(id.trim()))"
              :initial-index="index"
              fit="cover"
              class="detail-image"
              @load="onImageLoad(imageId.trim())"
              @error="onImageError(imageId.trim())"
            />
          </div>
        </div>
        
        <!-- 当没有图片时显示的信息 -->
        <div v-else class="no-image-section">
          <p>无相关图片</p>
        </div>
        
        <!-- 处理意见 -->
        <div v-if="detailData.opinion" class="opinion-section">
          <h4>处理意见</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="处理人">
              {{ detailData.opinion.processorName }}
            </el-descriptions-item>
            <el-descriptions-item label="处理时间">
              {{ formatDate(detailData.opinion.processingTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="处理意见" :span="2">
              {{ detailData.opinion.processingOpinion }}
            </el-descriptions-item>
            <el-descriptions-item v-if="detailData.opinion.remarks" label="备注" :span="2">
              {{ detailData.opinion.remarks }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>

    <!-- 满意度评价弹窗 -->
    <el-dialog
      v-model="satisfactionVisible"
      title="满意度评价"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="satisfaction-content">
        <p>请对此次吐槽处理结果进行评价：</p>
        <el-radio-group v-model="satisfactionForm.satisfaction">
          <el-radio :label="1">满意</el-radio>
          <el-radio :label="0">不满意</el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="satisfactionVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitSatisfaction">提交</el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="currentTab === 'feedback'"
          type="primary"
          @click="handleSubmit"
        >
          提交吐槽
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Plus, Search, ArrowUp, ArrowDown, Document, User, Calendar, Setting, Grid, CircleCheck, Star, Phone, Refresh } from '@element-plus/icons-vue'
import { 
  addUserFeedback, 
  uploadFile, 
  getUserFeedbackList, 
  getUserFeedbackDetail,
  submitFeedbackSatisfaction
} from '@/api/feedback'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(false)
const feedbackForm = ref(null)
const imageUpload = ref(null)
const showQueryForm = ref(true)

// 监听prop变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  // 当弹窗打开时，如果是历史查询页面，加载数据
  if (newVal && currentTab.value === 'history') {
    getHistoryFeedbackList()
  }
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 表单数据
const formData = reactive({
  featurePoint: '',
  reporterName: '',
  contactPhone: '',
  problemClassification: '',
  problemDescription: '',
  imageIds: ''
})

// 表单验证规则
const rules = {
  featurePoint: [{ required: true, message: '请选择吐槽的功能点', trigger: 'change' }],
  reporterName: [{ required: false, message: '请输入吐槽人姓名', trigger: 'blur' }],
  contactPhone: [
    { required: false, message: '请输入联系手机', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  problemClassification: [{ required: true, message: '请选择问题分类', trigger: 'change' }],
  problemDescription: [
    { required: true, message: '请输入问题描述', trigger: 'blur' },
    { min: 10, message: '问题描述至少10个字符', trigger: 'blur' }
  ]
}

// 文件列表
const imageFileList = ref([])
const uploadedImageIds = ref([])

// 页面切换
const currentTab = ref('feedback')

const switchTab = (tab) => {
  currentTab.value = tab
  if (tab === 'history') {
    getHistoryFeedbackList()
  }
}

const toggleQueryForm = () => {
  showQueryForm.value = !showQueryForm.value
}

// 图片选择处理
const handleImageChange = async (file, fileList) => {
  // 只处理新添加的文件
  if (file.status === 'ready') {
    const rawFile = file.raw
    const isImage = rawFile.type.startsWith('image/')
    const isLt5M = rawFile.size / 1024 / 1024 < 5

    if (!isImage) {
      ElMessage.error('只能上传图片文件!')
      // 从文件列表中移除无效文件
      imageFileList.value = imageFileList.value.filter(f => f.uid !== file.uid)
      return
    }
    if (!isLt5M) {
      ElMessage.error('图片文件大小不能超过5MB!')
      // 从文件列表中移除无效文件
      imageFileList.value = imageFileList.value.filter(f => f.uid !== file.uid)
      return
    }
    
    try {
      // 设置上传状态
      file.status = 'uploading'
      
      const response = await uploadFile(rawFile)
      if (response.code === 200) {
        ElMessage.success('图片上传成功!')
        // 根据上传文件接口返回的数据结构，获取fileId
        if (response.data && response.data.fileId) {
          uploadedImageIds.value.push(response.data.fileId)
          // 将图片ID数组转换为逗号分隔的字符串
          formData.imageIds = uploadedImageIds.value.join(',')
          
          // 为文件对象添加预览URL
          const previewUrl = getImageUrl(response.data.fileId)
          
          // 更新文件状态为成功，并设置预览URL
          file.status = 'success'
          file.response = response
          file.url = previewUrl  // 添加预览URL
          
          // 更新文件列表中对应文件的URL
          const fileIndex = imageFileList.value.findIndex(f => f.uid === file.uid)
          if (fileIndex !== -1) {
            imageFileList.value[fileIndex].url = previewUrl
          }
        }
      }
    } catch (error) {
      ElMessage.error('图片上传失败!')
      // 设置上传失败状态
      file.status = 'error'
      // 从文件列表中移除失败的文件
      imageFileList.value = imageFileList.value.filter(f => f.uid !== file.uid)
    }
  }
}



const handleImageRemove = (file) => {
  ElMessage.info('图片已移除')
  // 从上传的图片ID列表中移除对应的ID
  if (file.response && file.response.data && file.response.data.fileId) {
    uploadedImageIds.value = uploadedImageIds.value.filter(id => id !== file.response.data.fileId)
    formData.imageIds = uploadedImageIds.value.join(',')
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  imageFileList.value = []
  uploadedImageIds.value = []
  feedbackForm.value?.resetFields()
}

// 提交吐槽
const handleSubmit = async () => {
  try {
    await feedbackForm.value?.validate()
    
    await ElMessageBox.confirm(
      '确定要提交意见吐槽吗？',
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await addUserFeedback(formData)
    if (response.code === 200) {
      ElMessage.success('意见吐槽已提交!')
      handleClose()
    } else {
      ElMessage.error(response.msg || '提交失败!')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('提交失败，请重试!')
    }
  }
}

// 历史查询相关
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  featurePoint: '',
  problemClassification: '',
  processingStatus: null
})
const historyLoading = ref(false)
const historyList = ref([])
const total = ref(0)

// 字典数据（固定选项）
const dictData = reactive({
  featurePoints: [
    { label: '对话', value: '对话' },
    { label: '上传文件', value: '上传文件' },
    { label: '公文智能体', value: '公文智能体' },
    { label: '知识库管理', value: '知识库管理' },
    { label: '工作台', value: '工作台' },
    { label: '其他', value: '其他' }
  ],
  problemClassifications: [
    { label: '服务质量', value: '服务质量' },
    { label: '系统功能', value: '系统功能' },
    { label: '使用体验', value: '使用体验' },
    { label: '界面设计', value: '界面设计' },
    { label: '性能问题', value: '性能问题' },
    { label: '数据准确性', value: '数据准确性' },
    { label: '其他', value: '其他' }
  ],
  processingStatuses: [
    { label: '未处理', value: 0 },
    { label: '已处理', value: 1 }
  ],
  satisfactions: [
    { label: '满意', value: 1 },
    { label: '不满意', value: 0 }
  ]
})

const handleQuery = async () => {
  queryParams.pageNum = 1
  await getHistoryFeedbackList()
}

const handleReset = () => {
  queryParams.featurePoint = ''
  queryParams.problemClassification = ''
  queryParams.processingStatus = null
  handleQuery()
}



const getHistoryFeedbackList = async () => {
  historyLoading.value = true
  try {
    // 构建查询参数
    const params = {
      ...queryParams
    }
    
    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await getUserFeedbackList(params)
    if (response.code === 200) {
      historyList.value = response.data.rows || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取历史吐槽失败!')
    }
  } catch (error) {
    ElMessage.error('获取历史吐槽失败，请重试!')
  } finally {
    historyLoading.value = false
  }
}

const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getHistoryFeedbackList()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getHistoryFeedbackList()
}

const detailVisible = ref(false)
const detailData = ref(null)

const handleViewDetail = async (row) => {
  detailVisible.value = true
  try {
    const response = await getUserFeedbackDetail(row.feedbackId)
    if (response.code === 200) {
      detailData.value = response.data
    } else {
      ElMessage.error(response.msg || '获取详情失败!')
    }
  } catch (error) {
    ElMessage.error('获取详情失败，请重试!')
  }
}

const satisfactionVisible = ref(false)
const satisfactionForm = reactive({
  feedbackId: '',
  satisfaction: null
})

const handleRateSatisfaction = (row) => {
  satisfactionForm.feedbackId = row.feedbackId
  satisfactionForm.satisfaction = null
  satisfactionVisible.value = true
}

const handleSubmitSatisfaction = async () => {
  if (!satisfactionForm.feedbackId) {
    ElMessage.error('吐槽ID不存在!')
    return
  }
  if (satisfactionForm.satisfaction === null) {
    ElMessage.error('请选择满意度!')
    return
  }
  try {
    const response = await submitFeedbackSatisfaction(satisfactionForm.feedbackId, {
      satisfaction: satisfactionForm.satisfaction
    })
    if (response.code === 200) {
      ElMessage.success('满意度评价已提交!')
      satisfactionVisible.value = false
      handleQuery() // 刷新列表
    } else {
      ElMessage.error(response.msg || '评价失败!')
    }
  } catch (error) {
    ElMessage.error('评价失败，请重试!')
  }
}

// 日期格式化
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 获取图片URL
const getImageUrl = (imageId) => {
  const url = `${window.location.origin}/api/v1/public/file/get?fileId=${imageId}`
  return url
}

// 图片加载成功回调
const onImageLoad = (imageId) => {
  // 图片加载成功
}

// 图片加载失败回调
const onImageError = (imageId) => {
  // 图片加载失败
}


</script>

<style scoped>
.feedback-form {
  max-height: 60vh;
  overflow-y: auto;
}

.feedback-form .el-form-item {
  margin-bottom: 18px;
}

.feedback-form .el-textarea {
  font-family: inherit;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}

.custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-tabs .el-button {
  margin-right: 10px;
}

/* 查询容器 */
.query-container {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.query-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  color: white;
  cursor: pointer;
  user-select: none;
}

.query-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.query-icon {
  font-size: 18px;
}

.collapse-btn {
  color: white !important;
  border: none;
  background: none;
  padding: 4px;
  transition: transform 0.3s ease;
}

.collapse-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.1);
}

/* 查询表单 */
.query-form {
  padding: 12px 16px;
  background-color: #ffffff;
  border-radius: 0 0 8px 8px;
}

.query-form .el-form-item {
  margin-bottom: 8px;
}

.query-form .el-form-item__label {
  color: #303133;
  font-weight: 600;
  font-size: 13px;
  line-height: 1.2;
}

/* 查询分组 */
.query-group {
  margin-bottom: 8px;
  padding: 12px 16px;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.query-group:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-color: #c0c4cc;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 600;
  color: #409eff;
  padding-bottom: 4px;
  border-bottom: 1px solid #e4e7ed;
}

.group-icon {
  font-size: 16px;
  color: #409eff;
}

/* 内联操作按钮 */
.query-actions-inline {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
}

.query-btn-compact {
  min-width: 70px;
  height: 28px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 4px;
  border: none;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.query-btn-compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.4);
}

.reset-btn-compact {
  min-width: 70px;
  height: 28px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 4px;
  background-color: #f0f2f5;
  border: 1px solid #d9d9d9;
  color: #666;
  transition: all 0.3s ease;
}

.reset-btn-compact:hover {
  background-color: #e6f4ff;
  border-color: #40a9ff;
  color: #40a9ff;
  transform: translateY(-1px);
}

/* 表单项样式优化 */
.query-form :deep(.el-select) {
  border-radius: 4px;
}

.query-form :deep(.el-input) {
  border-radius: 4px;
}

.query-form :deep(.el-date-editor) {
  border-radius: 4px;
}

.query-form :deep(.el-select__wrapper) {
  border-radius: 4px;
  transition: all 0.3s ease;
}

.query-form :deep(.el-select__wrapper):hover {
  border-color: #409eff;
  box-shadow: 0 0 0 1px rgba(64, 159, 255, 0.2);
}

.query-form :deep(.el-input__wrapper) {
  border-radius: 4px;
  transition: all 0.3s ease;
}

.query-form :deep(.el-input__wrapper):hover {
  border-color: #409eff;
  box-shadow: 0 0 0 1px rgba(64, 159, 255, 0.2);
}

/* 小尺寸表单项的额外样式优化 */
.query-form :deep(.el-form-item--small .el-form-item__label) {
  font-size: 12px;
  line-height: 28px;
}

.query-form :deep(.el-form-item--small .el-form-item__content) {
  line-height: 28px;
}

.history-table {
  margin-top: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.detail-content, .opinion-section, .image-section {
  margin-top: 20px;
}

.opinion-section h4, .image-section h4 {
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.detail-image {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  cursor: pointer;
}

.satisfaction-content {
  text-align: center;
  padding: 20px;
}

.satisfaction-content p {
  margin-bottom: 20px;
  font-size: 16px;
}
</style> 