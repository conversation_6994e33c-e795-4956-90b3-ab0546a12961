import { get,post,service } from './index'
// 导入数据解析器
import { parseWorkflowEvent, parseMessageFlow, parseFinalMessage } from '@/utils/workflowDataParser'

// dify 原生ai对话
export function chatAi(params){
    return post('/v1/chat-messages', params)
  }

  // ai对话
export function chatMessages(params){
    return post('/v1/public/chat/message', params)
  }

// 删除会话
export function removeConversation(params){
    return post('/v1/public/chat/conversation/remove', params)
}
// 重命名会话
export function renameConversation(params){
    return post('/v1/public/chat/conversation/rename', params)
}

// 修改流式对话接口，使用相对路径
export function streamChatMessages(params) {
  return fetch('/v1/public/chat/message', {  // 使用相对路径，而不是完整的URL
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
    },
    body: JSON.stringify(params)
  });
}

// 智能体广场
export function getAgentList(params){
  return get('/system/roleAgent/agentListByUser', params)
  //return get('/v1/public/agent/page', params)
  }
  // 获取消息反馈
export function getFeedBack(params) {
  return get('v1/public/message/feedback', params)
}
// 获取历史消息
export function getMessageList(params){
    return get('/v1/public/chat/message/list', params)
  }

// 会话列表接口
export function getConversationList(params){
    return get('/v1/public/chat/conversation/list', params)
}
// 文字转语音接口
export function textToSpeech(params){
    return post('/v1/public/chat/textToAudio', params)
}
// 语音转文字接口
export function speechToText(params){
    return post('/v1/public/chat/audioToText', params)
}

// 文本转文件
export function textToFile(params){
  return service({
    url: '/v1/public/chat/textToFile',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 基础配置
const baseOptions = {
  headers: new Headers({
    'Content-Type': 'application/json',
  }),
}

// 获取智能体详情
export function getAgentDetail(params){
    return get('/v1/public/agent/parameters', params)
}

// 上传文件
export function uploadFile(params){
  return post('/v1/public/chat/uploadFile', params)
}

// SSE POST 实现
export const ssePost = (url, fetchOptions, otherOptions) => {
  const {
    onData,
    onCompleted,
    onError,
    getAbortController,
  } = otherOptions

  const abortController = new AbortController()

  // 合并请求选项
  const options = {
    ...baseOptions,
    method: 'POST',
    signal: abortController.signal,
    ...fetchOptions
  }

  if (getAbortController)
    getAbortController(abortController)

  // 处理请求体
  if (options.body)
    options.body = JSON.stringify(options.body)

  // 发起请求
  fetch(url, options)
    .then(async (response) => {
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Server Error')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''
      let isFirstMessage = true

      console.log('开始读取流数据...') // 调试日志

      while (true) { 
        
        const { value, done } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        console.log('收到数据块:', chunk)
        // console.log('收到数据块:', chunk)

        buffer += chunk
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (!line.trim() || !line.startsWith('data:'))
            continue

          try {
            const jsonStr = line.replace(/^data:data:/, 'data:').replace(/^data:/, '').trim()
            console.log('处理后的JSON字符串:', jsonStr)
            // console.log('处理后的JSON字符串:', jsonStr)

            if (!jsonStr) continue

            const data = JSON.parse(jsonStr)
            console.log('解析后的数据:', data)
            // console.log('解析后的数据:', data)

            switch ((data.event || data.eventType)?.toUpperCase()) {
              case 'MESSAGE':
                if (data.answer) {
                  console.log('发送消息:', data.answer)
                  onData?.(
                    data.answer,
                    isFirstMessage,
                    {
                      messageId: data.id,
                      conversationId: data.conversation_id
                    }
                  )
                  isFirstMessage = false
                }
                break

              case 'MESSAGE_END':
                console.log('消息结束，元数据:', data.metadata) // 调试日志
                
                // 处理文档内容，确保每个文档都有content字段
                if (data.metadata && data.metadata.retriever_resources) {
                  // 遍历文档，如果有content_text字段，则添加到content字段
                  data.metadata.retriever_resources = data.metadata.retriever_resources.map(doc => {
                    if (doc.content_text && !doc.content) {
                      doc.content = doc.content_text;
                    }
                    return doc;
                  });
                }
                
                // 将处理后的metadata传递给onCompleted回调
                onCompleted?.(data.metadata)
                break

              case 'ERROR':
                console.error('收到错误:', data.message) // 调试日志
                onError?.(data.message)
                break
                
              default:
                // 处理所有其他事件类型，包括新的工作流事件
                console.log('传递事件到onData:', data.event)
                onData?.(
                  JSON.stringify(data),
                  isFirstMessage,
                  {
                    messageId: data.message_id || data.id,
                    conversationId: data.conversation_id
                  }
                )
                isFirstMessage = false
                break
            }
          } catch (error) {
            console.error('解析错误:', error, '原始行:', line)
          }
        }
      }
    })
    .catch((error) => {
      if (error.name === 'AbortError')
        return

      console.error('请求失败:', error) // 调试日志
      onError?.(error.message)
    })
}

// 聊天消息发送处理函数
export const handleSendMessage = async (message, options = {}) => {
  const {
    onData = () => {},
    onCompleted = () => {},
    onError = () => {},
    agentId,  // 从 options 中获取 agentId
  } = options

  let responseText = ''
  let isInThinking = false
  let thinkingDetails = ''

  return ssePost(
    '/api/v1/public/chat/message',
    {
      body: {
        agentId: agentId,  // 使用传入的 agentId，而不是硬编码的值
        query: message,
        ...options
      },
      headers: {
        'Content-Type': 'application/json',
        'satoken': localStorage.getItem('token'),
      },
    },
    {
      onData: (data, isFirstMessage, { messageId, conversationId }) => {
        // 检查是否包含新格式的 <think> 标签
        if (data.includes('<think>')) {
          console.log('🎯 AI.js 检测到新格式 <think> 标签')
          
          // 提取 <think> 标签内的内容和后面的实际内容
          const thinkStart = data.indexOf('<think>')
          const thinkEnd = data.indexOf('</think>')
          
          if (thinkStart !== -1 && thinkEnd !== -1) {
            // 完整的 think 标签已存在，直接处理
            const beforeThink = data.substring(0, thinkStart)
            const thinkingContent = data.substring(thinkStart + 7, thinkEnd) // 7是'<think>'的长度
            const afterThink = data.substring(thinkEnd + 8) // 8是'</think>'的长度
            
            // 将思考内容包装为 details 格式
            const wrappedThinking = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;"><summary>Thinking...</summary>\n${thinkingContent}\n</details>`
            
            responseText = beforeThink + wrappedThinking + afterThink
            console.log('🎯 完整处理后的内容:', responseText)
          } else {
            // 如果没有完整的标签，按原来的逻辑处理
            responseText += data
          }
        }
        // 检查是否是thinking开始（原有逻辑）
        else if (data.includes('<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open>')) {
          isInThinking = true
          thinkingDetails = data
          responseText = thinkingDetails
        }
        // 如果正在thinking中
        else if (isInThinking) {
          // 检查是否包含结束标签
          if (data.includes('</details>')) {
            // 分割结束标签前后的内容
            const parts = data.split('</details>')
            thinkingDetails += parts[0] + '</details>'
            responseText = thinkingDetails

            // 如果有实际内容，添加到thinking后面
            if (parts[1]) {
              responseText += parts[1]
              isInThinking = false
            }
          } else {
            // 继续累加到thinking内容
            thinkingDetails += data
            responseText = thinkingDetails
          }
        }
        // thinking已结束，添加实际内容
        else {
          responseText += data
        }

        // 每次更新都发送到UI
        onData(responseText, messageId, conversationId)
      },
      onCompleted(metadata) {
        // 将responseText和metadata传递给回调
        onCompleted(responseText, metadata)
      },
      onError(error) {
        onError(error)
      },
    }
  )
}

// 发送公文办公消息
export function sendOfficeMessage(params){
  return post('/v1/workflow/tryRunningStream', params)
}

// 聊天消息发送处理函数 - Pro版本，支持工作流节点展示
export const handleSendMessagePro = async (message, options = {}) => {
  const {
    onWorkflowEvent = () => {},
    onMessageFlow = () => {},
    onCompleted = () => {},
    onError = () => {},
    agentId,
  } = options

  let responseText = ''
  let isInThinking = false
  let thinkingDetails = ''
  let actualContentStarted = false

  return ssePost(
    '/api/v1/public/chat/message',
    {
      body: {
        agentId: agentId,
        query: message,
        ...options
      },
      headers: {
        'Content-Type': 'application/json',
        'satoken': localStorage.getItem('token'),
      },
    },
    {
      onData: (data, isFirstMessage, { messageId, conversationId }) => {
        console.log('接收到原始数据:', data)
        
        // 尝试解析为JSON，如果失败则作为纯文本处理
        let parsedData
        try {
          parsedData = JSON.parse(data)
          console.log('解析为JSON成功:', parsedData)
          
          // 检查解析结果是否为基本类型（数字、字符串等），如果是则作为消息内容处理
          if (typeof parsedData !== 'object' || parsedData === null) {
            console.log('解析结果是基本类型，作为消息内容处理:', parsedData)
            
            // 将基本类型转换为字符串并添加到响应文本中
            const dataStr = String(parsedData)
            responseText += dataStr
            onMessageFlow(responseText, messageId, conversationId)
            return
          }
        } catch (error) {
          // 如果不是JSON，则作为纯文本消息处理
          console.log('数据不是JSON格式，作为消息内容处理:', data)
          
          // 检查是否是thinking开始
          if (data.includes('<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open>')) {
            isInThinking = true
            thinkingDetails = data
            responseText = thinkingDetails
            actualContentStarted = false
          }
          // 如果正在thinking中
          else if (isInThinking) {
            // 检查是否包含结束标签
            if (data.includes('</details>')) {
              // 分割结束标签前后的内容
              const parts = data.split('</details>')
              thinkingDetails += parts[0] + '</details>'
              responseText = thinkingDetails

              // 如果有实际内容，添加到thinking后面
              if (parts[1]) {
                responseText += parts[1]
                isInThinking = false
                actualContentStarted = true
              } else {
                // 没有实际内容，thinking结束
                isInThinking = false
                actualContentStarted = true
              }
            } else {
              // 智能检测：如果thinking已经很长且包含大量非thinking内容，强制结束thinking
              if (thinkingDetails.length > 200 && !data.includes('summary') && !data.includes('Thinking')) {
                // 强制结束thinking，将当前内容作为实际内容
                isInThinking = false
                actualContentStarted = true
                responseText += data
                console.log('强制结束thinking状态，当前内容作为实际回答')
              } else {
                // 继续累加到thinking内容
                thinkingDetails += data
                responseText = thinkingDetails
              }
            }
          }
          // thinking已结束，添加实际内容
          else {
            responseText += data
            actualContentStarted = true
          }

          onMessageFlow(responseText, messageId, conversationId)
          return
        }
        
        console.log('事件类型:', parsedData.event)
        
        // 处理工作流节点事件
        if (parsedData.event === 'message_flow_event') {
          console.log('进入工作流事件处理分支')
          try {
            const workflowData = JSON.parse(parsedData.answer)
            
            // 保留完整的工作流数据，包括parameters和result字段
            const completeWorkflowData = {
              ...workflowData,
              // 确保parameters和result字段被正确传递
              parameters: workflowData.parameters || null,
              result: workflowData.result || null,
              // 添加调试信息
              debugInfo: {
                originalAnswer: parsedData.answer,
                messageId: parsedData.message_id,
                conversationId: parsedData.conversation_id,
                timestamp: parsedData.created_at,
                processedAt: new Date().toISOString()
              }
            }
            
            onWorkflowEvent(completeWorkflowData)
          } catch (error) {
            console.error('解析工作流事件失败:', error, parsedData.answer)
          }
          return
        }
        
        // 处理消息流
        if (parsedData.event === 'message_flow') {
          const messageContent = parsedData.answer || ''
          console.log('消息流:', messageContent)
          
          // 检查是否是thinking开始
          if (messageContent.includes('<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open>')) {
            isInThinking = true
            thinkingDetails = messageContent
            responseText = thinkingDetails
            actualContentStarted = false
          }
          // 如果正在thinking中
          else if (isInThinking) {
            // 检查是否包含结束标签
            if (messageContent.includes('</details>')) {
              // 分割结束标签前后的内容
              const parts = messageContent.split('</details>')
              thinkingDetails += parts[0] + '</details>'
              responseText = thinkingDetails

              // 如果有实际内容，添加到thinking后面
              if (parts[1]) {
                responseText += parts[1]
                isInThinking = false
                actualContentStarted = true
              } else {
                // 没有实际内容，thinking结束
                isInThinking = false
                actualContentStarted = true
              }
            } else {
              // 智能检测：如果thinking已经很长且包含大量非thinking内容，强制结束thinking
              if (thinkingDetails.length > 200 && !messageContent.includes('summary') && !messageContent.includes('Thinking')) {
                // 强制结束thinking，将当前内容作为实际内容
                isInThinking = false
                actualContentStarted = true
                responseText += messageContent
                console.log('强制结束thinking状态，当前内容作为实际回答')
              } else {
                // 继续累加到thinking内容
                thinkingDetails += messageContent
                responseText = thinkingDetails
              }
            }
          }
          // thinking已结束，添加实际内容
          else {
            responseText += messageContent
            actualContentStarted = true
          }

          onMessageFlow(responseText, parsedData.message_id, parsedData.conversation_id)
          return
        }
        
        // 处理最终消息
        if (parsedData.event === 'message') {
          const messageContent = parsedData.answer || ''
          console.log('最终消息:', messageContent)
          
          // 检查是否是thinking开始
          if (messageContent.includes('<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open>')) {
            isInThinking = true
            thinkingDetails = messageContent
            responseText = thinkingDetails
            actualContentStarted = false
          }
          // 如果正在thinking中
          else if (isInThinking) {
            // 检查是否包含结束标签
            if (messageContent.includes('</details>')) {
              // 分割结束标签前后的内容
              const parts = messageContent.split('</details>')
              thinkingDetails += parts[0] + '</details>'
              responseText = thinkingDetails

              // 如果有实际内容，添加到thinking后面
              if (parts[1]) {
                responseText += parts[1]
                isInThinking = false
                actualContentStarted = true
              } else {
                // 没有实际内容，thinking结束
                isInThinking = false
                actualContentStarted = true
              }
            } else {
              // 智能检测：如果thinking已经很长且包含大量非thinking内容，强制结束thinking
              if (thinkingDetails.length > 200 && !messageContent.includes('summary') && !messageContent.includes('Thinking')) {
                // 强制结束thinking，将当前内容作为实际内容
                isInThinking = false
                actualContentStarted = true
                responseText += messageContent
                console.log('强制结束thinking状态，当前内容作为实际回答')
              } else {
                // 继续累加到thinking内容
                thinkingDetails += messageContent
                responseText = thinkingDetails
              }
            }
          }
          // thinking已结束，添加实际内容
          else {
            responseText += messageContent
            actualContentStarted = true
          }

          onMessageFlow(responseText, parsedData.message_id, parsedData.conversation_id)
          return
        }
        
        // 处理消息结束
        if (parsedData.event === 'message_end') {
          console.log('消息结束:', parsedData)
          onCompleted(responseText, parsedData.metadata)
          return
        }
        
        // 兜底处理，如果没有匹配到任何事件类型
        console.log('未知事件类型:', parsedData)
      },
      onCompleted(metadata) {
        onCompleted(responseText, metadata)
      },
      onError(error) {
        onError(error)
      },
    }
  )
}

// 流式运行工作流接口
export const handleWorkflowStreamRun = async (workflowId, variables, options = {}) => {
  const {
    onWorkflowEvent = () => {},
    onMessageFlow = () => {},
    onCompleted = () => {},
    onError = () => {},
  } = options

  let responseText = ''
  let isInThinking = false
  let thinkingDetails = ''

  return ssePost(
    '/api/v1/workflow/tryRunningStream',
    {
      body: {
        workflowId,
        variables
      },
      headers: {
        'Content-Type': 'application/json',
        'satoken': localStorage.getItem('token'),
      },
    },
    {
      onData: (data, isFirstMessage, { messageId, conversationId }) => {
        console.log('接收到工作流数据:', data)
        
        // 尝试解析为JSON，如果失败则作为纯文本处理
        let parsedData
        try {
          parsedData = JSON.parse(data)
          console.log('解析为JSON成功:', parsedData)
          
          // 检查解析结果是否为基本类型（数字、字符串等），如果是则作为消息内容处理
          if (typeof parsedData !== 'object' || parsedData === null) {
            console.log('解析结果是基本类型，作为消息内容处理:', parsedData)
            
            // 将基本类型转换为字符串并添加到响应文本中
            const dataStr = String(parsedData)
            responseText += dataStr
            onMessageFlow(responseText, messageId, conversationId)
            return
          }
        } catch (error) {
          // 如果不是JSON，则作为纯文本消息处理
          console.log('数据不是JSON格式，作为消息内容处理:', data)
          
          // 检查是否是thinking开始
          if (data.includes('<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open>')) {
            isInThinking = true
            thinkingDetails = data
            responseText = thinkingDetails
          }
          // 如果正在thinking中
          else if (isInThinking) {
            // 检查是否包含结束标签
            if (data.includes('</details>')) {
              // 分割结束标签前后的内容
              const parts = data.split('</details>')
              thinkingDetails += parts[0] + '</details>'
              responseText = thinkingDetails

              // 如果有实际内容，添加到thinking后面
              if (parts[1]) {
                responseText += parts[1]
                isInThinking = false
              } else {
                // 没有实际内容，thinking结束
                isInThinking = false
              }
            } else {
              // 继续累加到thinking内容
              thinkingDetails += data
              responseText = thinkingDetails
            }
          }
          // thinking已结束，添加实际内容
          else {
            responseText += data
          }

          onMessageFlow(responseText, messageId, conversationId)
          return
        }
        
        console.log('工作流事件类型:', parsedData.event)
        
        // 特殊处理：如果解析后的数据直接包含 output 和 url 字段，说明这是工作流的最终输出
        // if (!parsedData.event && parsedData.output) {
        //   console.log('检测到工作流最终输出数据，直接处理为消息内容')
        //   let finalContent = parsedData.output
          
        //   // 如果有下载链接，添加到内容末尾
        //   // if (parsedData.url) {
        //   //   console.log('添加文件下载链接:', parsedData.url)
        //   //   finalContent += `\n\n<div style="margin: 20px 0; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; ">
        //   //     <div style="display: flex; align-items: center; gap: 12px;">
        //   //       <div style="background: rgba(255,255,255,0.2); border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; font-size: 18px;">📁</div>
        //   //       <div style="flex: 1;">
        //   //         <div style="color: white; font-weight: bold; font-size: 16px; margin-bottom: 4px;">文件已生成完成</div>
        //   //         <a href="${parsedData.url}" target="_blank" style="display: inline-flex; align-items: center; gap: 8px; background: rgba(255,255,255,0.9); color: #4f46e5; padding: 8px 16px; border-radius: 8px; text-decoration: none; font-weight: 500; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.background='white'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.15)'" onmouseout="this.style.background='rgba(255,255,255,0.9)'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'">
        //   //           <span>💾</span>
        //   //           <span>立即下载文件</span>
        //   //           <span>→</span>
        //   //         </a>
        //   //       </div>
        //   //     </div>
        //   //   </div>`
        //   // }
          
        //   responseText += finalContent
        //   onMessageFlow(responseText, messageId, conversationId)
        //   return
        // }
        
        // 处理工作流节点事件
        if (parsedData.event === 'message_flow_event') {
          console.log('进入工作流事件处理分支')
          try {
            const workflowData = JSON.parse(parsedData.answer)

            // 使用新的数据解析器处理工作流事件
            const parsedWorkflowData = parseWorkflowEvent(workflowId, workflowData)

            // 保留完整的工作流数据，包括parameters和result字段
            const completeWorkflowData = {
              ...parsedWorkflowData,
              // 确保parameters和result字段被正确传递
              parameters: workflowData.parameters || null,
              result: workflowData.result || null,
              // 添加调试信息
              debugInfo: {
                originalAnswer: parsedData.answer,
                messageId: parsedData.message_id,
                conversationId: parsedData.conversation_id,
                timestamp: parsedData.created_at,
                processedAt: new Date().toISOString(),
                workflowId: workflowId
              }
            }

            console.log('解析后的工作流数据:', completeWorkflowData)
            onWorkflowEvent(completeWorkflowData)
          } catch (error) {
            console.error('解析工作流事件失败:', error, parsedData.answer)
          }
          return
        }
        
        // 处理消息流
        if (parsedData.event === 'message_flow') {
          const messageContent = parsedData.answer || ''
          console.log('消息流:', messageContent)

          // 使用新的数据解析器处理消息流
          const parsedMessageData = parseMessageFlow(workflowId, messageContent)

          // 检查是否是thinking开始
          if (parsedMessageData.hasThinking || messageContent.includes('<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open>')) {
            isInThinking = true
            thinkingDetails = messageContent
            responseText = thinkingDetails
          }
          // 如果正在thinking中
          else if (isInThinking) {
            // 检查是否包含结束标签
            if (messageContent.includes('</details>')) {
              // 分割结束标签前后的内容
              const parts = messageContent.split('</details>')
              thinkingDetails += parts[0] + '</details>'
              responseText = thinkingDetails

              // 如果有实际内容，添加到thinking后面
              if (parts[1]) {
                responseText += parts[1]
                isInThinking = false
              } else {
                // 没有实际内容，thinking结束
                isInThinking = false
              }
            } else {
              // 继续累加到thinking内容
              thinkingDetails += messageContent
              responseText = thinkingDetails
            }
          }
          // thinking已结束，添加实际内容
          else {
            responseText += messageContent
          }

          console.log('处理后的消息流:', { responseText, workflowId })
          onMessageFlow(responseText, parsedData.message_id, parsedData.conversation_id)
          return
        }
        
        // 处理最终消息
        if (parsedData.event === 'message') {
          const messageContent = parsedData.answer || ''
          console.log('最终消息:', messageContent)
          
          // 使用新的数据解析器处理最终消息
          const parsedFinalData = parseFinalMessage(workflowId, messageContent)
          let actualContent = parsedFinalData.content

          // 如果有下载链接，添加下载按钮
          if (parsedFinalData.downloadUrl) {
            console.log('工作流生成文件下载链接:', parsedFinalData.downloadUrl)
            actualContent += `\n\n<div style="margin: 20px 0; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; ">
              <div style="display: flex; align-items: center; gap: 12px;">
                <div style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                  <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                  </svg>
                </div>
                <div style="flex: 1;">
                  <div style="color: white; font-weight: 600; margin-bottom: 4px;">文档已生成</div>
                  <div style="color: rgba(255,255,255,0.8); font-size: 14px;">点击下载生成的${parsedFinalData.documentType || '文档'}</div>
                </div>
                <a href="${parsedFinalData.downloadUrl}" download style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-weight: 500; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                  下载
                </a>
              </div>
            </div>`
          }

          console.log('处理后的最终消息:', { actualContent, workflowId, parsedFinalData })

          // 对于最终消息（event: message），直接设置完整内容
          responseText = actualContent

          console.log('🎯 最终消息处理完成:', {
            originalAnswer: messageContent,
            parsedContent: actualContent,
            responseText: responseText,
            messageId: parsedData.message_id,
            conversationId: parsedData.conversation_id,
            workflowId: workflowId
          })

          onMessageFlow(responseText, parsedData.message_id, parsedData.conversation_id)
          return
        }
        
        // 处理消息结束
        if (parsedData.event === 'message_end') {
          console.log('消息结束:', parsedData)
          onCompleted(responseText, parsedData.metadata)
          return
        }
        
        // 处理最终消息内容（当数据只包含result字段时）
        if (parsedData.result && !parsedData.event) {
          console.log('🎯 处理最终消息内容:', parsedData.result)

          // 使用新的数据解析器处理最终消息
          const parsedFinalData = parseFinalMessage(workflowId, JSON.stringify(parsedData))
          let actualContent = parsedFinalData.content

          // 对于最终消息，直接设置完整内容
          responseText = actualContent

          console.log('🎯 最终消息处理完成:', {
            originalData: parsedData,
            parsedContent: actualContent,
            responseText: responseText,
            workflowId: workflowId
          })

          onMessageFlow(responseText, null, null)
          return
        }

        // 兜底处理，如果没有匹配到任何事件类型
        console.log('未知事件类型:', parsedData)
      },
      onCompleted(metadata) {
        onCompleted(responseText, metadata)
      },
      onError(error) {
        onError(error)
      },
    }
  )
}

//发送对话消息请求参数
/**
 * AgentChatReq
 */
// export interface Request {
//   /**
//    * 智能体ID
//    */
//   agentId: string;
//   /**
//    * 会话 ID，需要基于之前的聊天记录继续对话，必须传之前消息的 conversationId
//    */
//   conversationId?: string;
//   /**
//    * 深度思考
//    */
//   deepResearch?: boolean;
//   /**
//    * 上传文件列表
//    */
//   files?: AgentChatFileReq[];
//   /**
//    * 允许传入智能体定义的各变量值
//    */
//   inputs?: { [key: string]: { [key: string]: any } };
//   /**
//    * 联网搜索
//    */
//   /**
//    * 知识库ID列表
//    */
//   knowledgeIds?: string[];
//   /**
//    * 文档ID列表
//    */
//   documentIds?: string[];
//   onlineSearch?: boolean;
//   /**
//    * 用户输入/提问内容
//    */
//   query: string;
//   [property: string]: any;
// }

// /**
// * AgentChatFileReq，文件列表请求
// */
// export interface AgentChatFileReq {
//   /**
//    * 上传文件 ID，传输方式为 local_file 可用
//    */
//   fileId?: string;
//   /**
//    * 文件类型，支持类型有 document, image, audio, video, custom
//    */
//   fileType: string;
//   /**
//    * 传递方式：local_file 或者 remote_url，默认为 local_file
//    */
//   transferMethod?: string;
//   /**
//    * 地址：传递方式为 remote_url 可用
//    */
//   url?: string;
//   [property: string]: any;
// }
// 发送对话消息返回数据结构
// {
//   "event": "message",
//   data:{
//     "event":"message_flow_event",
//     "answer":"{\"nodeName\":\"开始节点\",\"workflowTitle\":\"项目可行性研究报告\",\"nodeType\":\"node_AghrfedG73rM2lTp\",\"nodeStatus\":\"start\",\"workflowId\":\"1939868414754336769\"}",
//     "message_id":"1942113823446753283",
//     "conversation_id":"1942113823446753282",
//     "created_at":"1751870993"}
// }