import { get, post,Delete } from './index'

// 登录接口
export function login(params){
  return post('/v1/auth/login', params)
}
// 退出登录
export function logout(params){
  return post('/v1/auth/logout', params)
}
// 登录用户信息
export function loginUserInfo(params){
  return post('/v1/auth/userInfo', params)
}

// 匿名登录
export function loginPassport(params){
  return post('/v1/auth/passport', params)
}

// 查询用户详细 包含角色菜单
export function getUserPermit() {
  return get('/system/user/getInfo')
}