import axios from 'axios'
import { ElMessage } from 'element-plus'
import { loginPassport } from '@/api/auth' // 根据实际路径调整

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 600000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
      // 添加 satoken header
      config.headers['satoken'] = token
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  async response => {
    const res = response.data
    
    if (res.code === 401) {
      // 触发退出登录事件，清除管理端按钮状态
      window.dispatchEvent(new CustomEvent('logout-success'))
      
      const urlParams = new URLSearchParams(window.location.search)
      const loginFree = urlParams.get('loginfree')
      console.log(loginFree)
      if (loginFree === '1') {
        // try {
        //   await loginPassport()
        //   return request(config)
        // } catch (loginError) {
        //   console.error('Login passport failed:', loginError)
        // }
      } else{
        window.dispatchEvent(new CustomEvent('show-login-dialog'))
        return Promise.reject(new Error(res.message || '未登录'))
      }
    }
    
    return res
  },
  error => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 触发退出登录事件，清除管理端按钮状态
          window.dispatchEvent(new CustomEvent('logout-success'))
          // HTTP 状态码 401 的情况也显示登录弹窗
          window.dispatchEvent(new CustomEvent('show-login-dialog'))
          break
        default:
          ElMessage.error(error.response.data?.message || '请求失败')
      }
    }
    return Promise.reject(error)
  }
)

// 通用的 GET 请求方法
export const get = (url, params) => {
  return service.get(url, { params })
}

// 通用的 POST 请求方法
export const post = (url, data) => {
  return service.post(url, data)
}

// delete 
export const Delete = (url, data) => {
  return service.delete(url, { data })
}

// 导出 service 实例以供直接使用
export { service }

// 其他通用方法可以在这里添加