import { get, post, service } from './index'


// 知识库系统分页查询  
export const getKnowledgeListLocal = (params) => get('/v1/knowledge/base/pageLocal', params)

// 获取知识库详情
export const getKnowledgeDetail = (params) => get('/v1/knowledge/base/detail', params)

// 新建知识库
export const addKnowledge = (params) => post('/v1/knowledge/base/add', params)

// 修改知识库
export const editKnowledge = (params) => post('/v1/knowledge/base/edit', params)

// 删除知识库
export const deleteKnowledge = (params) => post('/v1/knowledge/base/remove', params)

//知识库模型分页查询
export const getKnowledgeModelList = (params) => get('/v1/knowledge/base/page', params)


// 下载知识库
export const downloadKnowledge = (params) => {
  return service({
    url: '/v1/knowledge/file/download',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 更新知识库文件
export const updateKnowledgeFile = (params) => post('/v1/knowledge/file/edit', params)

// 删除知识库文件
export const deleteKnowledgeFile = (params) => post('/v1/knowledge/file/remove', params)

// 分页查询知识库文件
export const getKnowledgeFileList = (params) => get('/v1/knowledge/file/page', params)

// 本地知识库分页查询
export const getKnowledgeFileListLocal = (params) => get('/v1/knowledge/file/pageLocal', params)

// 批量上传知识库文件
export const uploadKnowledgeFile = (params) => post('/v1/knowledge/file/uploadMultiple', params)

// 单个上传知识库文件
export const uploadKnowledgeFileSingle = (params) => post('/v1/knowledge/file/uploadSingle', params)

// 新建文件分块
export const createFileChunk = (params) => post('/v1/knowledge/chunk/add', params)
// 修改文件分块
export const editFileChunk = (params) => post('/v1/knowledge/chunk/edit', params)
// 文件分块信息分页查询
export const getFileChunkList = (params) => get('/v1/knowledge/chunk/page', params)
// 删除文件分块
export const deleteFileChunk = (params) => post('/v1/knowledge/chunk/remove', params)
// 文件分块检索
export const retrieveFileChunk = (params) => post('/v1/knowledge/chunk/retrieve', params)
// 文件分块解析
export const parseFileChunk = (params) => post('/v1/knowledge/file/chunk', params)
// 文件分块预览
export const previewFileChunk = (params) => post('/v1/knowledge/file/previewChunk', params)
// 文件完整分块
export const completeFileChunk = (params) => post('/v1/knowledge/file/completeChunk', params)
// 模型文件解析
export const parseFile = (params) => post('/v1/knowledge/file/parse', params)
// 个人知识库查询(或创建)
export const personKnowledge = (params) => post('/v1/knowledge/base/person', params)

