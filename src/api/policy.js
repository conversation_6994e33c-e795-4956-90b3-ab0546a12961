import { get,post } from './index'

// 获取政策列表
export function getPolicyList(params) {
  return get('/gyq-api/public/policy/v1/page', params)
}

// 获取政策详情
export function getPolicyDetail(params) {
  return get('/gyq-api/public/policy/v1/detail', params)
}

// 公示公告
export function getNoticeList(params) {
  return get('/gyq-api/public/notice/v1/page', params)
}

// 公示公告详情
export function getNoticeDetail(params) {
  return get('/gyq-api/public/notice/v1/detail', params)
}

// 新闻动态列表 
export function getNewsList(params) {
  return get('/gyq-api/public/news/v1/page', params)
}

// 新闻动态详情
export function getNewsDetail(params) {
  return get('/gyq-api/public/news/v1/detail', params)
}
// 申报列表
export function getApplyList(params) {
  return get('/gyq-api/public/declaration/v1/page', params)
}
// 申报详情
export function getApplyDetail(params) {
  return get('/gyq-api/public/declaration/v1/detail', params)
}

// 获取企业信息
export function getCompanyDetail(params) {
  return get('/gyq-api/private/company/v1/detail', params)
}
// 提诉求 /private/corporate/v1/addCorporate
export function addCorporate(params){
  return post('/gyq-api/private/corporate/v1/addCorporate', params)
}
// 获取惠企活动列表
export function getActivityList(params){
  return get('/gyq-api/public/activity/v1/page', params)
}
// 获取政策解读列表
export function getPolicyCourseList(params){
  return get('/gyq-api/public/course/v1/page', params)
}
//政策计算器列表
export function getPolicyCalculatorList(params){
  return get('/gyq-api/private/policyCal/v1/list', params)
}
//政策计算器详情
export function getPolicyCalculatorDetail(id){
  return get(`/gyq-api/private/policyCal/v1/${id}`)
}


