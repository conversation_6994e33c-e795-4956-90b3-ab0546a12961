import request from '@/utils/request'

// 新增意见反馈
export function addFeedback(data) {
  return request({
    url: '/system/feedback/add',
    method: 'post',
    data
  })
}

// 修改意见反馈
export function editFeedback(data) {
  return request({
    url: '/system/feedback/edit',
    method: 'put',
    data
  })
}

// 获取意见反馈列表
export function getFeedbackList(params) {
  return request({
    url: '/system/feedback/list',
    method: 'get',
    params
  })
}

// 获取意见反馈详情
export function getFeedbackDetail(feedbackId) {
  return request({
    url: `/system/feedback/${feedbackId}`,
    method: 'get'
  })
}

// 删除意见反馈
export function deleteFeedback(feedbackIds) {
  return request({
    url: `/system/feedback/${feedbackIds}`,
    method: 'delete'
  })
}

// 新增修改处理意见
export function addFeedbackOpinion(data) {
  return request({
    url: '/system/feedback/opinion',
    method: 'post',
    data
  })
}

// ==================== 用户端接口 ====================

// 用户端 - 新增意见反馈
export function addUserFeedback(data) {
  return request({
    url: '/api/v1/public/feedback/add',
    method: 'post',
    data
  })
}

// 用户端 - 获取用户反馈列表
export function getUserFeedbackList(params) {
  return request({
    url: '/api/v1/public/feedback/list',
    method: 'get',
    params
  })
}

// 用户端 - 获取反馈详情
export function getUserFeedbackDetail(feedbackId) {
  return request({
    url: `/api/v1/public/feedback/${feedbackId}`,
    method: 'get'
  })
}

// 用户端 - 提交满意度评价
export function submitFeedbackSatisfaction(feedbackId, data) {
  return request({
    url: `/api/v1/public/feedback/${feedbackId}/satisfaction`,
    method: 'post',
    data
  })
}

// 上传文件 (通用文件上传接口)
export function uploadFile(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/api/v1/public/file/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
} 

//获取文件
export function getFile(params) {
  return request({
    url: '/api/v1/public/file/get',
    method: 'get',
    params
  })
}