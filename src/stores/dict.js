import { defineStore } from 'pinia'
import { getDictionary } from '../api/dict'

export const useDictStore = defineStore('dict', {
  state: () => ({
    dictData: {}, // 存储所有字典数据
    loading: false
  }),

  getters: {
    // 根据字典类型获取对应的字典数据
    getDictByType: (state) => (type) => {
      const dict = state.dictData[type]
      if (!dict) return []
      
      return [
        ...(dict.dictData || []).map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }))
      ]
    }
  },

  actions: {
    // 加载所有字典数据
    async loadAllDict() {
      if (Object.keys(this.dictData).length > 0) {
        return this.dictData
      }

      this.loading = true
      try {
        const { data } = await getDictionary()
        // 将数组转换为以 dictType 为 key 的对象
        this.dictData = data.reduce((acc, dict) => {
          acc[dict.dictType] = dict
          return acc
        }, {})
        return this.dictData
      } catch (error) {
        console.error('加载字典数据失败:', error)
        return {}
      } finally {
        this.loading = false
      }
    },

    // 清除所有字典缓存
    clearAllDict() {
      this.dictData = {}
    }
  }
})
