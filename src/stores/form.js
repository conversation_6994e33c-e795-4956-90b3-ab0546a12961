import { defineStore } from 'pinia'

export const useFormStore = defineStore('form', {
  state: () => ({
    // 存储不同agent的表单配置和值
    agentForms: {}
  }),
  
  getters: {
    // 检查是否存在指定agent的表单配置
    hasAgentForm: (state) => (agentId) => {
      return state.agentForms.hasOwnProperty(agentId) && state.agentForms[agentId].formValues
    },
    
    // 获取指定agent的表单值
    getAgentFormValues: (state) => (agentId) => {
      const agentData = state.agentForms[agentId]
      if (agentData && agentData.formValues) {
        return agentData.formValues
      }
      // 兼容旧格式
      return agentData || {}
    },
    
    // 获取指定agent的表单配置
    getAgentFormConfig: (state) => (agentId) => {
      const agentData = state.agentForms[agentId]
      if (agentData && agentData.formConfig) {
        return agentData.formConfig
      }
      return []
    }
  },
  
  actions: {
    // 保存agent的表单值
    saveAgentFormValues(agentId, formValues) {
      this.agentForms[agentId] = { ...formValues }
    },
    
    // 保存agent的表单配置和值
    saveAgentForm(agentId, formValues, formConfig) {
      this.agentForms[agentId] = { 
        formValues: { ...formValues },
        formConfig: formConfig ? [...formConfig] : []
      }
    },
    
    // 删除agent的表单配置
    removeAgentForm(agentId) {
      delete this.agentForms[agentId]
    },
    
    // 清空所有表单配置
    clearAllForms() {
      this.agentForms = {}
    }
  }
}) 