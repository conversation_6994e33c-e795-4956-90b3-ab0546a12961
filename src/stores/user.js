import { defineStore } from 'pinia'
import { ref } from 'vue'
import { loginUserInfo } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  const userInfo = ref(JSON.parse(sessionStorage.getItem('userInfo')) || null)
  const loading = ref(false)

  // 获取用户信息
  const fetchUserInfo = async () => {
    if (userInfo.value) {
      return
    }
    try {
      loading.value = true
      const res = await loginUserInfo()
      if (res.code === 200) {
        userInfo.value = res.data
        sessionStorage.setItem('userInfo', JSON.stringify(res.data))
        
        // 触发用户信息更新事件，通知管理端按钮刷新权限
        window.dispatchEvent(new CustomEvent('user-info-updated'))
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 设置用户信息
  const setUserInfo = (info) => {
    userInfo.value = info
    if (info) {
      sessionStorage.setItem('userInfo', JSON.stringify(info))
    } else {
      sessionStorage.removeItem('userInfo')
    }
  }

  // 清除用户信息
  const clearUserInfo = () => {
    userInfo.value = null
    sessionStorage.removeItem('userInfo')
    
    // 触发用户信息清除事件，通知管理端按钮隐藏
    window.dispatchEvent(new CustomEvent('logout-success'))
  }

  // 登出
  const logout = () => {
    clearUserInfo()
  }

  return {
    userInfo,
    loading,
    fetchUserInfo,
    setUserInfo,
    clearUserInfo,
    logout
  }
})
