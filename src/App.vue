<template>
  <div class="flex min-h-screen">
    <!-- 通过路由判断是否显示侧边栏 -->
    <MenuSidebar v-if="!isMobileRoute" />
    <main :class="[isMobileRoute ? '' : 'web-main']">
      <router-view />
    </main>
    <!-- 全局管理端切换按钮 -->
    <AdminSwitchButton />
    <LoginDialog v-model:visible="showLoginDialog" @login-success="handleLoginSuccess" />
  </div>
</template>

<script setup>
import MenuSidebar from './components/menu/MenuSidebar.vue'
import LoginDialog from './components/LoginDialog.vue'
import AdminSwitchButton from './components/AdminSwitchButton.vue'
import { onMounted, computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { setToken } from './utils/auth'

const route = useRoute()
const showLoginDialog = ref(false)

// 判断当前是否是移动端路由
const isMobileRoute = computed(() => {
  return route.path.startsWith('/mobile')
})

// 处理登录成功
const handleLoginSuccess = (res) => {
  // 保存token
  if (res.data && res.data.tokenValue) {
    // 使用统一的token管理方法
    setToken(res.data.tokenValue)
  }
  
  // 触发登录成功事件，通知管理端按钮刷新权限
  window.dispatchEvent(new CustomEvent('login-success'))
}

onMounted(async () => {
  // 监听全局登录事件
  window.addEventListener('show-login-dialog', () => {
    showLoginDialog.value = true
  })
  
})
</script>

<style>
@import 'element-plus/dist/index.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 移动端路由下移除默认的 padding 和 margin */
.mobile-route {
  padding: 0;
  margin: 0;
  width: 100%;
}

.web-main{
  flex-grow: 1;
  /* margin-left: 7rem;
  width: calc(100% - 7rem); */
}
</style>
