<template>
  <div class="flex flex-col items-center w-full">
    <!-- 主聊天区域 -->
    <div class="main-container">
      <header class="p-4 text-center">
        <div class="inline-flex gap-2 items-center text-xl font-bold text-txt-primary">
          <span>
            {{
              activeConversationIndex !== null && historyConversations[activeConversationIndex]
              ? historyConversations[activeConversationIndex].conversationName || `历史对话${activeConversationIndex}`
              : `历史对话${activeConversationIndex}`
            }}
          </span>
        </div>
      </header>

      <!-- 左侧历史对话列表 -->
      <div class="left-container">
        <div class="mb-6 text-xl font-bold text-txt-dark">
          历史对话
        </div>
        <div class="mb-6 text-base text-txt-middle">
          查看对话内容
        </div>
        <div class="history-container">
          <div class="mb-4 text-base text-gray-500">请选择历史对话</div>
          <ul>
            <li v-for="(group, groupName) in groupedConversations" :key="groupName">
              <div class="font-bold">{{ groupName }}</div>
              <ul>
                <li
                  v-for="conversation in group"
                  :key="conversation.conversationId"
                  class="flex justify-between items-center p-2 rounded-md btn-transparent"
                  :class="{ 'menu-active': activeConversationId === conversation.conversationId }"
                >
                  <!-- 对话名称，点击加载对话 -->
                  <el-tooltip
                    :content="conversation?.conversationName || `历史对话${conversation.index}`"
                    placement="top"
                    :show-after="500"
                    :hide-after="0"
                  >
                    <span 
                      @click="loadConversation(conversation.conversationId, conversation.index)"
                      class="overflow-hidden flex-1 truncate cursor-pointer"
                    >
                      {{ conversation?.conversationName || `历史对话${conversation.index}` }}
                    </span>
                  </el-tooltip>
                  
                  <!-- 操作按钮 -->
                  <div class="flex gap-2 opacity-0 group-hover:opacity-100">
                    <!-- 重命名按钮 -->
                    <button
                      @click.stop="handleRename(conversation)"
                      class="p-1 text-gray-400 hover:text-primary"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
                    </button>
                    <!-- 删除按钮 -->
                    <button
                      @click.stop="handleDelete(conversation)"
                      class="p-1 text-gray-400 hover:text-red-500"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>
                    </button>
                  </div>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>

      
      <div class="flex justify-center w-full">

        
        <!-- 聊天区域 -->
        <div class="chat-wrapper">
          <div class="messages-container" ref="messagesContainer">
            <!-- 消息内容 -->
            <div v-for="(message, index) in messages"
                :key="index"
                class="flex mb-4"
                :class="message.sender === 'ai' ? 'justify-start' : 'justify-end'"
            >
              <!-- 修改 AI 消息部分 -->
              <div v-if="message.sender === 'ai'" class="flex items-start gap-2 max-w-[50%] group relative">
                <div class="overflow-hidden flex-shrink-0 w-16 h-16 rounded-full">
                  <img src="@/assets/image/kebao.png" alt="AI头像" class="object-cover w-full h-full" />
                </div>
                <div class="p-4 bg-white rounded-lg shadow-md">
                  <!-- 添加 loading 状态显示 -->
                  <div v-if="message.loading" class="flex gap-2 items-center">
                    <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
                  </div>
                  <div v-else class="text-gray-800" v-html="message.content"></div>
                </div>

                <!-- 操作按钮组 - 移到对话框外部左下角 -->
                <div class="flex absolute -bottom-8 left-16 gap-2 items-center p-1 px-2 bg-white rounded-full border border-gray-100 shadow-sm opacity-0 transition-all duration-200 group-hover:opacity-100">
                  <!-- 复制按钮 -->
                  <button
                    @click="copyMessage(message.content)"
                    class="p-1 text-gray-400 rounded-full transition-colors duration-200 hover:text-primary hover:bg-gray-50"
                    title="复制消息"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
                  </button>

                  <!-- 重新生成按钮 -->
                  <button 
                    @click="regenerateResponse(index)"
                    class="p-1 text-gray-400 rounded-full transition-colors duration-200 hover:text-primary hover:bg-gray-50"
                    title="重新生成"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
                  </button>

                  <!-- 点赞按钮 -->
                  <button
                    @click="handleLike(message, true)"
                    :class="[
                      'p-1 transition-colors duration-200 rounded-full hover:bg-gray-50',
                      message.liked ? 'text-primary' : 'text-gray-400 hover:text-primary'
                    ]"
                    title="点赞"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M7 10v12"/><path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"/></svg>
                  </button>

                  <!-- 点踩按钮 -->
                  <button
                    @click="handleLike(message, false)"
                    :class="[
                      'p-1 transition-colors duration-200 rounded-full hover:bg-gray-50',
                      message.disliked ? 'text-red-500' : 'text-gray-400 hover:text-red-500'
                    ]"
                    title="点踩"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 14V2"/><path d="M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z"/></svg>
                  </button>
                </div>
              </div>
              <div v-else class="flex items-start gap-2 max-w-[80%] group relative">
                <div class="p-4 rounded-lg shadow-md bg-primary">
                  <p class="text-white">{{ message.content }}</p>
                </div>
                <!-- 添加用户消息复制按钮 -->
                <div class="flex absolute right-0 -bottom-8 gap-2 items-center p-1 px-2 bg-white rounded-full border border-gray-100 shadow-sm opacity-0 transition-all duration-200 group-hover:opacity-100">
                  <button 
                    @click="copyMessage(message.content)"
                    class="p-1 text-gray-400 rounded-full transition-colors duration-200 hover:text-primary hover:bg-gray-50"
                    title="复制消息"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="rounded-lg input-wrapper">
            <div class="flex relative flex-col p-4 bg-white rounded-lg shadow-md input-container">
              <!-- 添加文件预览区域 -->
              <div v-if="uploadedFiles.length > 0" class="flex flex-wrap gap-2 mb-3 uploaded-files-preview">
                <div
                  v-for="(file, index) in uploadedFiles"
                  :key="file.fileId"
                  class="flex items-center p-2 bg-gray-50 rounded-lg file-preview-item"
                >
                  <!-- 文件图标 -->
                  <span class="mr-2">{{ getFileIcon(file.fileType) }}</span>

                  <!-- 文件名称和预览按钮 -->
                  <span class="text-sm text-gray-600 cursor-pointer hover:text-primary" @click="previewFile(file)">
                    {{ file.fileName }}
                  </span>

                  <!-- 删除按钮 -->
                  <button
                    class="ml-2 text-gray-400 hover:text-red-500"
                    @click="removeFile(index)"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                  </button>
                </div>
              </div>

              <textarea
                :auto-focus="true"
                v-model="userInput"
                placeholder="你可以问我任何问题，或说出我如何帮助你..."
                class="flex-1 p-2 text-gray-700 align-top outline-none resize-none"
                @keyup.enter.prevent="sendMessage()"
                rows="3"
              ></textarea>

              <div class="flex gap-2 items-center">
                <!--<button 
                  @click="toggleMode('deep')" 
                  class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
                  :class="[
                    activeMode === 'deep' 
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  ]"
                >
                  <div class="flex gap-1 items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12z"></path><path d="M12 8v8"></path><path d="M8 12h8"></path></svg>
                    深度思考
                  </div>
                </button>-->
                <button 
                  @click="toggleMode('search')" 
                  class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
                  :class="[
                    activeMode === 'search' 
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  ]"
                >
                  <div class="flex gap-1 items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                    联网搜索
                  </div>
                </button>
                <div class="px-2 text-xs text-gray-400">内容由 AI 生成，请仔细甄别</div>
              </div>

              <div class="flex gap-2 items-center m-4 text-gray-500 func-container">
                <el-upload
                  class="upload-button"
                  :action="`/api/v1/public/chat/uploadFile`"
                  :data="{
                    userId: userStore.userInfo?.userId,
                    agentId: agentId
                  }"
                  :show-file-list="false"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeUpload"
                >
                  <button class="p-2 rounded-full hover:bg-gray-100">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-paperclip"><path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"/></svg>
                  </button>
                </el-upload>
                <!-- <button class="p-2 rounded-full hover:bg-gray-100">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mic"><path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"/><path d="M19 10v2a7 7 0 0 1-14 0v-2"/><line x1="12" x2="12" y1="19" y2="22"/></svg>
                </button> -->
                <button
                  @click="sendMessage()"
                  class="p-2 text-white rounded-full bg-primary hover:bg-primary-middle"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="chat-footer-pic" style="z-index: 1;">
        <img src="@/assets/image/ai.png" alt="AI助手" class="w-30 h-30" />
      </div> -->
    </div>

    <el-image
      v-if="previewVisible"
      :src="previewUrl"
      :preview-src-list="[previewUrl]"
      hide-on-click-modal
      @close="previewVisible = false"
    />

  </div>
</template>

<script>
import { h } from 'vue'
import { marked } from 'marked'
import {
  getConversationList,
  getFeedBack,
  getMessageList,
  handleSendMessage,
  removeConversation,
  renameConversation
} from '@/api/ai'
import { ElMessage, ElMessageBox, ElImageViewer } from 'element-plus'
import { createVNode, render } from 'vue'
import { useUserStore } from '@/stores/user'

export default {
  name: 'ChatInterface',
  components: {
    ElImageViewer
  },
  data() {
    return {
      historyConversations: [],
      messages: [],
      userInput: '',
      aiResponses: [
        '我可以帮你解答这个问题！',
        '这是一个很好的问题，让我来回答...',
        '我理解你的需求，以下是我的建议...',
        '根据我的分析，有几种可能的解决方案...',
        '我需要更多信息来更好地帮助你，能否详细说明？'
      ],
      activeConversationId: null,
      activeConversationIndex: '',
      pageNo: 1,
      pageSize: 100,
      userStore: useUserStore(),
      agentId: '9bad24bea86d3d7b2a1eff108ed7e7ec',
      isLoading: false,
      error: null,
      messagePageNo: 1,
      messagePageSize: 50,
      hasMoreMessages: true,
      uploadedFiles: [],
      previewVisible: false,
      previewUrl: '',
      activeMode: '',
      scrollTimer: null, // 添加滚动定时器
    }
  },
  computed: {
    groupedConversations() {
      const today = new Date();
      const groups = {
        '今天': [],
        '昨天': [],
        '前3天': [],
        '前7天': [],
        '更早': []
      };

      this.historyConversations.forEach(conversation => {
        const updateDate = new Date(conversation.updateTime);
        const diffDays = Math.floor((today - updateDate) / (1000 * 60 * 60 * 24));

        if (diffDays < 1) {
          groups['今天'].push(conversation);
        } else if (diffDays < 2) {
          groups['昨天'].push(conversation);
        } else if (diffDays < 4) { // 2-3天
          groups['前3天'].push(conversation);
        } else if (diffDays < 8) { // 4-7天
          groups['前7天'].push(conversation);
        } else {
          groups['更早'].push(conversation);
        }
      });

      // Filter out empty groups
      return Object.fromEntries(Object.entries(groups).filter(([_, group]) => group.length > 0));
    }
  },
  async mounted() {
    await this.fetchConversationList()
    this.$nextTick(() => {
      this.scrollToBottom();
    })
  },
  methods: {
    async fetchConversationList() {
      this.isLoading = true;
      this.error = null;
      try {
        const params = {
          userId: this.userStore.userInfo?.userId,
          agentId: this.agentId,
          pageNo: this.pageNo,
          pageSize: this.pageSize
        };

        const response = await getConversationList(params);
        if (response) {
          // Add an index to each conversation
          this.historyConversations = (response.rows || []).map((conversation, index) => ({
            ...conversation,
            index: index
          }));
        }
      } catch (error) {
        console.error('Failed to fetch conversation list:', error);
        this.error = '获取对话列表失败，请稍后重试';
      } finally {
        this.isLoading = false;
      }
    },
    async loadConversation(conversationId, index) {
      this.activeConversationId = conversationId;
      this.activeConversationIndex = index;
      this.isLoading = true;
      this.error = null;

      try {
        const conversation = this.historyConversations.find(conv => conv.conversationId === conversationId);
        if (!conversation) {
          console.error('Conversation not found for ID:', conversationId);
          return;
        }
        const params = {
          userId: this.userStore.userInfo?.userId,
          agentId: this.agentId,
          conversationId: conversation.conversationId,
          pageNo: '1',
          pageSize: '50'
        };

        const response = await getMessageList(params);

        if (response && response.rows) {
          // 清空现有消息
          this.messages = [];
          
          // 等待 DOM 更新
          await this.$nextTick();
          
          // 添加新消息
          this.messages = response.rows.flatMap(msg => {
            const messages = [];
            messages.push({
              sender: 'user',
              content: msg.query,
              createTime: msg.createTime
            });

            if (msg.answer && msg.answer.trim() !== '') {
              messages.push({
                sender: 'ai',
                content: this.parseMarkdown(msg.answer.replace(/<details[\s\S]*?<\/details>/g, '').trim()),
                createTime: msg.createTime
              });
            }

            return messages;
          });

          // 排序消息
          this.messages.sort((a, b) =>
            new Date(a.createTime) - new Date(b.createTime)
          );

          // 确保在消息加载完成后滚动到底部
          await this.$nextTick();
          this.scrollToBottom();
          
          // 额外的延迟滚动，以确保所有内容都已渲染
          setTimeout(() => {
            this.scrollToBottom();
          }, 100);
        }
      } catch (error) {
        console.error('Failed to fetch messages:', error);
        this.error = '获取对话消息失败，请稍后重试';
      } finally {
        this.isLoading = false;
      }
    },
    async sendMessage(messageContent = null) {
      if ((!this.userInput.trim() && !messageContent && this.uploadedFiles.length === 0) || this.isLoading) return;

      this.isLoading = true;

      // 先准备消息内容
      let messageText = messageContent || this.userInput.trim();
      if (typeof messageText === 'object') {
        messageText = JSON.stringify(messageText);
      }
      
      // 准备文件信息
      const files = this.uploadedFiles.map(file => ({
        fileId: file.fileId,
        fileType: file.fileType,
        extension: file.extension
      }));
      
      // 如果有文件，添加分割线和文件列表
      if (this.uploadedFiles.length > 0) {
        const filesList = this.uploadedFiles
          .map(file => `${this.getFileIcon(file.fileType)} ${file.fileName}`)
          .join('\n');
        messageText += `\n\n附件:\n${filesList}`;
      }
      
      // 添加用户消息
      this.messages.push({
        sender: 'user',
        content: messageText
      });

      // 添加 AI 消息占位，设置 loading 状态
      this.messages.push({
        sender: 'ai',
        content: '',
        loading: true
      });

      // 保存并清空输入（只有在不是重新生成时才清空输入框）
      if (!messageContent) {
        this.userInput = '';
        this.uploadedFiles = [];
      }

      // Set a timeout to reset isLoading if the response takes too long
      const timeout = setTimeout(() => {
        this.isLoading = false;
        console.error('Request timed out');
        // 更新最后一条 AI 消息为错误提示
        const aiMessageIndex = this.messages.length - 1;
        this.messages[aiMessageIndex].loading = false;
        this.messages[aiMessageIndex].content = '响应超时，请稍后重试。';
      }, 120000); // 2 minutes timeout

      // 使用 handleSendMessage 发送消息
      handleSendMessage(messageText, {
        userId: this.userStore.userInfo?.userId,
        agentId: this.agentId,
        conversationId: this.activeConversationId,
        files: files,
        mode: this.activeMode,
        onData: (text, messageId, conversationId) => {
          clearTimeout(timeout);
          console.log('收到新消息原始内容:', text);

          const aiMessageIndex = this.messages.length - 1;
          this.messages[aiMessageIndex].loading = false;

          // 处理消息前的内容
          console.log('解析前的消息内容:', text);
          
          // 处理后的内容
          const processedContent = this.parseMarkdown(text.replace(/^\n+/, ''));
          console.log('解析后的消息内容:', processedContent);
          
          this.messages[aiMessageIndex].content = processedContent;
          
          if (conversationId) {
            this.activeConversationId = conversationId;
          }

          this.debouncedScroll();
        },
        onCompleted: () => {
          clearTimeout(timeout); // Clear timeout on completion
          this.isLoading = false;
        },
        onError: (error) => {
          clearTimeout(timeout);
          console.error('Chat error:', error);
          // 更新最后一条 AI 消息为错误提示
          const aiMessageIndex = this.messages.length - 1;
          this.messages[aiMessageIndex].loading = false;
          this.messages[aiMessageIndex].content = '抱歉，出现了一些错误，请稍后重试。';
          this.isLoading = false;
        }
      });
    },
    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        const container = this.$refs.messagesContainer;
        const scrollHeight = container.scrollHeight;
        container.scrollTo({
          top: scrollHeight,
          behavior: 'smooth'
        });
      }
    },
    debouncedScroll() {
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }
      
      this.scrollTimer = setTimeout(() => {
        this.scrollToBottom();
      }, 50); // 减少延迟时间到 50ms
    },
    async loadMoreMessages() {
      if (!this.hasMoreMessages || this.isLoading) return;

      try {
        const conversation = this.activeConversationId;
        const params = {
          userId: this.userStore.userInfo?.userId,
          agentId: this.agentId,
          conversationId: conversation.conversationId,
          pageNo: String(this.messagePageNo + 1),
          pageSize: String(this.messagePageSize)
        };

        const response = await getMessageList(params);

        if (response && response.rows) {
          const newMessages = response.rows.flatMap(msg => [
            {
              sender: 'user',
              content: msg.query,
              createTime: msg.createTime
            },
            {
              sender: 'ai',
              content: msg.answer.replace(/<details[\s\S]*?<\/details>/g, '').trim(),
              createTime: msg.createTime
            }
          ]);

          this.messages = [...newMessages, ...this.messages];
          this.messagePageNo++;
          this.hasMoreMessages = response.rows.length === this.messagePageSize;
        }
      } catch (error) {
        console.error('Failed to load more messages:', error);
      }
    },
    // 添加文件相关方法
    getFileType(filename) {
      const extension = filename.split('.').pop().toUpperCase()

      const typeMap = {
        DOCUMENT: ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
        IMAGE: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
        AUDIO: ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
        VIDEO: ['MP4', 'MOV', 'MPEG', 'MPGA']
      }

      for (const [type, extensions] of Object.entries(typeMap)) {
        if (extensions.includes(extension)) {
          return {
            type,
            extension
          }
        }
      }

      return {
        type: 'CUSTOM',
        extension
      }
    },

    getFileIcon(fileType) {
      const icons = {
        'DOCUMENT': '📄',
        'IMAGE': '🖼️',
        'AUDIO': '🎵',
        'VIDEO': '🎥',
        'CUSTOM': '📎'
      }
      return icons[fileType] || '📎'
    },

    previewFile(file) {
      // 图片预览
      if (file.fileType === 'IMAGE') {
        const img = new Image()
        img.src = URL.createObjectURL(file.raw)

        // 创建图片预览实例
        const container = document.createElement('div')
        const vnode = createVNode(ElImageViewer, {
          urlList: [img.src],
          onClose: () => {
            render(null, container)
            document.body.removeChild(container)
          }
        })

        document.body.appendChild(container)
        render(vnode, container)
      }
      // 其他文件类型预览保持不变
      else if (file.fileType === 'DOCUMENT' && ['PDF'].includes(file.extension)) {
        window.open(URL.createObjectURL(file.raw))
      }
      else if (file.fileType === 'DOCUMENT' && ['TXT', 'MD', 'HTML', 'CSV'].includes(file.extension)) {
        const reader = new FileReader()
        reader.onload = (e) => {
          ElMessageBox.alert(e.target.result, '文件预览', {
            customClass: 'text-preview-dialog',
            dangerouslyHtmlString: file.extension === 'HTML'
          })
        }
        reader.readAsText(file.raw)
      }
      else if (file.fileType === 'AUDIO') {
        const audio = new Audio(URL.createObjectURL(file.raw))
        audio.play()
      }
      else if (file.fileType === 'VIDEO') {
        ElMessageBox.alert(
          `<video controls style="max-width: 100%">
            <source src="${URL.createObjectURL(file.raw)}" type="video/${file.extension.toLowerCase()}">
          </video>`,
          '视频预览',
          {
            dangerouslyHtmlString: true,
            customClass: 'video-preview-dialog'
          }
        )
      }
      else {
        ElMessage.info('该文件类型暂不支持预览')
      }
    },

    removeFile(index) {
      this.uploadedFiles.splice(index, 1)
    },

    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 50
      if (!isLt10M) {
        ElMessage.error('文件大小不能超过 50MB!')
        return false
      }
      const formData = new FormData()
      formData.append('file', file)
      formData.append('userId', this.userStore.userInfo?.userId)
      formData.append('agentId', this.agentId)
      return true
    },

    handleUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = this.getFileType(file.name)

        this.uploadedFiles.push({
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name,
          raw: file.raw,
          size: file.size
        })

        ElMessage.success({
          message: '文件上传成功',
          duration: 2000
        })
      } else {
        ElMessage.error('文件上传失败')
      }
    },

    handleUploadError() {
      ElMessage.error('文件上传失败，请重试')
    },
    // 处理重命名对话
    async handleRename(conversation) {
      try {
        const { value: formValues } = await ElMessageBox({
          title: '重命名对话',
          message: h('div', null, [
            h('div', { class: 'flex items-center mt-4' }, [  // 添加 mt-4 使其在输入框下方
              h('span', { class: 'mr-4' }, '是否自动命名'),  // 添加 mr-4 增加右侧间距
              h('div', { class: 'flex items-center gap-4' }, [
                h('label', { class: 'inline-flex items-center' }, [
                  '是',  // 是在前
                  h('input', {
                    type: 'radio',
                    name: 'autoGenerate',
                    value: 'true',
                    class: 'ml-2',
                    onChange: (e) => {
                      const input = document.querySelector('.el-message-box__input input')
                      if (input) {
                        input.value = ''
                        input.disabled = e.target.checked
                      }
                    }
                  })
                ]),
                h('label', { class: 'inline-flex items-center' }, [
                  '否',  // 否在后
                  h('input', {
                    type: 'radio',
                    name: 'autoGenerate',
                    value: 'false',
                    class: 'ml-2',
                    checked: true,
                    onChange: (e) => {
                      const input = document.querySelector('.el-message-box__input input')
                      if (input) {
                        input.value = conversation.conversationName || `历史对话${conversation.index}`
                        input.disabled = false
                      }
                    }
                  })
                ])
              ])
            ])
          ]),
          showInput: true,
          inputValue: conversation.conversationName || `历史对话${conversation.index}`,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (value) => {
            const autoGenerate = document.querySelector('input[name="autoGenerate"]:checked').value === 'true'
            if (!autoGenerate && !value) {
              return '名称不能为空'
            }
            return true
          }
        })

        if (formValues !== undefined) {
          const autoGenerate = document.querySelector('input[name="autoGenerate"]:checked').value === 'true'
          const params = {
            userId: this.userStore.userInfo?.userId,
            agentId: this.agentId,
            conversationId: conversation.conversationId,
            conversationName: autoGenerate ? null : formValues,
            autoGenerate: autoGenerate
          }

          const res = await renameConversation(params)
          const name = res.data.conversationName;
          
          // 更新本地数据
          const index = this.historyConversations.findIndex(
            c => c.conversationId === conversation.conversationId
          )
          if (index > -1) {
            this.historyConversations[index].conversationName = name;
            // 如果重命名的是当前活跃的对话，更新 activeConversationIndex
            if (conversation.conversationId === this.activeConversationId) {
              this.activeConversationIndex = index
            }
          }

          ElMessage.success('重命名成功')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重命名失败:', error)
          ElMessage.error('重命名失败，请重试')
        }
      }
    },

    // 处理删除对话
    async handleDelete(conversation) {
      try {
        // 确认删除
        await ElMessageBox.confirm(
          '确定要删除这个对话吗？此操作不可恢复',
          '删除对话',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        const params = {
          userId: this.userStore.userInfo?.userId,
          agentId: this.agentId,
          conversationId: conversation.conversationId
        }

        await removeConversation(params)

        // 更新本地数据
        const index = this.historyConversations.findIndex(
          c => c.conversationId === conversation.conversationId
        )
        if (index > -1) {
          this.historyConversations.splice(index, 1)
        }

        // 如果删除的是当前激活的对话，清空消息列表
        if (this.activeConversationId === conversation.conversationId) {
          this.messages = []
          this.activeConversationId = null
          this.activeConversationIndex = null
        }

        ElMessage.success('删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          ElMessage.error('删除失败，请重试')
        }
      }
    },
    // 添加 markdown 解析方法
    parseMarkdown(content) {
      if (!content) return ''
      try {
        // 替换图片相对路径为绝对路径
        // 将 ![image](/files/... 替换为 ![image](https://ai.gxgeq.com/files/...
        const processedContent = content.replace(/!\[([^\]]*)\]\(\/files\//g, '![$1](https://ai.gxgeq.com/files/');
        
        return marked(processedContent, {
          breaks: true, // 支持换行
          sanitize: false, // 允许HTML标签
          gfm: true, // 启用 GitHub 风格的 markdown
          pedantic: false, // 不那么严格的解析
        })
      } catch (e) {
        console.error('Markdown parsing error:', e)
        return content
      }
    },
    // 添加复制消息方法
    copyMessage(content) {
      const tempElement = document.createElement('div')
      tempElement.innerHTML = content
      const textContent = tempElement.textContent || tempElement.innerText
      
      navigator.clipboard.writeText(textContent).then(() => {
        ElMessage({
          message: '复制成功',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        ElMessage({
          message: '复制失败，请手动复制',
          type: 'error',
          duration: 2000
        })
      })
    },
    // 添加重新生成响应方法
    async regenerateResponse(index) {
      // 获取当前 AI 消息的上一条用户消息
      let userMessage = null;
      for (let i = index - 1; i >= 0; i--) {
        if (this.messages[i].sender === 'user') {
          userMessage = this.messages[i].content;
          break;
        }
      }
      
      if (!userMessage) {
        ElMessage.warning('本条为开场白，无法重新生成！');
        return;
      }
      
      // 不删除当前 AI 消息，而是直接添加新的 AI 消息
      await this.sendMessage(userMessage);
    },
    // 处理点赞/点踩
    async handleLike(message, isLike) {
      try {
        const response = await getFeedBack({
          messageId: message.messageId,
          agentId: this.agentId,
          rating: isLike,
          content: ''
        })
        console.log(response)
        // 如果是点赞
        if (response && response) {
          message.liked = !message.liked
          if (message.liked) {
            message.disliked = false // 取消点踩
          }
        }
        // 如果是点踩
        else {
          message.disliked = !message.disliked
          if (message.disliked) {
            message.liked = false // 取消点赞
          }
        }
      } catch (e) {

      }
      // 这里可以添加与后端交互的逻辑
      // TODO: 发送点赞/点踩状态到服务器
    },
    toggleMode(mode) {
      this.activeMode = this.activeMode === mode ? '' : mode
    },
  },
  watch: {
    messages: {
      deep: true,
      handler() {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    }
  },
}
</script>

<style scoped>
/* 自定义样式 */
.main-container {
  width: calc(100% - 7rem);
  margin-left: 7rem;
  min-height: 100vh;
  background-image: url('@/assets/image/bg.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;  /* 关键属性：使背景图固定 */
  position: relative;
  padding-top: 1rem;
  display: flex;
  align-items: center;
}

/* 处理消息中的换行 */
p {
  white-space: pre-line;
}

/* 修改左侧容器样式 */
.left-container {
  width: 17rem;
  margin-left: 2.5rem;
  position: fixed; /* 固定定位 */
  top: 5rem; /* 调整顶部距离，与header保持一致 */
  bottom: 2rem; /* 底部留出间距 */
  display: flex;
  flex-direction: column;
  background-color: transparent;
}

/* 修改历史容器样式 */
.history-container {
  width: 100%;
  flex: 1;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid #596DF4;
  background-color: rgba(255, 255, 255, 0.1); /* 添加半透明背景 */
  overflow-y: auto;
  color: #333;
}

/* 修改滚动条样式 */
.history-container::-webkit-scrollbar {
  width: 4px;
}

.history-container::-webkit-scrollbar-thumb {
  background-color: rgba(89, 109, 244, 0.3);
  border-radius: 2px;
}

.history-container::-webkit-scrollbar-track {
  background-color: transparent;
}


/* 调整头部样式 */
header {
  position: fixed;
  top: 0;
  left: 0;
  margin-left: 7rem;
  width: calc(100% - 7rem);
  z-index: 50;
  background-color: #e0f0fa; /* 添加背景颜色 */
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); /* 可选：添加一个淡淡的边框 */
}

/* 修改主聊天区域布局，调整上边距以适应固定定位的header */
.chat-wrapper {
  width: calc(100vw - 7rem - 34rem - 8rem);
  min-height: calc(100vh - 5rem);
  margin-top: 2rem; /* 添加这行，为固定定位的header留出空间 */
  padding-bottom: 3.5rem;
  z-index: 10;
}

.chat-footer-pic{
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  z-index: 1;
}

/* 消息列表容器 */
.messages-container {
  width: 100%;
  min-height: calc(100vh - 12rem);
  max-height: calc(100vh - 12rem);  /* 添加最大高度 */
  padding: 1rem;
  padding-bottom: 8rem;
  overflow-y: auto;
  scroll-behavior: smooth;
}

/* 输入区域包装器 */

.input-wrapper {
  width: calc(100% - 7rem - 44rem);
  margin-left: 3.5rem;
  position: fixed;
  bottom: 0rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20;
}

.input-container {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  height: 12.5rem;
  position: relative;
  z-index: 20;
  overflow: auto;
}

/* 移除旧的 message-input 类样式，使用新的 textarea 样式 */
textarea {
  width: 100%;
  border: none;
  outline: none;
}

.func-container {
  position: absolute;
  bottom: 0;
  right: 0;
}

/* 消息样式调整 */
.flex.mb-4 {
  width: 100%;
  font-size: 1.3em; /* 放大字体 */
}

/* AI消息和用户消息的最大宽度调整 */
.max-w-\[50\%\] {
  max-width: 60%; /* 统一消息最大宽度 */
}




.menu-active {
  color: #596DF4;
  background-color: rgba(89, 109, 244, 0.14);
}

/* 调整 details 样式 */
:deep(details) {
  margin-bottom: 4px;
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 4px 8px;
}

:deep(details:empty) {
  display: none;
}

:deep(summary) {
  cursor: pointer;
  user-select: none;
  padding: 2px 0;
  color: #666;
  font-size: 0.9em;
}

/* 添加悬停效果 */
:deep(summary:hover) {
  color: #333;
}

:deep(p) {
  white-space: pre-line;
  transition: all 0.3s ease;
  margin: 0;
}

:deep(details > *:not(summary)) {
  margin-top: 4px;
  padding-left: 8px;
  border-left: 2px solid #e5e7eb;
}

.message-container {
  line-height: 1.5;
}

.message-container :deep(p:first-child) {
  margin-top: 0;
}

.message-container :deep(p:last-child) {
  margin-bottom: 0;
}

/* 确保内容紧贴容器 */
.message-container :deep(*:first-child) {
  margin-top: 0;
}

/* 修改 AI 助手图片容器样式 */
.absolute.bottom-10.right-28 {
  z-index: 10; /* 确保在输入框下方 */
}

/* 添加上传按钮样式 */
.upload-button {
  display: inline-block;
  position: relative;
}

.upload-button :deep(.el-upload) {
  display: block;
}

.upload-button :deep(.el-upload-list) {
  display: none;
}

/* 添加文件类型图标样式 */
.file-icon {
  margin-right: 4px;
}

.uploaded-files-preview {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.file-preview-item {
  transition: all 0.3s ease;
}

.file-preview-item:hover {
  background-color: #f3f4f6;
}

.file-preview-item button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.file-preview-item:hover button {
  opacity: 1;
}
/* 添加对话列表项的悬停效果 */
.btn-transparent {
  transition: all 0.3s ease;
}

.btn-transparent:hover {
  background-color: rgba(89, 109, 244, 0.1);
}

/* 确保按钮在悬停时才显示 */
.btn-transparent .opacity-0 {
  transition: opacity 0.2s ease;
}

.btn-transparent:hover .opacity-0 {
  opacity: 1;
}

/* 添加文件预览相关样式 */
:deep(.text-preview-dialog) {
  max-width: 80vw;
  max-height: 80vh;
}

:deep(.text-preview-dialog .el-message-box__content) {
  max-height: 70vh;
  overflow: auto;
  white-space: pre-wrap;
  font-family: monospace;
}

:deep(.video-preview-dialog) {
  max-width: 80vw;
}

:deep(.video-preview-dialog .el-message-box__content) {
  text-align: center;
}

/* 确保图片预览器正确显示 */
:deep(.el-image-viewer__wrapper) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2100;
}

:deep(.el-image-viewer__close) {
  color: #fff;
  font-size: 40px;
  top: 40px;
  right: 40px;
}

/* 添加 markdown 样式 */
:deep(.message-content) {
  line-height: 1.6;
}

:deep(.message-content h1) {
  font-size: 1.5em;
  margin: 0.5em 0;
}

:deep(.message-content h2) {
  font-size: 1.3em;
  margin: 0.5em 0;
}

:deep(.message-content h3) {
  font-size: 1.1em;
  margin: 0.5em 0;
}

:deep(.message-content pre) {
  background: #f4f4f4;
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
}

:deep(.message-content code) {
  background: #f4f4f4;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

:deep(.message-content blockquote) {
  border-left: 4px solid #ddd;
  margin: 0;
  padding-left: 1em;
  color: #666;
}

:deep(.message-content ul),
:deep(.message-content ol) {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

:deep(.message-content p) {
  margin: 0.5em 0;
}

:deep(.message-content table) {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
}

:deep(.message-content th),
:deep(.message-content td) {
  border: 1px solid #ddd;
  padding: 0.5em;
}

:deep(.message-content img) {
  max-width: 100%;
  height: auto;
}

/* 修改按钮组样式 */
.group {
  margin-bottom: 2rem; /* 为按钮组留出空间 */
}

/* 按钮组悬停效果 */
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
  transform: translateY(0);
}

/* 按钮组初始状态 */
.opacity-0 {
  opacity: 0;
  transform: translateY(-5px);
}

/* 添加按钮过渡动画 */
button {
  transition: all 0.2s ease;
}

/* 按钮点击效果 */
button:active {
  transform: scale(0.95);
}

/* 确保消息之间有足够间距 */
.flex.mb-4 {
  margin-bottom: 2rem;
}

/* 按钮组容器样式 */
.group-hover\:opacity-100 {
  z-index: 1;
  white-space: nowrap;
}

/* 在 style 标签中添加 */
.mode-button {
  transition: all 0.2s ease;
}

.mode-button:hover {
  transform: scale(1.05);
}

.mode-button:active {
  transform: scale(0.95);
}

/* 美化滚动条 */
.messages-container::-webkit-scrollbar {
  width: 0; /* Hide the scrollbar */
}

.messages-container {
  scrollbar-width: none; /* For Firefox */
  -ms-overflow-style: none; /* For Internet Explorer and Edge */
}

.messages-container::-webkit-scrollbar-thumb {
  background: transparent; /* Make scrollbar thumb transparent */
}

:deep(.history-chat-container .input-wrapper) {
  position: fixed;
  bottom: 2rem;
  width: calc(100vw - 7rem - 34rem - 8rem);
  margin-left: 0;
  z-index: 30;
}
</style>
