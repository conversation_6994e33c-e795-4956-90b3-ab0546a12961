<template>
  <div class="flex flex-col items-center w-full">
    <!-- 主聊天区域 -->
    <div class="main-container">
      <header class="p-4 text-center">
        <div class="inline-flex gap-2 items-center text-xl font-bold text-txt-primary">
          <span>
            {{
              activeConversationIndex !== null && historyConversations[activeConversationIndex]
              ? historyConversations[activeConversationIndex].conversationName || `历史对话${activeConversationIndex}`
              : `历史对话${activeConversationIndex}`
            }}
          </span>
        </div>
      </header>

      <!-- 左侧历史对话列表 -->
      <div class="left-container">
        <div class="mb-6 text-xl font-bold text-txt-dark">
          历史对话
        </div>
        <div class="mb-6 text-base text-txt-middle">
          查看对话内容
        </div>
        <div class="history-container">
          <div class="mb-4 text-base text-gray-500">请选择历史对话</div>
          <ul>
            <li v-for="(group, groupName) in groupedConversations" :key="groupName">
              <div class="font-bold">{{ groupName }}</div>
              <ul>
                <li
                  v-for="conversation in group"
                  :key="conversation.conversationId"
                  class="flex justify-between items-center p-2 rounded-md btn-transparent"
                  :class="{ 'menu-active': activeConversationId === conversation.conversationId }"
                >
                  <!-- 对话名称，点击加载对话 -->
                  <el-tooltip
                    :content="conversation?.conversationName || `历史对话${conversation.index}`"
                    placement="top"
                    :show-after="500"
                    :hide-after="0"
                  >
                    <span 
                      @click="loadConversation(conversation)"
                      class="overflow-hidden flex-1 truncate cursor-pointer"
                    >
                      {{ conversation?.conversationName || `历史对话${conversation.index}` }}
                    </span>
                  </el-tooltip>
                  
                  <!-- 操作按钮 -->
                  <div class="flex gap-2 opacity-0 group-hover:opacity-100">
                    <!-- 重命名按钮 -->
                    <button
                      @click.stop="handleRename(conversation)"
                      class="p-1 text-gray-400 hover:text-primary"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
                    </button>
                    <!-- 删除按钮 -->
                    <button
                      @click.stop="handleDelete(conversation)"
                      class="p-1 text-gray-400 hover:text-red-500"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>
                    </button>
                  </div>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>

      <div class="flex justify-center w-full">
        <!-- 聊天区域 -->
        <div class="chat-wrapper">
          <!-- 使用聊天容器组件 -->
          <chat-container
            v-if="messages.length > 0 || !activeConversationId"
            :agent-id="agentId"
            :user-id="userStore.userInfo?.userId"
            :initial-messages="messages"
            :conversation-id="activeConversationId"
            :parent-type="'history'"
            :read-only="true"
            :fixed-height="true"
            :enable-audio="true"
            @error="handleError"
            @message-sent="handleMessageSent"
            @message-received="handleMessageReceived"
            @like="handleLikeAction"
            ref="chatContainer"
            class="history-chat-container"
          >
            <template #footer>
              <div class="flex justify-center items-center">
                <el-button :loading="fetchingHistory" @click="loadMoreMessages">{{ t('loadMore') }}</el-button>
              </div>
            </template>
          </chat-container>
          <div v-if="!activeConversationId" class="flex fixed justify-center items-center h-full text-gray-500">
            请选择一个对话
          </div>
        </div>
      </div>

      <!-- <div class="chat-footer-pic" style="z-index: 1;">
        <img src="@/assets/image/ai.png" alt="AI助手" class="w-30 h-30" />
      </div> -->
    </div>
  </div>
</template>

<script>
import { h } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import ChatContainer from '@/components/ChatContainer.vue'
import { marked } from 'marked'
import {
  getConversationList,
  getFeedBack,
  getMessageList,
  removeConversation,
  renameConversation
} from '@/api/ai'

export default {
  name: 'HistoryDialog2',
  components: {
    ChatContainer
  },
  data() {
    return {
      historyConversations: [],
      messages: [],
      activeConversationId: null,
      activeConversationIndex: '',
      pageNo: 1,
      pageSize: 100,
      userStore: useUserStore(),
      agentId: '',
      isLoading: false,
      error: null,
      fetchingHistory: false
    }
  },
  computed: {
    groupedConversations() {
      const today = new Date();
      const groups = {
        '今天': [],
        '昨天': [],
        '前3天': [],
        '前7天': [],
        '更早': []
      };

      this.historyConversations.forEach(conversation => {
        const updateDate = new Date(conversation.updateTime);
        const diffDays = Math.floor((today - updateDate) / (1000 * 60 * 60 * 24));

        if (diffDays < 1) {
          groups['今天'].push(conversation);
        } else if (diffDays < 2) {
          groups['昨天'].push(conversation);
        } else if (diffDays < 4) { // 2-3天
          groups['前3天'].push(conversation);
        } else if (diffDays < 8) { // 4-7天
          groups['前7天'].push(conversation);
        } else {
          groups['更早'].push(conversation);
        }
      });

      // Filter out empty groups
      return Object.fromEntries(Object.entries(groups).filter(([_, group]) => group.length > 0));
    }
  },
  async mounted() {
    await this.fetchConversationList()
  },
  methods: {
    async fetchConversationList() {
      this.isLoading = true;
      this.error = null;
      try {
        const params = {
          userId: this.userStore.userInfo?.userId,
          pageNo: this.pageNo,
          pageSize: this.pageSize
        };

        const response = await getConversationList(params);
        if (response) {
          // Add an index to each conversation
          this.historyConversations = (response.rows || []).map((conversation, index) => ({
            ...conversation,
            index: index
          }));
        }
      } catch (error) {
        console.error('Failed to fetch conversation list:', error);
        this.error = '获取对话列表失败，请稍后重试';
      } finally {
        this.isLoading = false;
      }
    },
    
    async loadConversation(item) {
      // 先设置为空数组，强制组件重新渲染
      this.messages = [];
      
      this.activeConversationId = item.conversationId;
      this.activeConversationIndex = item.index;
      this.agentId = item.agentId;
      this.isLoading = true;
      this.error = null;

      try {
        const conversation = this.historyConversations.find(conv => conv.conversationId === item.conversationId);
        if (!conversation) {
          console.error('Conversation not found for ID:', item.conversationId);
          return;
        }
        
        // 找到真实索引
        const realIndex = this.historyConversations.findIndex(
          c => c.conversationId === item.conversationId
        );
        if (realIndex !== -1) {
          this.activeConversationIndex = realIndex;
        }
        
        const params = {
          userId: this.userStore.userInfo?.userId,
          agentId: this.agentId,
          conversationId: conversation.conversationId,
          pageNo: '1',
          pageSize: '50'
        };

        const response = await getMessageList(params);

        if (response && response.rows) {
          // 添加新消息
          const newMessages = response.rows.flatMap(msg => {
            const messages = [];
            messages.push({
              sender: 'user',
              content: msg.query,
              createTime: msg.createTime
            });

            if (msg.answer && msg.answer.trim() !== '') {
              messages.push({
                sender: 'ai',
                content: this.parseMarkdown(msg.answer.replace(/<details[\s\S]*?<\/details>/g, '').trim()),
                messageId: msg.messageId,
                createTime: msg.createTime
              });
            }

            return messages;
          });

          // 排序消息
          newMessages.sort((a, b) =>
            new Date(a.createTime) - new Date(b.createTime)
          );
          
          // 使用Vue的nextTick确保DOM更新后再设置消息
          this.$nextTick(() => {
            this.messages = newMessages;
          });
        }
      } catch (error) {
        console.error('Failed to fetch messages:', error);
        this.error = '获取对话消息失败，请稍后重试';
      } finally {
        this.isLoading = false;
      }
    },
    
    // 处理重命名对话
    async handleRename(conversation) {
      try {
        const { value: formValues } = await ElMessageBox({
          title: '重命名对话',
          message: h('div', null, [
            h('div', { class: 'flex items-center mt-4' }, [
              h('span', { class: 'mr-4' }, '是否自动命名'),
              h('div', { class: 'flex items-center gap-4' }, [
                h('label', { class: 'inline-flex items-center' }, [
                  '是',
                  h('input', {
                    type: 'radio',
                    name: 'autoGenerate',
                    value: 'true',
                    class: 'ml-2',
                    onChange: (e) => {
                      const input = document.querySelector('.el-message-box__input input')
                      if (input) {
                        input.value = ''
                        input.disabled = e.target.checked
                      }
                    }
                  })
                ]),
                h('label', { class: 'inline-flex items-center' }, [
                  '否',
                  h('input', {
                    type: 'radio',
                    name: 'autoGenerate',
                    value: 'false',
                    class: 'ml-2',
                    checked: true,
                    onChange: (e) => {
                      const input = document.querySelector('.el-message-box__input input')
                      if (input) {
                        input.value = conversation.conversationName || `历史对话${conversation.index}`
                        input.disabled = false
                      }
                    }
                  })
                ])
              ])
            ])
          ]),
          showInput: true,
          inputValue: conversation.conversationName || `历史对话${conversation.index}`,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (value) => {
            const autoGenerate = document.querySelector('input[name="autoGenerate"]:checked').value === 'true'
            if (!autoGenerate && !value) {
              return '名称不能为空'
            }
            return true
          }
        })

        if (formValues !== undefined) {
          const autoGenerate = document.querySelector('input[name="autoGenerate"]:checked').value === 'true'
          const params = {
            userId: this.userStore.userInfo?.userId,
            agentId: this.agentId,
            conversationId: conversation.conversationId,
            conversationName: autoGenerate ? null : formValues,
            autoGenerate: autoGenerate
          }

          const res = await renameConversation(params)
          const name = res.data.conversationName;
          
          // 更新本地数据
          const index = this.historyConversations.findIndex(
            c => c.conversationId === conversation.conversationId
          )
          if (index > -1) {
            this.historyConversations[index].conversationName = name;
            if (conversation.conversationId === this.activeConversationId) {
              this.activeConversationIndex = index
            }
          }

          ElMessage.success('重命名成功')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重命名失败:', error)
          ElMessage.error('重命名失败，请重试')
        }
      }
    },

    // 处理删除对话
    async handleDelete(conversation) {
      try {
        await ElMessageBox.confirm(
          '确定要删除这个对话吗？此操作不可恢复',
          '删除对话',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        const params = {
          userId: this.userStore.userInfo?.userId,
          agentId: this.agentId,
          conversationId: conversation.conversationId
        }

        await removeConversation(params)

        // 更新本地数据
        const index = this.historyConversations.findIndex(
          c => c.conversationId === conversation.conversationId
        )
        if (index > -1) {
          this.historyConversations.splice(index, 1)
        }

        // 如果删除的是当前激活的对话，清空消息列表
        if (this.activeConversationId === conversation.conversationId) {
          this.messages = []
          this.activeConversationId = null
          this.activeConversationIndex = null
        }

        ElMessage.success('删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          ElMessage.error('删除失败，请重试')
        }
      }
    },
    
    handleError({ type, message }) {
      ElMessage.error(message)
    },
    
    handleMessageSent(data) {
      console.log('Message sent:', data)
      // 可以在这里添加额外的处理逻辑
    },
    
    handleMessageReceived(data) {
      console.log('Message received:', data)
      // 可以在这里添加额外的处理逻辑
      
      // 如果是新会话，可能需要刷新会话列表
      if (!this.historyConversations.some(conv => conv.conversationId === data.conversationId)) {
        this.fetchConversationList()
      }
    },
    
    async handleLikeAction({ messageId, isLike }) {
      try {
        await getFeedBack({
          messageId: messageId,
          agentId: this.agentId,
          rating: isLike,
          content: ''
        })
        
        ElMessage.success(isLike ? '点赞成功' : '反馈已提交')
      } catch (error) {
        console.error('点赞/点踩失败:', error)
        ElMessage.error('操作失败，请重试')
      }
    },
    
    parseMarkdown(content) {
      if (!content) return ''
      try {
        // 先处理<think></think>标签，参考ChatContainer的处理方式
        let processedContent = content;
        
        // 检查是否包含<think>标签
        if (processedContent.includes('<think>')) {
          // 提取thinking内容
          const thinkingMatch = processedContent.match(/<think>([\s\S]*?)<\/think>/);
          if (thinkingMatch) {
            const thinkingContent = thinkingMatch[1];
            
            // 移除原始的<think></think>标签
            processedContent = processedContent.replace(/<think>[\s\S]*?<\/think>/, '');
            
            // 创建thinking显示块，使用与ChatContainer相同的样式
            const thinkingHtml = `<details open style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;margin-bottom: 16px;"><summary>Thinking...</summary>\n${thinkingContent}\n</details>`;
            
            // 将thinking块放在内容前面
            processedContent = thinkingHtml + processedContent;
          }
        }
        
        // 替换图片相对路径为绝对路径
        // 将 ![image](/files/... 替换为 ![image](https://ai.gxgeq.com/files/...
        processedContent = processedContent.replace(/!\[([^\]]*)\]\(\/files\//g, '![$1](https://ai.gxgeq.com/files/');
        
        return marked(processedContent, {
          breaks: true, // 支持换行
          sanitize: false, // 允许HTML标签
          gfm: true, // 启用 GitHub 风格的 markdown
          pedantic: false, // 不那么严格的解析
        })
      } catch (e) {
        console.error('Markdown parsing error:', e)
        return content
      }
    },
    
    async loadMoreMessages() {
      this.fetchingHistory = true;
      try {
        const params = {
          userId: this.userStore.userInfo?.userId,
          agentId: this.agentId,
          conversationId: this.activeConversationId,
          pageNo: this.pageNo + 1,
          pageSize: this.pageSize
        };

        const response = await getMessageList(params);

        if (response && response.rows) {
          // 添加新消息
          const newMessages = response.rows.flatMap(msg => {
            const messages = [];
            messages.push({
              sender: 'user',
              content: msg.query,
              createTime: msg.createTime
            });

            if (msg.answer && msg.answer.trim() !== '') {
              messages.push({
                sender: 'ai',
                content: this.parseMarkdown(msg.answer.replace(/<details[\s\S]*?<\/details>/g, '').trim()),
                messageId: msg.messageId,
                createTime: msg.createTime
              });
            }

            return messages;
          });

          // 排序消息
          newMessages.sort((a, b) =>
            new Date(a.createTime) - new Date(b.createTime)
          );
          
          // 使用Vue的nextTick确保DOM更新后再设置消息
          this.$nextTick(() => {
            this.messages = [...this.messages, ...newMessages];
          });
        }
      } catch (error) {
        console.error('Failed to load more messages:', error);
        this.error = '获取更多消息失败，请稍后重试';
      } finally {
        this.fetchingHistory = false;
      }
    }
  }
}
</script>

<style scoped>
/* 保留原有样式 */
.main-container {
  width: calc(100% - 7rem);
  margin-left: 7rem;
  min-height: 100vh;
  background-image: url('@/assets/image/bg.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  padding-top: 1rem;
  display: flex;
  align-items: center;
}

/* 处理消息中的换行 */
p {
  white-space: pre-line;
}

/* 修改左侧容器样式 */
.left-container {
  width: 17rem;
  margin-left: 2.5rem;
  position: fixed; /* 固定定位 */
  top: 5rem; /* 调整顶部距离，与header保持一致 */
  bottom: 2rem; /* 底部留出间距 */
  display: flex;
  flex-direction: column;
  background-color: transparent;
}

/* 修改历史容器样式 */
.history-container {
  width: 100%;
  flex: 1;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid #596DF4;
  background-color: rgba(255, 255, 255, 0.1); /* 添加半透明背景 */
  overflow-y: auto;
  color: #333;
}

/* 修改滚动条样式 */
.history-container::-webkit-scrollbar {
  width: 4px;
}

.history-container::-webkit-scrollbar-thumb {
  background-color: rgba(89, 109, 244, 0.3);
  border-radius: 2px;
}

.history-container::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 调整头部样式 */
header {
  position: fixed;
  top: 0;
  left: 0;
  margin-left: 7rem;
  width: calc(100% - 7rem);
  z-index: 50;
  background-color: #e0f0fa; /* 添加背景颜色 */
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); /* 可选：添加一个淡淡的边框 */
}

/* 修改主聊天区域布局 */
.chat-wrapper {
  width: calc(100vw - 7rem - 34rem - 8rem);
  min-height: calc(100vh - 5rem);
  margin-top: 2rem;
  margin-left: 2.5rem; /* 添加左边距与input-style保持一致 */
  padding-bottom: 15rem; /* 增加底部padding，给输入框留出空间 */
  z-index: 10;
  position: relative; /* 添加相对定位 */
}

.chat-footer-pic {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  z-index: 1;
}

/* 按钮样式 */
.btn-transparent {
  transition: all 0.3s ease;
}

.btn-transparent:hover {
  background-color: rgba(89, 109, 244, 0.1);
}

/* 确保按钮在悬停时才显示 */
.btn-transparent .opacity-0 {
  transition: opacity 0.2s ease;
}

.btn-transparent:hover .opacity-0 {
  opacity: 1;
}

/* 菜单激活状态 */
.menu-active {
  color: #596DF4;
  background-color: rgba(89, 109, 244, 0.14);
}

/* 消息样式调整 */
:deep(.flex.mb-4) {
  width: 100%;
  font-size: 1em; /* 放大字体 */
}

/* 特殊样式用于历史对话组件中的聊天容器 */
:deep(.history-chat-container .input-wrapper) {
  position: fixed;
  bottom: 2rem;
  width: calc(100vw - 7rem - 34rem - 8rem - 4.2rem);
  margin-left: 5rem;
  z-index: 30;
}
</style>