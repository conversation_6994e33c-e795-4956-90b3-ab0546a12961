<template>
  <div>
    <div class="flex flex-col items-center w-full main-container">
      <!-- 头部 -->
      <header class="p-4 text-center">
        <div class="inline-flex items-center gap-2 text-xl font-bold text-txt-primary">
          <span>{{ agentName }}</span>
        </div>
      </header>

      <!-- 聊天区域 -->
      <div class="flex-1 p-4 space-y-4 chat-container" ref="chatContainer">
        <!-- AI消息和文档引用 -->
        <div v-for="(message, index) in messages" :key="index" class="flex" :class="message.sender === 'ai' ? 'justify-start' : 'justify-end'">
          <!-- AI头像和消息 -->
          <div v-if="message.sender === 'ai'" class="flex items-start gap-2 max-w-[60%] group relative">
            <div class="flex-shrink-0 w-16 h-16 overflow-hidden rounded-full">
              <img src="@/assets/image/kebao.png" alt="AI头像" class="object-cover w-full h-full" />
            </div>
            <div class="flex flex-col gap-2">
              <!-- AI回答内容 -->
              <div class="p-4 bg-white rounded-lg shadow-md">
                <div v-if="message.loading" class="flex items-center gap-2">
                  <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce"></div>
                  <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                  <div class="w-2 h-2 bg-gray-300 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
                </div>
                <div v-else class="text-gray-800" v-html="message.content || ''"></div>
              </div>
              
              <!-- 文档引用区域 -->
              <div v-if="message.documents && message.documents.length > 0" class="px-4 py-2 mt-1 text-sm rounded-lg bg-gray-50">
                <div class="mb-1 text-xs text-gray-500">参考文档：</div>
                <div class="space-y-1">
                  <div v-for="(doc, docIndex) in message.documents" :key="docIndex" 
                       class="flex items-center gap-2 p-2 transition-colors duration-200 rounded-md hover:bg-gray-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                      <polyline points="14 2 14 8 20 8"></polyline>
                      <line x1="16" y1="13" x2="8" y2="13"></line>
                      <line x1="16" y1="17" x2="8" y2="17"></line>
                      <polyline points="10 9 9 9 8 9"></polyline>
                    </svg>
                    <span class="text-gray-600">{{ doc.document_name }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 修改操作按钮组，添加重新生成按钮 -->
            <div class="absolute flex items-center gap-2 p-1 px-2 transition-all duration-200 bg-white border border-gray-100 rounded-full shadow-sm opacity-0 -bottom-8 left-16 group-hover:opacity-100">
              <!-- 复制按钮 -->
              <button 
                @click="copyMessage(message.content)"
                class="p-1 text-gray-400 transition-colors duration-200 rounded-full hover:text-primary hover:bg-gray-50"
                title="复制消息"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
              </button>
              
              <!-- 重新生成按钮 -->
              <button 
                @click="regenerateResponse(index)"
                class="p-1 text-gray-400 transition-colors duration-200 rounded-full hover:text-primary hover:bg-gray-50"
                title="重新生成"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
              </button>
              
              <!-- 点赞按钮 -->
              <button 
                @click="handleLike(message, true)"
                :class="[
                  'p-1 transition-colors duration-200 rounded-full hover:bg-gray-50',
                  message.liked ? 'text-primary' : 'text-gray-400 hover:text-primary'
                ]"
                title="点赞"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M7 10v12"/><path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"/></svg>
              </button>
              <!-- 点踩按钮 -->
              <button 
                @click="handleLike(message, false)"
                :class="[
                  'p-1 transition-colors duration-200 rounded-full hover:bg-gray-50',
                  message.disliked ? 'text-red-500' : 'text-gray-400 hover:text-red-500'
                ]"
                title="点踩"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 14V2"/><path d="M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z"/></svg>
              </button>
            </div>
          </div>
          
          <!-- 用户消息和头像 -->
          <div v-else class="flex items-start gap-2 max-w-[60%] group relative">
            <div class="p-4 rounded-lg shadow-md bg-primary">
              <p class="text-white">{{ message.content }}</p>
            </div>
            <!-- 添加用户消息复制按钮 -->
            <div class="absolute right-0 flex items-center gap-2 p-1 px-2 transition-all duration-200 bg-white border border-gray-100 rounded-full shadow-sm opacity-0 -bottom-8 group-hover:opacity-100">
              <button 
                @click="copyMessage(message.content)"
                class="p-1 text-gray-400 transition-colors duration-200 rounded-full hover:text-primary hover:bg-gray-50"
                title="复制消息"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="rounded-lg input-container" style="background: linear-gradient(to right, #f6fcfe, #eaf6ff)">
        <div class="relative flex flex-col p-4 bg-white rounded-lg shadow-md input-wrapper">
          <!-- 文件预览区域 -->
          <div class="file-preview-container">
            <div v-if="uploadedFiles.length > 0" class="flex flex-wrap gap-2 mb-3 uploaded-files-preview">
              <div 
                v-for="(file, index) in uploadedFiles" 
                :key="file.fileId"
                class="flex items-center p-2 rounded-lg bg-gray-50 file-preview-item"
              >
                <span class="mr-2">{{ getFileIcon(file.fileType) }}</span>
                <span class="text-sm text-gray-600 cursor-pointer hover:text-primary" @click="previewFile(file)">
                  {{ file.fileName }}
                </span>
                <button 
                  class="ml-2 text-gray-400 hover:text-red-500"
                  @click="removeFile(index)"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                </button>
              </div>
            </div>
          </div>

          <!-- 输入框区域 -->
          <div class="input-area">
            <!-- 输入框 -->
            <textarea
              v-model="userInput"
              placeholder="你可以问我任何问题，或说出我如何帮助你..."
              class="w-full p-2 text-gray-700 align-top outline-none resize-none input-textarea"
              @keyup.enter.prevent="handleEnterPress"
              rows="3"
            ></textarea>
          </div>

          <!-- 底部工具栏 -->
          <div class="bottom-toolbar">
            <div class="flex items-center justify-between">
              <!-- 左侧标签按钮 -->
              <div class="flex items-center gap-2 ml-4">
                <!--<button 
                  @click="toggleMode('deep')" 
                  class="px-3 py-1 text-sm transition-colors duration-200 rounded-full"
                  :class="[
                    activeModes.includes('deep')
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  ]"
                >
                  <div class="flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12z"></path><path d="M12 8v8"></path><path d="M8 12h8"></path></svg>
                    深度思考
                  </div>
                </button>-->
                <button 
                  @click="toggleMode('search')" 
                  class="px-3 py-1 text-sm transition-colors duration-200 rounded-full"
                  :class="[
                    activeModes.includes('search')
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  ]"
                >
                  <div class="flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                    联网搜索
                  </div>
                </button>
                 <div class="px-2 text-xs text-gray-400">内容由 AI 生成，请仔细甄别</div>
              </div>

              <!-- 右侧工具栏 -->
              <div class="flex items-center gap-2 m-4 text-gray-500 func-container">
                <el-upload
                  class="upload-button"
                  :action="`/api/v1/public/chat/uploadFile`"
                  :data="{ agentId: agentId }"
                  :show-file-list="false"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeUpload"
                >
                  <button class="p-2 rounded-full hover:bg-gray-100">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-paperclip"><path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"/></svg>
                  </button>
                </el-upload>
                <button 
                  @click="sendMessage" 
                  class="p-2 text-white rounded-full bg-primary hover:bg-primary-middle"
                  :disabled="isLoading"
                >
                  <svg v-if="!isLoading" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="animate-spin"><path d="M21 12a9 9 0 1 1-6.219-8.56"/></svg>
                </button>
              </div>
            </div>

          </div>
        </div>
      </div>

      <!-- AI助手图标 -->
      <!-- <div class="absolute bottom-10 right-28">
        <img src="@/assets/image/ai.png" alt="AI助手" class="w-30 h-30" />
      </div> -->
    </div>
    
    <!-- 图片预览组件 -->
    <el-image
      v-if="previewVisible"
      :src="previewUrl"
      :preview-src-list="[previewUrl]"
      hide-on-click-modal
      @close="previewVisible = false"
      class="preview-image"
    />
  </div>
</template>

<script>
import { marked } from 'marked'
import { handleSendMessage,speechToText,textToSpeech } from '@/api/ai'
import { ElMessage, ElMessageBox, ElImageViewer } from 'element-plus'
import { createVNode, render } from 'vue'
import { useUserStore } from '@/stores/user'

export default {
  name: 'ChatInterface',
  components: {
    ElImageViewer
  },
  props: {
    agentId: {
      type: String,
      required: false,
      default: '9bad24bea86d3d7b2a1eff108ed7e7ec'
    },
    agentName: {
      type: String,
      required: false,
      default: '科宝'
    }
  },
  data() {
    return {
      messages: [],
      userInput: '',
      currentConversationId: '',
      isLoading: false,
      uploadedFiles: [],
      previewVisible: false,
      previewUrl: '',
      textToConvert: '',
      userStore: useUserStore(),
      activeModes: [], // 改用数组来存储激活的模式，而不是单个字符串
      scrollTimer: null, // 添加滚动定时器
    }
  },
  computed: {
    // 使用计算属性来获取初始消息
    initialMessage() {
      return {
        sender: 'ai',
        content: `Hi, 我是${this.agentName}～\n你可以把你的需求告诉我，让我来帮助你吧！`
      }
    }
  },
  created() {
    // 设置初始消息
    if (this.agentName) {
      this.messages = [this.initialMessage]
      
      // 获取路由参数
      const initialMessage = this.$route.query.initialMessage
      const initialFiles = this.$route.query.files ? JSON.parse(this.$route.query.files) : []
      const modes = this.$route.query.modes ? JSON.parse(this.$route.query.modes) : []
      
      // 设置模式
      this.activeModes = modes
      
      // 如果有文件，添加到上传文件列表
      if (initialFiles.length > 0) {
        this.uploadedFiles = initialFiles.map(file => ({
          fileId: file.fileId,
          fileType: file.fileType,
          extension: file.extension,
          fileName: file.fileName
        }))
      }

      // 如果有初始消息或文件，直接发送一次消息
      if (initialMessage || initialFiles.length > 0) {
        // 直接调用 sendMessage，不需要先添加到消息列表
        this.$nextTick(() => {
          this.sendMessage(initialMessage || '')
        })
      }
    }
  },
  methods: {
    // 添加 markdown 解析方法
    parseMarkdown(content) {
      if (!content) return ''
      try {
        // 替换图片相对路径为绝对路径
        // 将 ![image](/files/... 替换为 ![image](https://ai.gxgeq.com/files/...
        const processedContent = content.replace(/!\[([^\]]*)\]\(\/files\//g, '![$1](https://ai.gxgeq.com/files/');
        
        return marked(processedContent, {
          breaks: true, // 支持换行
          sanitize: false, // 允许HTML标签
          gfm: true, // 启用 GitHub 风格的 markdown
          pedantic: false, // 不那么严格的解析
        })
      } catch (e) {
        console.error('Markdown parsing error:', e)
        return content
      }
    },

    // 修改文件类型判断方法
    getFileType(filename) {
      const extension = filename.split('.').pop().toUpperCase()
      
      const typeMap = {
        DOCUMENT: ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
        IMAGE: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
        AUDIO: ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
        VIDEO: ['MP4', 'MOV', 'MPEG', 'MPGA']
      }
      
      for (const [type, extensions] of Object.entries(typeMap)) {
        if (extensions.includes(extension)) {
          return {
            type,
            extension
          }
        }
      }
      
      return {
        type: 'CUSTOM',
        extension
      }
    },

    // 获取文件图标
    getFileIcon(fileType) {
      const icons = {
        'DOCUMENT': '📄',
        'IMAGE': '🖼️',
        'AUDIO': '🎵',
        'VIDEO': '🎥',
        'CUSTOM': '📎'
      }
      return icons[fileType] || '📎'
    },

    // 预览文件
    previewFile(file) {
      // 图片预览
      if (file.fileType === 'IMAGE') {
        const img = new Image()
        img.src = URL.createObjectURL(file.raw)
        
        // 创建图片预览实例
        const container = document.createElement('div')
        const vnode = createVNode(ElImageViewer, {
          urlList: [img.src],
          onClose: () => {
            render(null, container)
            document.body.removeChild(container)
          }
        })
        
        document.body.appendChild(container)
        render(vnode, container)
      } 
      // PDF预览
      else if (file.fileType === 'DOCUMENT' && ['PDF'].includes(file.extension)) {
        window.open(URL.createObjectURL(file.raw))
      } 
      // 文本文件预览
      else if (file.fileType === 'DOCUMENT' && ['TXT', 'MD', 'HTML', 'CSV'].includes(file.extension)) {
        const reader = new FileReader()
        reader.onload = (e) => {
          ElMessageBox.alert(e.target.result, '文件预览', {
            customClass: 'text-preview-dialog',
            dangerouslyHtmlString: file.extension === 'HTML'
          })
        }
        reader.readAsText(file.raw)
      }
      // 音频预览
      else if (file.fileType === 'AUDIO') {
        const audio = new Audio(URL.createObjectURL(file.raw))
        audio.play()
      }
      // 视频预览
      else if (file.fileType === 'VIDEO') {
        ElMessageBox.alert(
          `<video controls style="max-width: 100%">
            <source src="${URL.createObjectURL(file.raw)}" type="video/${file.extension.toLowerCase()}">
          </video>`,
          '视频预览',
          {
            dangerouslyHtmlString: true,
            customClass: 'video-preview-dialog'
          }
        )
      }
      else {
        ElMessage.info('该文件类型暂不支持预览')
      }
    },

    // 删除文件
    removeFile(index) {
      this.uploadedFiles.splice(index, 1)
    },

    // 修改上传前的验证方法
    beforeUpload(file) {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('userId', this.userStore.userInfo?.userId)
      formData.append('agentId', this.agentId)

      // 文件大小验证（10MB）
      const isLt10M = file.size / 1024 / 1024 < 50
      if (!isLt10M) {
        ElMessage.error('文件大小不能超过 50MB!')
        return false
      }

      // 可以在这里添加文件类型的限制
      // const allowedTypes = ['document', 'image', 'audio', 'video']
      // if (!allowedTypes.includes(type)) {
      //   ElMessage.error('不支持该文件类型!')
      //   return false
      // }

      return true
    },

    // 修改上传成功的处理方法
    handleUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = this.getFileType(file.name)
        
        // 将文件信息添加到数组中
        this.uploadedFiles.push({
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name,
          raw: file.raw, // 保存原始文件对象用于预览
          size: file.size
        })
        
        ElMessage.success({
          message: '文件上传成功',
          duration: 2000
        })
      } else {
        ElMessage.error('文件上传失败')
      }
    },

    // 上传失败的处理
    handleUploadError() {
      ElMessage.error('文件上传失败，请重试')
    },

    // 添加处理回车按键的方法
    handleEnterPress(event) {
      event.preventDefault();
      if (event.shiftKey) {
        // Shift + Enter 换行
        return;
      }
      if (this.userInput.trim()) {
        this.sendMessage();
      }
    },

    // 修改切换模式的方法
    toggleMode(mode) {
      const index = this.activeModes.indexOf(mode);
      if (index === -1) {
        // 如果模式不在数组中，添加它
        this.activeModes.push(mode);
      } else {
        // 如果模式已经在数组中，移除它
        this.activeModes.splice(index, 1);
      }
    },

    // 修改发送消息方法
    async sendMessage(messageContent = null) {
      // 检查是否有内容可发送
      if ((!this.userInput.trim() && !messageContent && this.uploadedFiles.length === 0) || this.isLoading) return;
      
      this.isLoading = true;
      
      const files = this.uploadedFiles.map(file => ({
        fileId: file.fileId,
        fileType: file.fileType,
        extension: file.extension
      }));
      let inputs = {}
      // 确保使用正确的消息内容
      let messageText = '';
      if (typeof messageContent === 'string' && messageContent.trim()) {
        messageText = messageContent.trim();
      } else {
        messageText = this.userInput.trim();
      }
      
      // 添加文件信息到消息内容
      if (this.uploadedFiles.length > 0) {
        const filesList = this.uploadedFiles
          .map(file => `${this.getFileIcon(file.fileType)} ${file.fileName}`)
          .join('\n');
        messageText += `\n\n附件:\n${filesList}`;
      }
      
      // 添加用户消息到消息列表
      this.messages.push({
        sender: 'user',
        content: messageText
      });

      // 清理输入框和文件列表
      this.userInput = '';
      this.uploadedFiles = [];

      // 添加 AI 消息占位
      this.messages.push({
        sender: 'ai',
        content: '',
        loading: true
      });

      let hasReceivedResponse = false;
      const timeout = setTimeout(() => {
        if (!hasReceivedResponse) {
          this.isLoading = false;
          const aiMessageIndex = this.messages.length - 1;
          this.messages[aiMessageIndex].loading = false;
          this.messages[aiMessageIndex].content = '响应超时，请稍后重试。';
          console.error('Request timed out after 2 minutes');
        }
      }, 120000);
      if(this.activeModes.includes('search')){
        inputs = { online: 1 }
      } else {
        inputs = { online: 0 }
      }
      handleSendMessage(messageText, {
        agentId: this.agentId,
        conversationId: this.currentConversationId,
        userId: this.userStore.userInfo?.userId,
        files: files,
        modes: this.activeModes,
        inputs: inputs,
        onData: (text, messageId, conversationId, metadata) => {
          hasReceivedResponse = true;
          clearTimeout(timeout);
          
          const aiMessageIndex = this.messages.length - 1;
          this.messages[aiMessageIndex].loading = false;
          
          // 处理文档引用
          if (metadata?.retriever_resources) {
            this.messages[aiMessageIndex].documents = metadata.retriever_resources;
          }
          
          if (text.includes('Thinking...')) {
            const thinkingContent = text.match(/<details[^>]*>([\s\S]*?)<\/details>/)?.[1] || '';
            if (!thinkingContent.trim() || thinkingContent.includes('</summary>') && thinkingContent.split('</summary>')[1].trim() === '') {
              const newText = text.replace(/<details[^>]*>[\s\S]*?<\/details>/, '');
              this.messages[aiMessageIndex].content = this.parseMarkdown(newText
                .replace(/<\/details>\n+/, '</details>')); // 处理 details 标签后的换行
            } else {
              this.messages[aiMessageIndex].content = this.parseMarkdown(text
                .replace('<details open', '<details')
                .replace(/<\/details>\n+/, '</details>'));
            }
          } else {
            this.messages[aiMessageIndex].content = this.parseMarkdown(text); // 将两个或更多换行符替换为单个换行符
          }

          if (conversationId) {
            this.currentConversationId = conversationId;
          }
          
          // 使用防抖处理滚动
          this.debouncedScroll();
        },
        onCompleted: () => {
          hasReceivedResponse = true;
          clearTimeout(timeout);
          this.isLoading = false;
        },
        onError: (error) => {
          hasReceivedResponse = true;
          clearTimeout(timeout);
          console.error('Chat error:', error);
          const aiMessageIndex = this.messages.length - 1;
          this.messages[aiMessageIndex].loading = false;
          this.messages[aiMessageIndex].content = '抱歉，出现了一些错误，请稍后重试。';
          this.isLoading = false;
        }
      });
    },
    scrollToBottom() {
      if (this.$refs.chatContainer) {
        const container = this.$refs.chatContainer;
        // 使用 nextTick 确保 DOM 更新后再滚动
        this.$nextTick(() => {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          });
        });
      }
    },
    // 显示文本输入对话框
    showTextInputDialog() {
      ElMessageBox.prompt('请输入要转换为语音的文本', '文本转语音', {
        confirmButtonText: '转换',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入文本...'
      }).then(({ value }) => {
        if (value) {
          this.convertTextToSpeech(value)
        }
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 文本转语音
    async convertTextToSpeech(text) {
      try {
        ElMessage.info('正在转换文本为语音...')
        
        const response = await textToSpeech({
          messageId: Date.now().toString(),
          text: text,
          userId: this.userStore.userInfo?.userId,
          agentId: this.agentId
        }, 'blob')

        // 创建音频文件
        const audioBlob = new Blob([response], { type: 'audio/mp3' })
        const audioFile = new File([audioBlob], `speech_${Date.now()}.mp3`, {
          type: 'audio/mp3'
        })

        // 上传音频文件并转换为文本
        await this.convertSpeechToText(audioFile)

      } catch (error) {
        console.error('Text to speech error:', error)
        ElMessage.error('文本转语音失败')
      }
    },

    // 语音转文本
    async convertSpeechToText(audioFile) {
      try {
        ElMessage.info('正在转换语音为文本...')
        
        const formData = new FormData()
        formData.append('file', audioFile)
        formData.append('userId', this.userStore.userInfo?.userId)
        formData.append('agentId', this.agentId)

        // 调用语音转文本接口
        const response = await speechToText(formData)

        if (response.code === 200) {
          // 将转换后的文本添加到输入框
          this.userInput = response.data

          // 添加音频文件到上传列表
          this.uploadedFiles.push({
            fileId: Date.now().toString(), // 临时 ID
            fileType: 'AUDIO',
            extension: 'MP3',
            fileName: audioFile.name,
            raw: audioFile,
            size: audioFile.size
          })

          ElMessage.success('转换完成')
        } else {
          throw new Error(response.msg || '转换失败')
        }
      } catch (error) {
        console.error('Speech to text error:', error)
        ElMessage.error('语音转文本失败')
      }
    },

    // 添加复制消息方法
    copyMessage(content) {
      const tempElement = document.createElement('div')
      tempElement.innerHTML = content
      const textContent = tempElement.textContent || tempElement.innerText
      
      navigator.clipboard.writeText(textContent).then(() => {
        ElMessage({
          message: '复制成功',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        ElMessage({
          message: '复制失败，请手动复制',
          type: 'error',
          duration: 2000
        })
      })
    },

    // 添加点赞方法
    handleLike(message, isLike) {
      if (isLike) {
        message.liked = !message.liked
      }
    },

    // 修改防抖滚动方法
    debouncedScroll() {
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }
      
      this.scrollTimer = setTimeout(() => {
        this.scrollToBottom();
      }, 50); // 减少延迟时间到 50ms
    },

    // 修改重新生成响应方法
    async regenerateResponse(index) {
      // 获取当前 AI 消息的上一条用户消息
      let userMessage = null;
      for (let i = index - 1; i >= 0; i--) {
        if (this.messages[i].sender === 'user') {
          userMessage = this.messages[i].content;
          break;
        }
      }
      
      if (!userMessage) {
        ElMessage.warning('本条为开场白，无法重新生成');
        return;
      }
      
      // 不删除当前 AI 消息，而是直接添加新的 AI 消息
      await this.sendMessage(userMessage);
    },
  },
  mounted() {
    console.log('组件挂载时的属性:', {
      agentId: this.agentId,
      agentName: this.agentName
    })
    this.$nextTick(() => {
      this.scrollToBottom();
    });
  },
  watch: {
    'messages.length'() {
      this.scrollToBottom();
    },
    'messages':{
      deep: true,
      handler() {
        this.scrollToBottom();
      }
    }
  }
}
</script>

<style scoped>
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #e0f0fa; /* 添加背景颜色 */
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); /* 可选：添加一个淡淡的边框 */
}
/* 自定义样式 */
.main-container {
  width: 100%;
  min-height: 100vh;
  background-image: url('@/assets/image/bg.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;  /* 关键属性：使背景图固定 */
  position: relative;
  padding-top: 1rem;
}


/* 处理消息中的换行 */
p {
  white-space: pre-line;
}

.chat-container {
  width: 50%;
  margin-bottom: 235px;
  margin-top: 3rem;
  max-height: calc(100vh - 300px); /* 设置最大高度 */
  overflow-y: auto; /* 添加垂直滚动 */
  scroll-behavior: smooth; /* 添加平滑滚动效果 */
}


.input-container {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50%;
  z-index: 20;
  padding-bottom: 20px;
}

.input-wrapper {
  height: 200px; /* 固定总高度 */
  display: flex;
  flex-direction: column;
}

.file-preview-container {
  max-height: 60px; /* 文件预览区域最大高度 */
  overflow-y: auto;
  margin-bottom: 8px;
}

.input-area {
  flex: 1;
  min-height: 0; /* 重要：允许内容区域收缩 */
}

.input-textarea {
  height: 100%;
  min-height: 60px; /* 输入框最小高度 */
}

.bottom-toolbar {
  margin-top: auto; /* 将工具栏推到底部 */
}

/* 美化滚动条 */
.chat-container::-webkit-scrollbar {
  width: 0; /* Hide the scrollbar */
}

.chat-container {
  scrollbar-width: none; /* For Firefox */
  -ms-overflow-style: none; /* For Internet Explorer and Edge */
}

.chat-container::-webkit-scrollbar-thumb {
  background: transparent; /* Make scrollbar thumb transparent */
}

/* 文件预览项样式 */
.file-preview-item {
  display: inline-flex;
  align-items: center;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  margin: 2px;
}

.file-preview-item:hover {
  background: #e5e7eb;
}

.func-container {
  position: absolute;
  bottom: 0;
  right: 0;
}

/* 调整 details 样式 */
:deep(details) {
  margin-bottom: 4px;
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 4px 8px;
}

:deep(details:empty) {
  display: none;
}

:deep(summary) {
  cursor: pointer;
  user-select: none;
  padding: 2px 0;
  color: #666;
  font-size: 0.9em;
}

/* 添加悬停效果 */
:deep(summary:hover) {
  color: #333;
}

:deep(p) {
  white-space: pre-line;
  transition: all 0.3s ease;
  margin: 0;
}

:deep(details > *:not(summary)) {
  margin-top: 4px;
  padding-left: 8px;
  border-left: 2px solid #e5e7eb;
}

.message-container {
  line-height: 1.5;
}

.message-container :deep(p:first-child) {
  margin-top: 0;
}

.message-container :deep(p:last-child) {
  margin-bottom: 0;
}

/* 确保内容紧贴容器 */
.message-container :deep(*:first-child) {
  margin-top: 0;
}

/* 添加上传按钮样式 */
.upload-button {
  display: inline-block;
  position: relative;
}

.upload-button :deep(.el-upload) {
  display: block;
}

.upload-button :deep(.el-upload-list) {
  display: none;
}

/* 添加文件类型图标样式 */
.file-icon {
  margin-right: 4px;
}

.uploaded-files-preview {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.file-preview-item {
  transition: all 0.3s ease;
}

.file-preview-item:hover {
  background-color: #f3f4f6;
}

.file-preview-item button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.file-preview-item:hover button {
  opacity: 1;
}

/* 修改预览相关样式 */
:deep(.preview-image) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2100;
}

/* 修改预览对话框样式 */
:deep(.el-message-box) {
  position: fixed;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%);
  margin: 0 !important;
  z-index: 2200;
}

:deep(.el-message-box__wrapper) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  z-index: 2150;
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.el-overlay) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2000;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: auto;
}

/* 图片预览器样式 */
:deep(.el-image-viewer__wrapper) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2100;
}

:deep(.el-image-viewer__btn) {
  z-index: 2200;
}

:deep(.el-image-viewer__close) {
  color: #fff;
  font-size: 40px;
  top: 40px;
  right: 40px;
  position: fixed;
  z-index: 2200;
}

:deep(.el-image-viewer__canvas) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

:deep(.el-image-viewer__img) {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

/* 确保其他预览对话框样式正确 */
:deep(.text-preview-dialog),
:deep(.pdf-preview-dialog),
:deep(.video-preview-dialog),
:deep(.audio-preview-dialog) {
  width: 80vw;
  max-height: 80vh;
  margin: 15vh auto;
}

:deep(.el-message-box__content) {
  max-height: calc(70vh - 100px);
  overflow-y: auto;
}

/* 修复滚动问题 */
body.el-popup-parent--hidden {
  padding-right: 0 !important;
}

/* 添加文件预览相关样式 */
:deep(.text-preview-dialog) {
  max-width: 80vw;
  max-height: 80vh;
}

:deep(.text-preview-dialog .el-message-box__content) {
  max-height: 70vh;
  overflow: auto;
  white-space: pre-wrap;
  font-family: monospace;
}

:deep(.video-preview-dialog) {
  max-width: 80vw;
}

:deep(.video-preview-dialog .el-message-box__content) {
  text-align: center;
}

/* 确保图片预览器正确显示 */
:deep(.el-image-viewer__wrapper) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2100;
}

:deep(.el-image-viewer__close) {
  color: #fff;
  font-size: 40px;
  top: 40px;
  right: 40px;
}

/* 添加对话框样式 */
:deep(.el-message-box__input textarea) {
  min-height: 100px;
  margin-top: 10px;
}

/* 添加 markdown 样式 */
:deep(.message-content) {
  line-height: 1.6;
}

:deep(.message-content h1) {
  font-size: 1.5em;
  margin: 0.5em 0;
}

:deep(.message-content h2) {
  font-size: 1.3em;
  margin: 0.5em 0;
}

:deep(.message-content h3) {
  font-size: 1.1em;
  margin: 0.5em 0;
}

:deep(.message-content pre) {
  background: #f4f4f4;
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
}

:deep(.message-content code) {
  background: #f4f4f4;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

:deep(.message-content blockquote) {
  border-left: 4px solid #ddd;
  margin: 0;
  padding-left: 1em;
  color: #666;
}

:deep(.message-content ul),
:deep(.message-content ol) {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

:deep(.message-content p) {
  margin: 0.5em 0;
}

:deep(.message-content table) {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
}

:deep(.message-content th),
:deep(.message-content td) {
  border: 1px solid #ddd;
  padding: 0.5em;
}

:deep(.message-content img) {
  max-width: 100%;
  height: auto;
}

/* 添加按钮过渡效果 */
button {
  transition: all 0.3s ease;
}

button:active {
  transform: scale(0.95);
}

/* 修改输入框滚动条样式 */
.input-textarea::-webkit-scrollbar {
  width: 0; /* Hide the scrollbar */
}

.input-textarea {
  scrollbar-width: none; /* For Firefox */
  -ms-overflow-style: none; /* For Internet Explorer and Edge */
}

.input-textarea::-webkit-scrollbar-thumb {
  background: transparent; /* Make scrollbar thumb transparent */
}

/* 添加文档引用样式 */
.document-reference {
  background: linear-gradient(to right, #f8f9fa, #ffffff);
  border-left: 3px solid #e9ecef;
}

.document-reference:hover {
  background: linear-gradient(to right, #f1f3f5, #f8f9fa);
}
</style>