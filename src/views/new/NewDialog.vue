<template>
  <div>
    <div class="flex flex-col items-center w-full main-container">
      <!-- 头部 -->
      <header class="p-4 text-center">
        <div class="inline-flex gap-2 items-center text-xl font-bold text-txt-primary">
          <span>{{ agentName }}</span>
        </div>
      </header>

      <!-- 聊天容器 -->
      <div class="chat-wrapper">
        <chat-container
          :agent-id="agentId"
          :user-id="userStore.userInfo?.userId"
          :initial-messages="initialMessages"
          :conversation-id="currentConversationId"
          :parent-type="'new'"
          @update:conversation-id="currentConversationId = $event"
          @error="handleError"
          @message-sent="handleMessageSent"
          @message-received="handleMessageReceived"
          ref="chatContainer"
        />
      </div>

      <!-- AI助手图标 -->
      <!-- <div class="absolute bottom-10 right-28">
        <img src="@/assets/image/ai.png" alt="AI助手" class="w-30 h-30" />
      </div> -->
    </div>
  </div>
</template>

<script>
import { useUserStore } from '@/stores/user'
import ChatContainer from '@/components/ChatContainer.vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'NewDialog',
  components: {
    ChatContainer
  },
  props: {
    agentId: {
      type: String,
      required: false,
      default: '9bad24bea86d3d7b2a1eff108ed7e7ec'
    },
    agentName: {
      type: String,
      required: false,
      default: '科宝'
    }
  },
  data() {
    return {
      currentConversationId: '',
      userStore: useUserStore()
    }
  },
  computed: {
    initialMessages() {
      return [
        {
          sender: 'ai',
          content: `Hi, 我是${this.agentName}～
你可以把你的需求告诉我，让我来帮助你吧！`
        }
      ]
    }
  },
  mounted() {
    // 获取路由参数
    const initialMessage = this.$route.query.initialMessage
    let initialFiles = []
    let modes = []

    // 安全解析 JSON 参数
    try {
      initialFiles = this.$route.query.files ? JSON.parse(this.$route.query.files) : []
    } catch (error) {
      console.error('解析文件参数失败:', error)
      initialFiles = []
    }

    try {
      modes = this.$route.query.modes ? JSON.parse(this.$route.query.modes) : []
    } catch (error) {
      console.error('解析模式参数失败:', error)
      modes = []
    }

    let knowledgeList = []
    let knowledgeIds = []
    let documentIds = []

    try {
      knowledgeList = this.$route.query.knowledgeList ? JSON.parse(this.$route.query.knowledgeList) : []
    } catch (error) {
      console.error('解析知识库参数失败:', error)
      knowledgeList = []
    }

    try {
      knowledgeIds = this.$route.query.knowledgeIds ? JSON.parse(this.$route.query.knowledgeIds) : []
    } catch (error) {
      console.error('解析知识库ID参数失败:', error)
      knowledgeIds = []
    }

    try {
      documentIds = this.$route.query.documentIds ? JSON.parse(this.$route.query.documentIds) : []
    } catch (error) {
      console.error('解析文档ID参数失败:', error)
      documentIds = []
    }

    console.log('NewDialog mounted - 接收到的参数:', {
      initialMessage,
      initialFiles,
      modes,
      knowledgeList,
      knowledgeIds,
      documentIds
    })

    // 检查是否应该发送初始消息（避免页面刷新时重复发送）
    const sessionKey = `initialMessage_sent_${this.agentId}_${this.$route.path}`
    const hasAlreadySent = sessionStorage.getItem(sessionKey)

    // 如果有初始消息或文件，且没有发送过，则发送一次消息
    if ((initialMessage || initialFiles.length > 0) && !hasAlreadySent) {
      // 标记已经发送过初始消息
      sessionStorage.setItem(sessionKey, 'true')

      // 使用 setTimeout 确保 ChatContainer 完全初始化
      setTimeout(() => {
        // 如果有文件，设置文件到chatContainer组件
        if (initialFiles.length > 0) {
          this.$refs.chatContainer.uploadedFiles = initialFiles.map(file => ({
            fileId: file.fileId,
            fileType: file.fileType,
            extension: file.extension,
            fileName: file.fileName
          }));
          console.log('设置文件:', this.$refs.chatContainer.uploadedFiles)
        }

        // 如果有模式，设置模式
        if (modes.length > 0) {
          this.$refs.chatContainer.activeModes = modes;
          console.log('设置模式:', this.$refs.chatContainer.activeModes)
        }

        // 如果有知识库，设置知识库
        if (knowledgeList.length > 0) {
          this.$refs.chatContainer.selectedKnowledgeList = knowledgeList;
          console.log('设置知识库:', this.$refs.chatContainer.selectedKnowledgeList)
        }

        // 如果有知识库ID和文档ID，设置它们
        if (knowledgeIds.length > 0 || documentIds.length > 0) {
          this.$refs.chatContainer.selectedKnowledgeIds = knowledgeIds;
          this.$refs.chatContainer.selectedDocumentIds = documentIds;
          console.log('设置知识库IDs:', this.$refs.chatContainer.selectedKnowledgeIds)
          console.log('设置文档IDs:', this.$refs.chatContainer.selectedDocumentIds)
        }

        // 如果有初始消息，直接发送
        if (initialMessage) {
          console.log('发送初始消息:', initialMessage)
          this.$refs.chatContainer.sendMessage(initialMessage)

          // 发送消息后清理URL参数
          this.clearInitialMessageFromUrl()
        }
      }, 500) // 延迟500ms确保ChatContainer完全初始化
    } else if (hasAlreadySent) {
      // 如果已经发送过，直接清理URL参数
      console.log('检测到已发送过初始消息，清理URL参数')
      this.clearInitialMessageFromUrl()
    }
  },
  methods: {
    handleError({ type, message }) {
      ElMessage.error(message)
    },

    handleMessageSent(data) {
      console.log('Message sent:', data)
      // 可以在这里添加额外的处理逻辑，如分析、记录等
    },

    handleMessageReceived(data) {
      console.log('Message received:', data)
      // 可以在这里添加额外的处理逻辑，如通知、声音提醒等
    },

    // 清理URL中的初始消息参数
    clearInitialMessageFromUrl() {
      try {
        const currentQuery = { ...this.$route.query }
        let hasChanges = false

        // 删除初始消息相关的参数
        const paramsToRemove = ['initialMessage', 'files', 'modes', 'knowledgeList', 'knowledgeIds', 'documentIds']
        paramsToRemove.forEach(param => {
          if (currentQuery[param]) {
            delete currentQuery[param]
            hasChanges = true
          }
        })

        // 如果有参数被删除，跳转到基础的 /new-dialog 路径
        if (hasChanges) {
          this.$router.replace({
            path: '/new-dialog',
            query: {}
          }).catch(err => {
            // 忽略导航重复错误
            if (err.name !== 'NavigationDuplicated') {
              console.error('清除URL参数时出错:', err)
            }
          })
          console.log('已清理URL，跳转到基础路径: /new-dialog')
        }
      } catch (error) {
        console.error('清除URL参数时出错:', error)
      }
    }
  }
}
</script>

<style scoped>
/* 保留原有样式 */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #e0f0fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.main-container {
  width: 100%;
  min-height: 100vh;
  background-image: url('@/assets/image/bg.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  padding-top: 1rem;
}

.chat-wrapper {
  width: 53.75%;
  margin-top: 3rem;
  height: calc(100vh - 300px);
  margin-bottom: 235px;
}
</style>