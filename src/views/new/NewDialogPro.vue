<template>
  <div class="new-dialog-container">
    <!-- 头部 -->
    <header class="header">
      <div class="header-content">
        <!-- 左侧：智能体名称 -->
        <div class="header-left" :class="{ 'full-width': isWorkspaceCollapsed }" :style="{ width: isWorkspaceCollapsed ? '100%' : leftPanelWidth + '%' }">
          <div class="inline-flex gap-2 items-center text-xl font-bold text-txt-primary">
            <span>{{ agentName }}</span>
          </div>
        </div>
        
        <!-- 右侧：工作空间标题 -->
        <div class="header-right" v-show="!isWorkspaceCollapsed" :style="{ width: rightPanelWidth + '%' }">
          <div class="inline-flex gap-2 items-center text-xl font-bold text-txt-primary">
            <span>工作空间</span>
          </div>
        </div>
      </div>
    </header>

    <!-- 主体内容区域 -->
    <div class="main-content" :class="{ 'workspace-collapsed': isWorkspaceCollapsed }">
      <!-- 左侧对话区域 -->
      <chat-section
        :agent-id="agentId"
        :agent-name="agentName"
        :user-id="userStore.userInfo?.userId"
        :initial-messages="initialMessages"
        :conversation-id="currentConversationId"
        @update:conversation-id="currentConversationId = $event"
        @error="handleError"
        @message-sent="handleMessageSent"
        @message-received="handleMessageReceived"
        @node-execution="handleNodeExecution"
        @process-update="handleProcessUpdate"
        ref="chatSection"
        :class="{ 'chat-full-width': isWorkspaceCollapsed }"
        :style="{ 
          width: isWorkspaceCollapsed ? '65%' : leftPanelWidth + '%', 
          maxWidth: isWorkspaceCollapsed ? '1200px' : 'none', 
          minWidth: isWorkspaceCollapsed ? '600px' : 'auto',
          flexShrink: 0,
          flexGrow: 0
        }"
      />

      <!-- 可拖动的分割条 -->
      <div 
        class="resizable-divider" 
        v-show="!isWorkspaceCollapsed"
        @mousedown="startDrag"
        @dblclick="resetPanelWidths"
        :class="{ 'dragging': isDragging }"
      >
        <div class="divider-handle">
          <div class="divider-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
        </div>
        <!-- 拖动提示 -->
        <div class="divider-tooltip drag-tip" v-if="isDragging">
          {{ Math.round(leftPanelWidth) }}% | {{ Math.round(rightPanelWidth) }}%
        </div>
        
        <!-- 悬停提示 -->
        <div class="divider-tooltip hover-tip" v-if="!isDragging">
          拖动调整宽度 | 双击重置
        </div>
      </div>

      <!-- 右侧工作空间 -->
      <workspace-section
        v-show="!isWorkspaceCollapsed"
        :current-node="currentNode"
        :execution-steps="executionSteps"
        :current-ai-message="currentAiMessage"
        ref="workspaceSection"
        :style="{ 
          width: rightPanelWidth + '%',
          flexShrink: 0,
          flexGrow: 0
        }"
      />

      <!-- 工作空间切换按钮 -->
      <div class="workspace-toggle-button" @click="toggleWorkspace">
        <div class="toggle-content">
          <svg v-if="!isWorkspaceCollapsed" class="toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
          <svg v-else class="toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="15,18 9,12 15,6"></polyline>
          </svg>
          
          <!-- 文字提示 -->
          <div class="toggle-tooltip">
            {{ isWorkspaceCollapsed ? '展开工作空间' : '收起工作空间' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 拖动时的遮罩层 -->
    <div v-if="isDragging" class="drag-overlay"></div>
  </div>
</template>

<script>
import { useUserStore } from '@/stores/user'
import ChatSection from '@/components/ChatSection.vue'
import WorkspaceSection from '@/components/WorkspaceSection.vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'NewDialog',
  components: {
    ChatSection,
    WorkspaceSection
  },
  props: {
    agentId: {
      type: String,
      required: false,
      default: 'c6ca8011c454f4245a961b2ba9430d7c'
    },
    agentName: {
      type: String,
      required: false,
      default: '科宝'
    }
  },
  data() {
    return {
      currentConversationId: '',
      userStore: useUserStore(),
      // 工作空间相关数据
      currentNode: null,
      executionSteps: [],
      currentAiMessage: null, // 当前AI消息内容
      syncTimer: null, // 同步定时器
      // 工作空间展开状态 - 默认展开
      isWorkspaceCollapsed: false,
      // 拖拽相关数据
      isDragging: false,
      startX: 0,
      startLeftWidth: 50,
      // 面板宽度百分比
      leftPanelWidth: 50,  // 左侧面板宽度（百分比）
      rightPanelWidth: 50, // 右侧面板宽度（百分比）
      // 宽度限制
      minLeftWidth: 10,  // 最小左侧宽度 30%
      maxLeftWidth: 90,  // 最大左侧宽度 70%
    }
  },
  computed: {
    initialMessages() {
      return [
        {
          sender: 'ai',
          content: `Hi, 我是${this.agentName}～你可以把你的需求告诉我，让我来帮助你吧！`
        }
      ]
    }
  },
  mounted() {
    // 定期同步AI消息到工作空间
    this.syncTimer = setInterval(() => {
      this.syncAiMessageToWorkspace()
    }, 500);

    // 获取路由参数
    const initialMessage = this.$route.query.initialMessage
    const specialAgent = this.$route.query.specialAgent // 新增：获取特殊智能体标识
    let initialFiles = []
    let modes = []

    // 安全解析 JSON 参数
    try {
      initialFiles = this.$route.query.files ? JSON.parse(this.$route.query.files) : []
    } catch (error) {
      console.error('解析文件参数失败:', error)
      initialFiles = []
    }

    try {
      modes = this.$route.query.modes ? JSON.parse(this.$route.query.modes) : []
    } catch (error) {
      console.error('解析模式参数失败:', error)
      modes = []
    }

    let knowledgeList = []
    let knowledgeIds = []
    let documentIds = []

    try {
      knowledgeList = this.$route.query.knowledgeList ? JSON.parse(this.$route.query.knowledgeList) : []
    } catch (error) {
      console.error('解析知识库参数失败:', error)
      knowledgeList = []
    }

    try {
      knowledgeIds = this.$route.query.knowledgeIds ? JSON.parse(this.$route.query.knowledgeIds) : []
    } catch (error) {
      console.error('解析知识库ID参数失败:', error)
      knowledgeIds = []
    }

    try {
      documentIds = this.$route.query.documentIds ? JSON.parse(this.$route.query.documentIds) : []
    } catch (error) {
      console.error('解析文档ID参数失败:', error)
      documentIds = []
    }

    console.log('NewDialog mounted - 接收到的参数:', {
      initialMessage,
      initialFiles,
      modes,
      knowledgeList,
      knowledgeIds,
      documentIds,
      specialAgent // 新增日志
    })

    // 检查是否应该发送初始消息（避免页面刷新时重复发送）
    const sessionKey = `initialMessage_sent_${this.agentId}_${this.$route.path}`
    const hasAlreadySent = sessionStorage.getItem(sessionKey)

    // 如果有初始消息或文件，且没有发送过，则发送一次消息
    if ((initialMessage || initialFiles.length > 0) && !hasAlreadySent) {
      // 标记已经发送过初始消息
      sessionStorage.setItem(sessionKey, 'true')

      // 使用 setTimeout 确保 ChatContainer 完全初始化
      setTimeout(() => {
        // 如果有文件，设置文件到chatSection组件
        if (initialFiles.length > 0) {
          const fileData = initialFiles.map(file => ({
            fileId: file.fileId,
            fileType: file.fileType,
            extension: file.extension,
            fileName: file.fileName
          }));
          this.$refs.chatSection.setUploadedFiles(fileData);
          console.log('设置文件:', fileData)
        }

        // 如果有模式，设置模式
        if (modes.length > 0) {
          this.$refs.chatSection.setActiveModes(modes);
          console.log('设置模式:', modes)
        }

        // 如果有知识库，设置知识库
        if (knowledgeList.length > 0) {
          this.$refs.chatSection.setSelectedKnowledgeList(knowledgeList);
          console.log('设置知识库:', knowledgeList)
        }

        // 如果有知识库ID和文档ID，设置它们
        if (knowledgeIds.length > 0 || documentIds.length > 0) {
          this.$refs.chatSection.setSelectedKnowledgeIds(knowledgeIds);
          this.$refs.chatSection.setSelectedDocumentIds(documentIds);
          console.log('设置知识库IDs:', knowledgeIds)
          console.log('设置文档IDs:', documentIds)
        }

        // 如果有初始消息，直接发送
        if (initialMessage) {
          console.log('发送初始消息:', initialMessage)
          this.$refs.chatSection.sendMessage(initialMessage)

          // 发送消息后清理URL参数
          this.clearInitialMessageFromUrl()
        }
      }, 500) // 延迟500ms确保ChatContainer完全初始化
    } else if (hasAlreadySent) {
      // 如果已经发送过，直接清理URL参数
      console.log('检测到已发送过初始消息，清理URL参数')
      this.clearInitialMessageFromUrl()
    }

    // 初始化工作空间数据
    this.initializeWorkspace()
    
    // 如果是特殊智能体，传递标识到ChatSection
    if (specialAgent) {
      setTimeout(() => {
        if (this.$refs.chatSection && this.$refs.chatSection.$refs.chatContainer) {
          this.$refs.chatSection.$refs.chatContainer.specialAgent = specialAgent
          console.log('设置特殊智能体标识:', specialAgent)
        }
      }, 600) // 稍微延迟确保组件完全初始化
    }

    // 添加窗口大小变化监听器
    window.addEventListener('resize', this.handleWindowResize)
  },
  methods: {
    // 切换工作空间展开/收起状态
    toggleWorkspace() {
      this.isWorkspaceCollapsed = !this.isWorkspaceCollapsed
    },



    // 开始拖拽
    startDrag(event) {
      console.log('startDrag 被调用了！', event)
      
      this.isDragging = true
      this.startX = event.clientX
      this.startLeftWidth = this.leftPanelWidth
      
      // 阻止文本选择和默认行为
      event.preventDefault()
      event.stopPropagation()
      
      document.body.style.userSelect = 'none'
      document.body.style.cursor = 'col-resize'
      
      // 动态绑定事件监听器
      document.addEventListener('mousemove', this.onDrag, { passive: false })
      document.addEventListener('mouseup', this.stopDrag, { once: true })
      
      console.log('开始拖拽:', { 
        startX: this.startX, 
        startLeftWidth: this.startLeftWidth,
        isDragging: this.isDragging 
      })
    },

    // 拖拽过程中
    onDrag(event) {
      if (!this.isDragging) {
        console.log('onDrag: 不在拖拽状态，退出')
        return
      }
      
      event.preventDefault()
      event.stopPropagation()
      
      // 计算拖拽距离
      const deltaX = event.clientX - this.startX
      
      // 获取容器宽度（减去固定的边距）
      const containerWidth = window.innerWidth - 112 // 7rem = 112px 左侧边栏宽度
      
      // 计算新的宽度百分比
      const deltaPercent = (deltaX / containerWidth) * 100
      let newLeftWidth = this.startLeftWidth + deltaPercent
      
      // 限制宽度范围
      newLeftWidth = Math.max(this.minLeftWidth, Math.min(this.maxLeftWidth, newLeftWidth))
      
      // 更新宽度
      this.leftPanelWidth = newLeftWidth
      this.rightPanelWidth = 100 - newLeftWidth
      
      console.log('拖拽中:', { 
        deltaX, 
        deltaPercent, 
        newLeftWidth, 
        leftWidth: this.leftPanelWidth, 
        rightWidth: this.rightPanelWidth,
        containerWidth,
        startX: this.startX,
        currentX: event.clientX
      })
    },

    // 停止拖拽
    stopDrag() {
      if (!this.isDragging) return
      
      this.isDragging = false
      document.body.style.userSelect = ''
      document.body.style.cursor = ''
      
      // 移除事件监听器
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
      
      console.log('停止拖拽:', { 
        finalLeftWidth: this.leftPanelWidth, 
        finalRightWidth: this.rightPanelWidth 
      })
      
      // 强制Vue重新渲染，确保样式更新
      this.$forceUpdate()
      
      // 延迟执行，确保DOM更新后再保存
      this.$nextTick(() => {
        // 保存宽度设置到本地存储
        localStorage.setItem('workspacePanelWidths', JSON.stringify({
          leftPanelWidth: this.leftPanelWidth,
          rightPanelWidth: this.rightPanelWidth
        }))
        
        console.log('✅ 面板宽度已保存:', {
          left: this.leftPanelWidth + '%',
          right: this.rightPanelWidth + '%'
        })
      })
    },

    // 从本地存储恢复宽度设置
    restorePanelWidths() {
      try {
        const savedWidths = localStorage.getItem('workspacePanelWidths')
        if (savedWidths) {
          const { leftPanelWidth, rightPanelWidth } = JSON.parse(savedWidths)
          // 确保宽度在有效范围内
          this.leftPanelWidth = Math.max(this.minLeftWidth, Math.min(this.maxLeftWidth, leftPanelWidth))
          this.rightPanelWidth = 100 - this.leftPanelWidth
        }
      } catch (error) {
        console.error('恢复面板宽度失败:', error)
      }
    },

    // 重置面板宽度为默认值（双击分割条时调用）
    resetPanelWidths() {
      this.leftPanelWidth = 50
      this.rightPanelWidth = 50
      
      // 保存到本地存储
      localStorage.setItem('workspacePanelWidths', JSON.stringify({
        leftPanelWidth: this.leftPanelWidth,
        rightPanelWidth: this.rightPanelWidth
      }))
      
      // 提供视觉反馈
      if (this.$refs.workspaceSection) {
        this.$refs.workspaceSection.$el.style.transition = 'width 0.3s ease'
        setTimeout(() => {
          if (this.$refs.workspaceSection) {
            this.$refs.workspaceSection.$el.style.transition = ''
          }
        }, 300)
      }
      
      console.log('重置面板宽度为默认值')
    },

    // 处理窗口大小变化
    handleWindowResize() {
      // 在窗口大小变化时，确保面板宽度仍然有效
      if (this.leftPanelWidth < this.minLeftWidth || this.leftPanelWidth > this.maxLeftWidth) {
        this.resetPanelWidths()
      }
    },

    handleError({ type, message }) {
      ElMessage.error(message)
      this.addExecutionStep('错误', 'error', message)
    },
    
    handleMessageSent(data) {
      console.log('Message sent:', data)
      this.addExecutionStep('消息发送', 'completed', data.content)
      
      // 同步到工作空间
      this.syncAiMessageToWorkspace()
    },
    
    handleMessageReceived(data) {
      console.log('Message received:', data)
      this.addExecutionStep('消息接收', 'completed', data.content)
      
      // 同步AI消息到工作空间
      this.syncAiMessageToWorkspace(data)

      // 如果工作空间是收起状态且有新的AI消息，可以考虑自动展开
      // （可选功能，根据实际需求决定是否启用）
      // if (this.isWorkspaceCollapsed && data && data.workflowNodes && data.workflowNodes.length > 0) {
      //   this.isWorkspaceCollapsed = false
      // }
    },

    // 新增的事件处理方法
    handleNodeExecution(nodeData) {
      console.log('Node execution:', nodeData)
      this.currentNode = {
        name: nodeData.name || '未知节点',
        type: nodeData.type || 'default',
        description: nodeData.description || '节点执行中...',
        status: nodeData.status || 'running'
      }
      this.addExecutionStep(nodeData.name || '节点执行', nodeData.status || 'running')
      
      // 同步到工作空间
      this.syncAiMessageToWorkspace()
    },

    handleProcessUpdate(processData) {
      console.log('Process update:', processData)
      
      if (processData.currentStep) {
        this.addExecutionStep(
          processData.currentStep.name,
          processData.currentStep.status,
          processData.currentStep.description
        )
      }
      
      // 同步到工作空间
      this.syncAiMessageToWorkspace()
    },

    addExecutionStep(title, status = 'running', description = '') {
      const step = {
        title,
        status,
        description,
        timestamp: new Date().toLocaleTimeString()
      }
      
      // 避免重复添加相同的步骤
      const existingIndex = this.executionSteps.findIndex(s => s.title === title)
      if (existingIndex !== -1) {
        this.executionSteps[existingIndex] = step
      } else {
        this.executionSteps.push(step)
      }
    },

    getStatusText(status) {
      const statusMap = {
        pending: '等待中',
        running: '运行中',
        completed: '已完成',
        error: '错误'
      }
      return statusMap[status] || status
    },

    initializeWorkspace() {
      // 恢复保存的面板宽度
      this.restorePanelWidths()
      
      // 初始化工作空间显示
      this.addExecutionStep('系统初始化', 'completed', '对话系统已准备就绪')
    },

    downloadResults() {
      try {
        // 生成执行步骤的文本内容
        const content = this.executionSteps.map(step => 
          `${step.timestamp} - ${step.title} (${this.getStatusText(step.status)})\n${step.description ? step.description + '\n' : ''}`
        ).join('\n')
        
        // 创建下载链接
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `执行结果_${new Date().toLocaleString().replace(/[/:]/g, '-')}.txt`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        ElMessage.success('执行结果已下载')
      } catch (error) {
        console.error('下载失败:', error)
        ElMessage.error('下载失败')
      }
    },

    copyResults() {
      try {
        // 生成执行步骤的文本内容
        const content = this.executionSteps.map(step => 
          `${step.timestamp} - ${step.title} (${this.getStatusText(step.status)})\n${step.description ? step.description + '\n' : ''}`
        ).join('\n')
        
        // 复制到剪贴板
        navigator.clipboard.writeText(content).then(() => {
          ElMessage.success('执行结果已复制到剪贴板')
        }).catch(err => {
          console.error('复制失败:', err)
          // 降级处理：使用传统方法
          const textarea = document.createElement('textarea')
          textarea.value = content
          document.body.appendChild(textarea)
          textarea.select()
          document.execCommand('copy')
          document.body.removeChild(textarea)
          ElMessage.success('执行结果已复制到剪贴板')
        })
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    },

    // 同步AI消息到工作空间
    syncAiMessageToWorkspace(messageData) {
      // 从ChatSection获取最新的AI消息
      if (this.$refs.chatSection) {
        const messages = this.$refs.chatSection.getMessages()
        const lastAiMessage = messages.slice().reverse().find(msg => msg.sender === 'ai')
        if (lastAiMessage) {
          this.currentAiMessage = lastAiMessage
        }
      }
    },

    // 清理URL中的初始消息参数
    clearInitialMessageFromUrl() {
      try {
        const currentQuery = { ...this.$route.query }
        let hasChanges = false

        // 删除初始消息相关的参数
        const paramsToRemove = ['initialMessage', 'files', 'modes', 'knowledgeList', 'knowledgeIds', 'documentIds']
        paramsToRemove.forEach(param => {
          if (currentQuery[param]) {
            delete currentQuery[param]
            hasChanges = true
          }
        })

        // 如果有参数被删除，跳转到基础的 /new-dialog 路径
        if (hasChanges) {
          this.$router.replace({
            path: '/new-dialog',
            query: {}
          }).catch(err => {
            // 忽略导航重复错误
            if (err.name !== 'NavigationDuplicated') {
              console.error('清除URL参数时出错:', err)
            }
          })
          console.log('已清理URL，跳转到基础路径: /new-dialog')
        }
      } catch (error) {
        console.error('清除URL参数时出错:', error)
      }
    }
  },

  beforeUnmount() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
    }
    
    // 移除事件监听器
    this.stopDrag() // 确保拖动状态被清理
    window.removeEventListener('resize', this.handleWindowResize)
  }
}
</script>

<style scoped>
.new-dialog-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url('@/assets/image/bg.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  margin-left: 7rem; /* 为侧边栏留出空间 */
  width: calc(100% - 7rem); /* 调整宽度以适应侧边栏 */
}

.header {
  position: fixed;
  top: 0;
  left: 7rem; /* 为侧边栏留出空间 */
  width: calc(100% - 7rem); /* 调整宽度以适应侧边栏 */
  z-index: 10;
  background-color: #e0f0fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem;
}

.header-content {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: none;
}

.header-left {
  flex: 1;
  display: flex;
  justify-content: center;
  padding-right: 0.5rem;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  transition: border-right 0.3s ease;
}

.header-left.full-width {
  border-right: none;
  padding-right: 0;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
  padding-left: 0.5rem;
}

.main-content {
  display: flex;
  flex: 1;
  margin-top: 4rem;
  height: calc(100vh - 4rem);
  overflow: hidden;
  position: relative;
  /* 移除 justify-content: center，让子元素按设定宽度显示 */
}

/* 当工作空间收起时，ChatSection居中显示 */
.main-content.workspace-collapsed {
  justify-content: center;
}

/* 聊天区域占满全宽时的样式 */
.chat-full-width {
  flex: none !important;
  width: 65% !important;
  max-width: 1200px !important;
  min-width: 600px !important;
}

/* 可拖动的分割条 */
.resizable-divider {
  position: relative;
  width: 12px;
  height: 100%;
  cursor: col-resize;
  z-index: 100;
  user-select: none;
  touch-action: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  pointer-events: auto;
  background: transparent;
}

.divider-handle {
  width: 6px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.divider-handle::before {
  content: '';
  position: absolute;
  left: -3px;
  right: -3px;
  top: -8px;
  bottom: -8px;
  border-radius: 8px;
  background: transparent;
}

.divider-dots {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 3px;
  width: 100%;
  height: 100%;
}

.dot {
  width: 3px;
  height: 3px;
  background-color: #64748b;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.resizable-divider:hover .divider-handle {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #94a3b8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

.resizable-divider:hover .dot {
  background-color: #475569;
}

.resizable-divider.dragging .divider-handle {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-color: #1d4ed8;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
  transform: scale(1.2);
}

.resizable-divider.dragging .dot {
  background-color: white;
}

/* 分割条提示 */
.divider-tooltip {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 200;
  pointer-events: none;
  backdrop-filter: blur(8px);
  animation: fadeInUp 0.2s ease-out;
}

/* 拖动时的百分比提示 */
.divider-tooltip.drag-tip {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.divider-tooltip.drag-tip::after {
  border-top-color: #2563eb;
}

/* 悬停提示 */
.divider-tooltip.hover-tip {
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  transition-delay: 0.5s;
}

.resizable-divider:hover .divider-tooltip.hover-tip {
  opacity: 1;
  visibility: visible;
}

.divider-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #374151;
}

/* 分割条的活跃区域增大，便于拖拽 */
.resizable-divider::before {
  content: '';
  position: absolute;
  left: -8px;
  right: -8px;
  top: 0;
  bottom: 0;
  cursor: col-resize;
  z-index: 1;
  background: transparent;
  pointer-events: auto;
}

/* 改善拖拽时的视觉反馈 */
.resizable-divider.dragging {
  z-index: 1000;
}

.resizable-divider.dragging::before {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 4px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}



/* 工作空间切换按钮 */
.workspace-toggle-button {
  position: fixed;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  cursor: pointer;
}

.toggle-content {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
}

.toggle-content:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #cbd5e1;
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.toggle-icon {
  width: 18px;
  height: 18px;
  color: #64748b;
  transition: color 0.3s ease;
}

.toggle-content:hover .toggle-icon {
  color: #334155;
}

/* 文字提示样式 */
.toggle-tooltip {
  position: absolute;
  right: 55px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  backdrop-filter: blur(8px);
}

/* 添加小箭头 */
.toggle-tooltip::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 100%;
  transform: translateY(-50%);
  border: 5px solid transparent;
  border-left-color: #374151;
}

/* 悬停时显示提示 */
.workspace-toggle-button:hover .toggle-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(-8px);
}

/* 拖动时的遮罩层 */
.drag-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(59, 130, 246, 0.05);
  backdrop-filter: blur(2px);
  z-index: 999;
  cursor: col-resize;
  pointer-events: all;
  user-select: none;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .new-dialog-container {
    margin-left: 0; /* 移动端移除左边距 */
    width: 100%; /* 移动端使用全宽 */
  }
  
  .header {
    left: 0; /* 移动端头部从左边开始 */
    width: 100%; /* 移动端头部使用全宽 */
  }
  
  .header-content {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .header-left,
  .header-right {
    padding: 0;
    justify-content: center;
  }
  
  .header-left {
    border-right: none; /* 移动端移除边框 */
  }
  
  .header-left .inline-flex,
  .header-right .inline-flex {
    font-size: 1rem; /* 移动端字体稍小 */
  }
  
  .main-content {
    flex-direction: column;
    justify-content: flex-start;
  }
  
  .chat-full-width {
    width: 95% !important;
    min-width: auto !important;
    max-width: none !important;
  }
  
  .resizable-divider {
    width: 100%;
    height: 8px;
    cursor: row-resize;
    flex-direction: row;
  }
  
  .divider-handle {
    width: 60px;
    height: 4px;
  }
  
  .divider-dots {
    flex-direction: row;
    gap: 2px;
  }
  
  .dot {
    width: 2px;
    height: 2px;
  }
  
  .workspace-toggle-button {
    bottom: 20px;
    top: auto;
    right: 20px;
    transform: none;
  }
  
  .toggle-content {
    width: 50px;
    height: 50px;
  }
  
  .toggle-icon {
    width: 20px;
    height: 20px;
  }
  
  /* 移动端tooltip样式调整 */
  .toggle-tooltip {
    right: 65px;
    font-size: 11px;
    padding: 4px 8px;
  }
  
  .toggle-tooltip::after {
    border-width: 4px;
  }
}
</style> 