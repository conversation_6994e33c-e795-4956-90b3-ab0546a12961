<template>
  <div class="new-dialog-container">
    <!-- 头部 -->
    <header class="header">
      <div class="header-content">
        <!-- 左侧：智能体名称 -->
        <div class="header-left" :class="{ 'full-width': isWorkspaceCollapsed }" :style="{ width: isWorkspaceCollapsed ? '100%' : leftPanelWidth + '%' }">
          <div class="inline-flex gap-2 items-center text-xl font-bold text-txt-primary">
            <span>{{ agentName }}</span>
          </div>
        </div>

        <!-- 右侧：工作空间标题 -->
        <div class="header-right" v-show="!isWorkspaceCollapsed" :style="{ width: rightPanelWidth + '%' }">
          <div class="inline-flex gap-2 items-center text-xl font-bold text-txt-primary">
            <span>工作空间</span>
          </div>
        </div>
      </div>
    </header>

    <!-- 主体内容区域 -->
    <div class="main-content" :class="{ 'workspace-collapsed': isWorkspaceCollapsed }">
      <!-- 左侧对话区域 -->
      <announcement-chat-section
        :agent-id="agentId"
        :agent-name="agentName"
        :user-id="userStore.userInfo?.userId"
        :initial-messages="initialMessages"
        :conversation-id="currentConversationId"
        :workflow-id="workflowId"
        @update:conversation-id="currentConversationId = $event"
        @error="handleError"
        @message-sent="handleMessageSent"
        @message-received="handleMessageReceived"
        @node-execution="handleNodeExecution"
        @process-update="handleProcessUpdate"
        ref="chatSection"
        :class="{ 'chat-full-width': isWorkspaceCollapsed }"
        :style="{
          width: isWorkspaceCollapsed ? '65%' : leftPanelWidth + '%',
          maxWidth: isWorkspaceCollapsed ? '1200px' : 'none',
          minWidth: isWorkspaceCollapsed ? '600px' : 'auto',
          flexShrink: 0,
          flexGrow: 0
        }"
      />

      <!-- 可拖动的分割条 -->
      <div
        class="resizable-divider"
        v-show="!isWorkspaceCollapsed"
        @mousedown="startDrag"
        @dblclick="resetPanelWidths"
        :class="{ 'dragging': isDragging }"
      >
        <div class="divider-handle">
          <div class="divider-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
        </div>
        <!-- 拖动提示 -->
        <div class="divider-tooltip drag-tip" v-if="isDragging">
          {{ Math.round(leftPanelWidth) }}% | {{ Math.round(rightPanelWidth) }}%
        </div>

        <!-- 悬停提示 -->
        <div class="divider-tooltip hover-tip" v-if="!isDragging">
          拖动调整宽度 | 双击重置
        </div>
      </div>

      <!-- 右侧工作空间 -->
      <workspace-section
        v-show="!isWorkspaceCollapsed"
        :current-node="currentNode"
        :execution-steps="executionSteps"
        :current-ai-message="currentAiMessage"
        ref="workspaceSection"
        :style="{
          width: rightPanelWidth + '%',
          flexShrink: 0,
          flexGrow: 0
        }"
      />

      <!-- 工作空间切换按钮 -->
      <div class="workspace-toggle-button" @click="toggleWorkspace">
        <div class="toggle-content">
          <svg v-if="!isWorkspaceCollapsed" class="toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
          <svg v-else class="toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="15,18 9,12 15,6"></polyline>
          </svg>

          <div class="toggle-text">
            {{ isWorkspaceCollapsed ? '展开' : '收起' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useUserStore } from '@/stores/user'
import AnnouncementChatSection from '@/components/AnnouncementChatSection.vue'
import WorkspaceSection from '@/components/OfficialWorkspaceSection.vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'AnnouncementNewDialog',
  components: {
    AnnouncementChatSection,
    WorkspaceSection
  },
  props: {
    agentId: {
      type: String,
      required: true
    },
    agentName: {
      type: String,
      default: '公文公告'
    }
  },
  data() {
    return {
      currentConversationId: '',
      userStore: useUserStore(),
      // 工作空间相关数据
      currentNode: null,
      executionSteps: [],
      currentAiMessage: null, // 当前AI消息内容
      syncTimer: null, // 同步定时器
      // 工作空间展开状态 - 默认展开
      isWorkspaceCollapsed: false,
      // 拖拽相关数据
      isDragging: false,
      startX: 0,
      startLeftWidth: 50,
      // 面板宽度百分比
      leftPanelWidth: 50,  // 左侧面板宽度（百分比）
      rightPanelWidth: 50, // 右侧面板宽度（百分比）
      // 宽度限制
      minLeftWidth: 10,  // 最小左侧宽度 30%
      maxLeftWidth: 90,  // 最大左侧宽度 70%
      initialMessages: [],
      workflowId: '1947848702138769410' // 公文公告工作流ID
    }
  },
  async mounted() {
    console.log('AnnouncementNewDialog mounted with agentId:', this.agentId, 'agentName:', this.agentName)

    // 从URL参数中获取工作流ID
    if (this.$route.query.workflowId) {
      this.workflowId = this.$route.query.workflowId
    }

    // 解析初始消息参数
    let initialMessage = this.$route.query.initialMessage || ''

    // 解析文件参数
    let initialFiles = []
    try {
      initialFiles = this.$route.query.files ? JSON.parse(this.$route.query.files) : []
    } catch (error) {
      console.error('解析文件参数失败:', error)
      initialFiles = []
    }

    // 解析模式参数
    let modes = []
    try {
      modes = this.$route.query.modes ? JSON.parse(this.$route.query.modes) : []
    } catch (error) {
      console.error('解析模式参数失败:', error)
      modes = []
    }

    // 解析知识库列表参数
    let knowledgeList = []
    try {
      knowledgeList = this.$route.query.knowledgeList ? JSON.parse(this.$route.query.knowledgeList) : []
    } catch (error) {
      console.error('解析知识库列表参数失败:', error)
      knowledgeList = []
    }

    // 解析知识库ID参数
    let knowledgeIds = []
    try {
      knowledgeIds = this.$route.query.knowledgeIds ? JSON.parse(this.$route.query.knowledgeIds) : []
    } catch (error) {
      console.error('解析知识库ID参数失败:', error)
      knowledgeIds = []
    }

    // 解析文档ID参数
    let documentIds = []
    try {
      documentIds = this.$route.query.documentIds ? JSON.parse(this.$route.query.documentIds) : []
    } catch (error) {
      console.error('解析文档ID参数失败:', error)
      documentIds = []
    }

    // 解析表单数据参数（来自 DocumentOffice 页面的跳转）
    let formData = null
    try {
      formData = this.$route.query.formData ? JSON.parse(this.$route.query.formData) : null
    } catch (error) {
      console.error('解析表单数据参数失败:', error)
      formData = null
    }

    console.log('AnnouncementNewDialog mounted - 接收到的参数:', {
      initialMessage,
      initialFiles,
      modes,
      knowledgeList,
      knowledgeIds,
      documentIds,
      workflowId: this.workflowId,
      formData
    })

    // 检查是否应该发送初始消息（避免页面刷新时重复发送）
    const sessionKey = `initialMessage_sent_${this.agentId}_${this.$route.path}`
    const hasAlreadySent = sessionStorage.getItem(sessionKey)

    // 如果有初始消息或文件，且没有发送过，则发送一次消息
    if ((initialMessage || initialFiles.length > 0) && !hasAlreadySent) {
      // 标记已经发送过初始消息
      sessionStorage.setItem(sessionKey, 'true')

      // 等待组件完全加载
      await this.$nextTick()

      // 再等待一个tick确保子组件也完全加载
      setTimeout(async () => {
        if (this.$refs.chatSection) {
          // 设置上传的文件
          if (initialFiles.length > 0) {
            this.$refs.chatSection.setUploadedFiles(initialFiles)
            console.log('设置初始文件:', initialFiles)
          }

          // 设置激活的模式
          if (modes.length > 0) {
            this.$refs.chatSection.setActiveModes(modes)
            console.log('设置激活模式:', modes)
          }

          // 设置选择的知识库列表
          if (knowledgeList.length > 0) {
            this.$refs.chatSection.setSelectedKnowledgeList(knowledgeList)
            console.log('设置知识库列表:', knowledgeList)
          }

          // 如果有知识库ID和文档ID，设置它们
          if (knowledgeIds.length > 0 || documentIds.length > 0) {
            this.$refs.chatSection.setSelectedKnowledgeIds(knowledgeIds)
            this.$refs.chatSection.setSelectedDocumentIds(documentIds)
            console.log('设置知识库IDs:', knowledgeIds)
            console.log('设置文档IDs:', documentIds)
          }

          // 如果有初始消息，发送消息
          if (initialMessage) {
            console.log('发送初始消息:', initialMessage)
            
            // 检查是否有表单数据（来自 DocumentOffice 页面）
            if (formData) {
              console.log('检测到表单数据，直接调用公文公告发送方法:', formData)
              // 直接调用公文公告发送方法，避免消息内容解析
              this.$refs.chatSection.sendAnnouncementMessageWithFormData(formData)
            } else {
              // 普通消息发送
              this.$refs.chatSection.sendMessage(initialMessage)
            }

            // 发送消息后清理URL参数
            this.clearInitialMessageFromUrl()
          }
        } else {
          console.error('chatSection ref 不存在')
        }
      }, 100)
    }

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleWindowResize)
  },
  methods: {
    toggleWorkspace() {
      this.isWorkspaceCollapsed = !this.isWorkspaceCollapsed
      if (!this.isWorkspaceCollapsed) {
        this.leftPanelWidth = 50
        this.rightPanelWidth = 50
      }
    },
    handleBack() {
      this.$router.push('/document-office')
    },
    handleError(error) {
      console.error('对话错误:', error)
    },
    handleMessageSent(message) {
      console.log('消息已发送:', message)
    },
    handleMessageReceived(message) {
      console.log('🔄 收到消息:', message)

      // 确保 currentAiMessage 有正确的结构
      if (!this.currentAiMessage) {
        this.currentAiMessage = {
          content: '',
          workflowNodes: [],
          loading: false
        }
      }

      // 更新消息内容
      if (typeof message === 'string') {
        this.currentAiMessage.content = message
      } else if (message && typeof message === 'object') {
        // 如果是对象，合并属性
        this.currentAiMessage = {
          ...this.currentAiMessage,
          ...message
        }
      }

      console.log('📊 更新后的 currentAiMessage:', this.currentAiMessage)
      this.syncAiMessageToWorkspace()
    },
    handleNodeExecution(nodeData) {
      console.log('🔧 Node execution:', nodeData)

      // 更新当前节点信息
      this.currentNode = {
        name: nodeData.name || nodeData.displayName || '未知节点',
        type: nodeData.type || 'default',
        description: nodeData.description || '节点执行中...',
        status: nodeData.status || 'running',
        category: nodeData.category || 'general',
        workflowType: nodeData.workflowType || 'unknown'
      }

      // 添加执行步骤
      this.addExecutionStep(
        nodeData.name || nodeData.displayName || '节点执行',
        nodeData.status || 'running',
        nodeData.description || ''
      )

      // 确保 currentAiMessage 存在并有 workflowNodes 数组
      if (!this.currentAiMessage) {
        this.currentAiMessage = {
          content: '',
          workflowNodes: [],
          loading: false
        }
      }

      if (!this.currentAiMessage.workflowNodes) {
        this.currentAiMessage.workflowNodes = []
      }

      // 检查是否已存在相同类型的节点（用于合并start和end节点）
      const existingNodeIndex = this.currentAiMessage.workflowNodes.findIndex(
        node => node.nodeType === (nodeData.type || 'default')
      )

      if (existingNodeIndex !== -1) {
        // 如果节点已存在，合并数据
        const existingNode = this.currentAiMessage.workflowNodes[existingNodeIndex]

        console.log('🔄 合并节点数据:', {
          existingNode: existingNode,
          newNodeData: nodeData
        })

        // 合并节点数据
        const mergedNode = {
          ...existingNode,
          // 更新状态和时间戳
          nodeStatus: nodeData.status || existingNode.nodeStatus,
          status: nodeData.status || existingNode.status,
          timestamp: nodeData.timestamp || new Date().toISOString(),

          // 合并输入参数（通常来自start节点）
          parameters: this.mergeNodeData(existingNode.parameters, nodeData.parameters),

          // 合并输出结果（通常来自end节点）
          result: this.mergeNodeData(existingNode.result, nodeData.result),

          // 更新节点名称（优先使用更友好的名称）
          nodeName: this.getBetterNodeName(existingNode.nodeName, nodeData.name || nodeData.displayName),
          name: this.getBetterNodeName(existingNode.name, nodeData.name || nodeData.displayName),

          // 标记为已合并
          isMerged: true,
          mergeCount: (existingNode.mergeCount || 1) + 1
        }

        // 替换现有节点
        this.currentAiMessage.workflowNodes[existingNodeIndex] = mergedNode

        console.log('✅ 节点合并完成:', mergedNode)
      } else {
        // 如果是新节点，直接添加
        const workflowNode = {
          id: nodeData.id || Date.now() + Math.random(),
          // WorkspaceSection 期望的字段名
          nodeName: nodeData.name || nodeData.displayName || '未知节点',
          nodeType: nodeData.type || 'default',
          nodeStatus: nodeData.status || 'running',
          workflowTitle: '公文公告生成工作流',
          workflowId: '1947848702138769410',
          // 保留原始字段名以兼容其他组件
          name: nodeData.name || nodeData.displayName || '未知节点',
          type: nodeData.type || 'default',
          status: nodeData.status || 'running',
          timestamp: nodeData.timestamp || new Date().toISOString(),
          parameters: nodeData.parameters,
          result: nodeData.result,
          category: nodeData.category || 'general',
          workflowType: nodeData.workflowType || 'unknown',
          isMerged: false,
          mergeCount: 1
        }

        this.currentAiMessage.workflowNodes.push(workflowNode)
        console.log('➕ 添加新节点:', workflowNode)
      }

      console.log('📊 更新后的工作流节点:', {
        currentNode: this.currentNode,
        executionSteps: this.executionSteps,
        workflowNodes: this.currentAiMessage.workflowNodes,
        currentAiMessage: this.currentAiMessage
      })

      // 强制触发 Vue 响应式更新
      this.$forceUpdate()

      this.syncAiMessageToWorkspace()
    },
    handleProcessUpdate(processData) {
      console.log('Process update:', processData)
      if (processData.currentStep) {
        this.addExecutionStep(
          processData.currentStep.name,
          processData.currentStep.status,
          processData.currentStep.description
        )
      }
      this.syncAiMessageToWorkspace()
    },
    addExecutionStep(stepName, status, description = '') {
      const step = {
        id: Date.now() + Math.random(),
        name: stepName,
        status: status,
        description: description,
        timestamp: new Date().toLocaleTimeString()
      }
      this.executionSteps.push(step)
    },
    syncAiMessageToWorkspace() {
      // 同步AI消息到工作空间
    },
    startDrag(e) {
      this.isDragging = true
      this.startX = e.clientX
      this.startLeftWidth = this.leftPanelWidth

      document.addEventListener('mousemove', this.onDrag)
      document.addEventListener('mouseup', this.stopDrag)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'

      e.preventDefault()
    },

    onDrag(e) {
      if (!this.isDragging) return

      const deltaX = e.clientX - this.startX
      const containerWidth = this.$el.clientWidth
      const deltaPercent = (deltaX / containerWidth) * 100

      let newLeftWidth = this.startLeftWidth + deltaPercent

      // 限制拖拽范围
      newLeftWidth = Math.max(this.minLeftWidth, Math.min(this.maxLeftWidth, newLeftWidth))

      this.leftPanelWidth = newLeftWidth
      this.rightPanelWidth = 100 - newLeftWidth
    },

    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    },

    resetPanelWidths() {
      this.leftPanelWidth = 50
      this.rightPanelWidth = 50
    },

    handleWorkspaceResize(newWidth) {
      this.rightPanelWidth = newWidth
      this.leftPanelWidth = 100 - newWidth
    },
    handleWindowResize() {
      // 处理窗口大小变化
    },
    clearInitialMessageFromUrl() {
      try {
        const currentQuery = { ...this.$route.query }
        let hasChanges = false

        // 需要清除的参数列表
        const paramsToRemove = [
          'initialMessage', 'files', 'modes', 'knowledgeList',
          'knowledgeIds', 'documentIds', 'fromDocumentOffice', 'formData'
        ]

        paramsToRemove.forEach(param => {
          if (currentQuery[param]) {
            delete currentQuery[param]
            hasChanges = true
          }
        })

        // 如果有参数被删除，跳转到公文公告的基础路径
        if (hasChanges) {
          this.$router.replace({
            path: `/announcement-new-dialog/${this.agentId}/${encodeURIComponent(this.agentName)}`,
            query: {}
          }).catch(err => {
            if (err.name !== 'NavigationDuplicated') {
              console.error('清除URL参数时出错:', err)
            }
          })
          console.log('已清理URL，跳转到公文公告基础路径:', `/announcement-new-dialog/${this.agentId}/${encodeURIComponent(this.agentName)}`)
        }
      } catch (error) {
        console.error('清除URL参数时出错:', error)
      }
    },

    /**
     * 合并节点数据（用于合并start和end节点的参数和结果）
     * @param {string|Object} existing - 现有数据
     * @param {string|Object} newData - 新数据
     * @returns {string|Object} 合并后的数据
     */
    mergeNodeData(existing, newData) {
      // 如果新数据为空，返回现有数据
      if (!newData || newData === '{}' || newData === '') {
        return existing
      }

      // 如果现有数据为空，返回新数据
      if (!existing || existing === '{}' || existing === '') {
        return newData
      }

      // 如果都是字符串，尝试解析为JSON
      try {
        const existingObj = typeof existing === 'string' ? JSON.parse(existing) : existing
        const newObj = typeof newData === 'string' ? JSON.parse(newData) : newData

        // 如果都是对象，合并它们
        if (typeof existingObj === 'object' && typeof newObj === 'object') {
          return { ...existingObj, ...newObj }
        }
      } catch (error) {
        // 如果解析失败，优先返回新数据
        console.log('节点数据合并时JSON解析失败，使用新数据:', error)
      }

      // 默认返回新数据
      return newData
    },

    /**
     * 获取更好的节点名称
     * @param {string} existing - 现有名称
     * @param {string} newName - 新名称
     * @returns {string} 更好的名称
     */
    getBetterNodeName(existing, newName) {
      // 如果新名称为空，返回现有名称
      if (!newName) {
        return existing
      }

      // 如果现有名称为空，返回新名称
      if (!existing) {
        return newName
      }

      // 优先级：具体名称 > 通用名称
      const genericNames = ['开始节点', '结束节点', '未知节点', 'start', 'end', 'unknown']

      // 如果现有名称是通用名称，优先使用新名称
      if (genericNames.includes(existing.toLowerCase())) {
        return newName
      }

      // 如果新名称是通用名称，保持现有名称
      if (genericNames.includes(newName.toLowerCase())) {
        return existing
      }

      // 如果都不是通用名称，优先使用更长的名称（通常更具描述性）
      return newName.length > existing.length ? newName : existing
    }
  },
  beforeUnmount() {
    this.stopDrag()
    window.removeEventListener('resize', this.handleWindowResize)
  }
}
</script>

<style scoped>
.new-dialog-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url('@/assets/image/bg.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  margin-left: 7rem; /* 为侧边栏留出空间 */
  width: calc(100% - 7rem); /* 调整宽度以适应侧边栏 */
}

.header {
  position: fixed;
  top: 0;
  left: 7rem; /* 为侧边栏留出空间 */
  width: calc(100% - 7rem); /* 调整宽度以适应侧边栏 */
  z-index: 10;
  background-color: #e0f0fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem;
}

.header-content {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: none;
}

.header-left {
  flex: 1;
  display: flex;
  justify-content: center;
  padding-right: 0.5rem;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  transition: border-right 0.3s ease;
}

.header-left.full-width {
  border-right: none;
  padding-right: 0;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: center;
  padding-left: 0.5rem;
}

.main-content {
  display: flex;
  flex: 1;
  margin-top: 4rem;
  height: calc(100vh - 4rem);
  overflow: hidden;
  position: relative;
  /* 移除 justify-content: center，让子元素按设定宽度显示 */
}

/* 当工作空间收起时，ChatSection居中显示 */
.main-content.workspace-collapsed {
  justify-content: center;
}

/* 聊天区域占满全宽时的样式 */
.chat-full-width {
  flex: none !important;
  width: 65% !important;
  max-width: 1200px !important;
  min-width: 600px !important;
}

/* 可拖动的分割条 */
.resizable-divider {
  position: relative;
  width: 12px;
  height: 100%;
  cursor: col-resize;
  z-index: 100;
  user-select: none;
  touch-action: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  pointer-events: auto;
  background: transparent;
}

.divider-handle {
  width: 6px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.divider-handle::before {
  content: '';
  position: absolute;
  left: -3px;
  right: -3px;
  top: -8px;
  bottom: -8px;
  border-radius: 8px;
  background: transparent;
}

.divider-dots {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 3px;
  width: 100%;
  height: 100%;
}

.dot {
  width: 3px;
  height: 3px;
  background-color: #64748b;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.resizable-divider:hover .divider-handle {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #94a3b8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

.resizable-divider:hover .dot {
  background-color: #475569;
}

.resizable-divider.dragging .divider-handle {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-color: #1d4ed8;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
  transform: scale(1.2);
}

.resizable-divider.dragging .dot {
  background-color: white;
}

/* 分割条提示 */
.divider-tooltip {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 200;
  pointer-events: none;
  backdrop-filter: blur(8px);
  animation: fadeInUp 0.2s ease-out;
}

/* 拖动时的百分比提示 */
.divider-tooltip.drag-tip {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.divider-tooltip.drag-tip::after {
  border-top-color: #2563eb;
}

/* 悬停提示 */
.divider-tooltip.hover-tip {
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  transition-delay: 0.5s;
}

.resizable-divider:hover .divider-tooltip.hover-tip {
  opacity: 1;
  visibility: visible;
}

.divider-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #374151;
}

/* 分割条的活跃区域增大，便于拖拽 */
.resizable-divider::before {
  content: '';
  position: absolute;
  left: -8px;
  right: -8px;
  top: 0;
  bottom: 0;
  cursor: col-resize;
  z-index: 1;
  background: transparent;
  pointer-events: auto;
}

/* 改善拖拽时的视觉反馈 */
.resizable-divider.dragging {
  z-index: 1000;
}

.resizable-divider.dragging::before {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 4px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 工作空间切换按钮 */
.workspace-toggle-button {
  position: fixed;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  cursor: pointer;
}

.toggle-content {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
}

.toggle-content:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #cbd5e1;
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.toggle-icon {
  width: 18px;
  height: 18px;
  color: #64748b;
  transition: color 0.3s ease;
}

.toggle-content:hover .toggle-icon {
  color: #334155;
}

.toggle-text {
  position: absolute;
  right: 55px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  backdrop-filter: blur(8px);
}

/* 添加小箭头 */
.toggle-text::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 100%;
  transform: translateY(-50%);
  border: 5px solid transparent;
  border-left-color: #374151;
}

/* 悬停时显示提示 */
.workspace-toggle-button:hover .toggle-text {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(-8px);
}

/* 拖动时的遮罩层 */
.drag-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(59, 130, 246, 0.05);
  backdrop-filter: blur(2px);
  z-index: 999;
  cursor: col-resize;
  pointer-events: all;
  user-select: none;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .new-dialog-container {
    margin-left: 0; /* 移动端移除左边距 */
    width: 100%; /* 移动端使用全宽 */
  }

  .header {
    left: 0; /* 移动端头部从左边开始 */
    width: 100%; /* 移动端头部使用全宽 */
  }

  .header-content {
    flex-direction: column;
    gap: 0.5rem;
  }

  .header-left,
  .header-right {
    padding: 0;
    justify-content: center;
  }

  .header-left {
    border-right: none; /* 移动端移除边框 */
  }

  .header-left .inline-flex,
  .header-right .inline-flex {
    font-size: 1rem; /* 移动端字体稍小 */
  }

  .main-content {
    flex-direction: column;
    justify-content: flex-start;
  }

  .chat-full-width {
    width: 95% !important;
    min-width: auto !important;
    max-width: none !important;
  }

  .resizable-divider {
    width: 100%;
    height: 8px;
    cursor: row-resize;
    flex-direction: row;
  }

  .divider-handle {
    width: 60px;
    height: 4px;
  }

  .divider-dots {
    flex-direction: row;
    gap: 2px;
  }

  .dot {
    width: 2px;
    height: 2px;
  }

  .workspace-toggle-button {
    bottom: 20px;
    top: auto;
    right: 20px;
    transform: none;
  }

  .toggle-content {
    width: 50px;
    height: 50px;
  }

  .toggle-icon {
    width: 20px;
    height: 20px;
  }

  /* 移动端tooltip样式调整 */
  .toggle-text {
    right: 65px;
    font-size: 11px;
    padding: 4px 8px;
  }

  .toggle-text::after {
    border-width: 4px;
  }
}
</style>
