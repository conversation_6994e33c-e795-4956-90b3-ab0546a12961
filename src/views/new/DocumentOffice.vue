<template>
  <div class="flex min-h-screen document-office-container">
    <div class="flex flex-col w-full main-content">
      
      <!-- 顶部标题区域 - 移到左上角 -->
      <div class="flex justify-start items-start pt-6 pl-8 top-title">
        <h1 class="text-2xl font-bold text-txt-primary">👋 让我帮你写</h1>
      </div>

      <!-- 顶部内容区域 -->
      <div class="flex flex-col items-center pt-4 top-content">
        
        <!-- 横向菜单栏 -->
        <div class="mb-6 w-full max-w-6xl menu-section">
          <div class="flex justify-center">
            <div class="inline-flex bg-white rounded-lg shadow-sm p-1 menu-tabs">
              <button 
                v-for="(menu, index) in menuList" 
                :key="menu.id"
                @click="selectMenu(menu.id)"
                class="px-3 lg:px-6 py-2 text-xs lg:text-sm font-medium rounded-md transition-all duration-200 menu-tab"
                :class="[
                  activeMenuId === menu.id
                    ? 'bg-blue-500 text-white shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                ]"
              >
                {{ menu.name }}
              </button>
            </div>
          </div>
        </div>

        <!-- 功能卡片区域 - 动态显示，响应式布局 -->
        <div class="mb-8 w-full max-w-6xl cards-section">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6 cards-container">
            <!-- 动态渲染卡片，最多显示6个 -->
            <div 
              v-for="(card, index) in displayCards" 
              :key="card.id"
              class="p-4 lg:p-6 rounded-lg cursor-pointer feature-card min-h-[120px] lg:min-h-[140px] flex flex-col justify-center text-center transform transition-all duration-300 hover:scale-105"
              :class="[
                selectedCard === card.name 
                  ? 'selected-card' 
                  : ''
              ]"
              @click="selectCard(card.name)"
            >
              <div class="flex items-center justify-center mb-2 lg:mb-3">
                <img :src="card.icon" :alt="card.name" class="w-6 h-6 lg:w-8 lg:h-8 mr-2 lg:mr-3" />
                <h3 class="text-base lg:text-lg font-bold" style="color: #009DFF">{{ card.name }}</h3>
              </div>
              <p class="text-xs lg:text-sm text-gray-600 mt-1">{{ card.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间空白区域 -->
      <div class="flex-1"></div>

      <!-- 底部输入框区域 -->
      <div class="flex justify-center pb-8 bottom-content" style="transform: translateY(-1cm);">
        <div class="w-full max-w-6xl input-section">
          <!-- 公文办公专用发送框 -->
          <div v-if="currentChatType === 'office'" class="office-chat-wrapper">
            <OfficeChatTemplate
              :user-id="userStore.userInfo.id || 'default-user'"
              :agent-id="'799592ca740609d28525d878d539fdc1'"
              :is-loading="isLoading"
              @send-message="handleOfficeChatSend"
            />
          </div>

          <!-- 公文公告专用发送框 -->
          <div v-else-if="currentChatType === 'announcement'" class="announcement-chat-wrapper">
            <OfficialAnnouncementTemplate
              :user-id="userStore.userInfo.id || 'default-user'"
              :agent-id="'799592ca740609d28525d878d539fdc1'"
              :is-loading="isLoading"
              @send-message="handleAnnouncementChatSend"
            />
          </div>

          <!-- 普通发送框 -->
          <div v-else class="normal-chat-wrapper">
            <ChatTemplate
              :placeholder="inputPlaceholder"
              :show-search-mode="showSearchMode"
              :show-knowledge-selection-button="showKnowledgeSelection"
              :show-file-upload="true"
              :selected-knowledge-list="selectedKnowledgeList"
              :is-loading="isLoading"
              :user-id="userStore.userInfo.id || 'default-user'"
              :agent-id="'799592ca740609d28525d878d539fdc1'"
              @send-message="handleNormalChatSend"
              @show-knowledge-selection="showDatabaseSelection = true"
            />
          </div>

          <!--
            TODO: 预留其他专用发送框的扩展位置
            当需要新增其他卡片类型的专用发送框时，可以在此处添加：

            例如：
            <div v-else-if="currentChatType === 'analysis'" class="analysis-chat-wrapper">
              <AnalysisChatTemplate
                :user-id="userStore.userInfo.id || 'default-user'"
                :agent-id="'799592ca740609d28525d878d539fdc1'"
                :is-loading="isLoading"
                @send-message="handleAnalysisChatSend"
              />
            </div>

            <div v-else-if="currentChatType === 'report'" class="report-chat-wrapper">
              <ReportChatTemplate
                :user-id="userStore.userInfo.id || 'default-user'"
                :agent-id="'799592ca740609d28525d878d539fdc1'"
                :is-loading="isLoading"
                @send-message="handleReportChatSend"
              />
            </div>
          -->
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <footer class="mt-6 text-center fixed text-[#596DF4]" style="bottom: 5px;">
      © 广西交科集团有限公司
    </footer>

    <!-- 知识库选择弹框 -->
    <DatabaseSelection
      v-model="showDatabaseSelection"
      :selected-ids="selectedKnowledgeList.map(item => item.id)"
      @confirm="handleKnowledgeSelection"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getAgentDetail } from '@/api/ai'
import DatabaseSelection from '@/components/DatabaseSelection.vue'
import OfficeChatTemplate from '@/components/ChatTemplate/officeChat.vue'
import OfficialAnnouncementTemplate from '@/components/ChatTemplate/OfficialAnnouncement.vue'
import ChatTemplate from '@/components/ChatTemplate/chat.vue'
import { useUserStore } from '@/stores/user'

// 导入图标
import icon4 from '@/assets/image/icon4.png'

const router = useRouter()
const userStore = useUserStore()

const isLoading = ref(false)
const showSearchMode = ref(true)
const showKnowledgeSelection = ref(true)
const showDatabaseSelection = ref(false)
const selectedKnowledgeList = ref([])
const selectedKnowledgeIds = ref([])
const selectedDocumentIds = ref([])
const selectedCard = ref('')
const inputPlaceholder = ref('你可以问我任何问题，或说出我如何帮助你...')

// 当前聊天类型：'office' 表示公文办公专用发送框，'normal' 表示普通发送框
const currentChatType = ref('normal')

// 公文办公输入样式
const officialChatInputStyle = ref({
  position: 'static',
  padding: '0',
  background: 'transparent',
  width: '100%'
})

// 卡片列表数据
const cardList = ref([
  // 政策法规类
  {
    id: 1,
    name: '公文办公',
    description: '辅助书写公文与可研报告',
    icon: icon4,
    category: 'zhengce'
  },
  {
    id: 7,
    name: '公文公告',
    description: '各类公文和公告文件',
    icon: icon4,
    category: 'zhengce'
  },
  {
    id: 8,
    name: '公示公告',
    description: '对外公示和公告文件',
    icon: icon4,
    category: 'zhengce'
  },
  {
    id: 19,
    name: '政策解读',
    description: '政策文件解读和说明',
    icon: icon4,
    category: 'zhengce'
  },
  {
    id: 20,
    name: '法规条例',
    description: '法律法规和管理条例',
    icon: icon4,
    category: 'zhengce'
  },
  {
    id: 21,
    name: '实施方案',
    description: '政策实施具体方案',
    icon: icon4,
    category: 'zhengce'
  },
  // 标准规范类
  {
    id: 2,
    name: '标准规范制定',
    description: '行业标准、技术规范等制定',
    icon: icon4,
    category: 'biaozhun'
  },
  {
    id: 9,
    name: '技术标准',
    description: '技术标准和规范文件',
    icon: icon4,
    category: 'biaozhun'
  },
  {
    id: 10,
    name: '管理制度',
    description: '企业管理制度和流程',
    icon: icon4,
    category: 'biaozhun'
  },
  {
    id: 22,
    name: '操作规程',
    description: '作业操作规程和指导',
    icon: icon4,
    category: 'biaozhun'
  },
  {
    id: 23,
    name: '质量标准',
    description: '质量控制标准和要求',
    icon: icon4,
    category: 'biaozhun'
  },
  {
    id: 24,
    name: '安全规范',
    description: '安全生产规范和制度',
    icon: icon4,
    category: 'biaozhun'
  },
  // 专项总结类
  {
    id: 3,
    name: '专项总结制定',
    description: '项目总结、工作报告等文件',
    icon: icon4,
    category: 'zhuanxiang'
  },
  {
    id: 11,
    name: '项目总结',
    description: '项目完成后的总结报告',
    icon: icon4,
    category: 'zhuanxiang'
  },
  {
    id: 12,
    name: '年度总结',
    description: '年度工作总结和汇报',
    icon: icon4,
    category: 'zhuanxiang'
  },
  {
    id: 25,
    name: '阶段总结',
    description: '阶段性工作总结报告',
    icon: icon4,
    category: 'zhuanxiang'
  },
  {
    id: 26,
    name: '经验总结',
    description: '工作经验和做法总结',
    icon: icon4,
    category: 'zhuanxiang'
  },
  {
    id: 27,
    name: '成果总结',
    description: '工作成果和效益总结',
    icon: icon4,
    category: 'zhuanxiang'
  }
])

// 菜单列表数据
const menuList = ref([
  { id: 'zhengce', name: '政策法规' },
  { id: 'biaozhun', name: '标准规范' },
  { id: 'zhuanxiang', name: '专项总结' }
])

const activeMenuId = ref('zhengce')
const currentCards = ref([])

// 计算属性：限制显示的卡片数量为最多6个
const displayCards = computed(() => {
  return currentCards.value.slice(0, 6);
});

// 获取agent详情
const fetchAgentDetail = async () => {
  try {
    const response = await getAgentDetail({ agentId: '799592ca740609d28525d878d539fdc1' });
    if (response && response.data) {
      showSearchMode.value = response.data.onlineSearch;
      showKnowledgeSelection.value = response.data.selectKnow !== 0;
    }
  } catch (error) {
    console.error('Failed to get agent detail:', error);
  }
}

// 获取卡片列表数据
const fetchCardList = async () => {
  try {
    // TODO: 替换为实际的接口调用
    // const response = await getCardList();
    // if (response && response.data) {
    //   cardList.value = response.data;
    // }
    
    // 目前使用默认数据
    console.log('Card list loaded:', cardList.value.length, 'items');
  } catch (error) {
    console.error('Failed to get card list:', error);
  }
}

onMounted(async () => {
  await fetchAgentDetail();
  await fetchCardList();
  updateCurrentCards();
  // 默认选中公文办公卡片
  selectCard('公文办公');
});

// 选择菜单
const selectMenu = (menuId) => {
  activeMenuId.value = menuId;
  updateCurrentCards();
}

// 更新当前显示的卡片列表
const updateCurrentCards = () => {
  currentCards.value = cardList.value.filter(card => card.category === activeMenuId.value);
}

// 选择卡片
const selectCard = (cardName) => {
  selectedCard.value = cardName

  // 根据卡片类型切换发送框
  if (cardName === '公文办公') {
    // 公文办公使用专用发送框
    currentChatType.value = 'office'
    inputPlaceholder.value = '请填写公文信息，发送后将跳转到专业对话界面...'
  } else if (cardName === '公文公告') {
    // 公文公告使用专用发送框
    currentChatType.value = 'announcement'
    inputPlaceholder.value = '请填写公文公告信息，发送后将跳转到专业对话界面...'
  } else {
    // 其他卡片类型使用普通发送框
    currentChatType.value = 'normal'
    inputPlaceholder.value = `请输入您需要生成的${cardName}的具体要求...`

    // TODO: 预留其他专用发送框类型的判断逻辑
    // 当需要为其他卡片类型添加专用发送框时，可以在此处扩展：
    /*
    if (cardName === '数据分析') {
      currentChatType.value = 'analysis'
      inputPlaceholder.value = '请输入您的数据分析需求...'
    } else if (cardName === '报告生成') {
      currentChatType.value = 'report'
      inputPlaceholder.value = '请输入您的报告生成需求...'
    } else {
      // 默认使用普通发送框
      currentChatType.value = 'normal'
      inputPlaceholder.value = `请输入您需要生成的${cardName}的具体要求...`
    }
    */
  }
}

// 处理知识库选择
const handleKnowledgeSelection = (selection) => {
  selectedKnowledgeList.value = selection.list
  selectedKnowledgeIds.value = selection.knowledgeIds || []
  selectedDocumentIds.value = selection.documentIds || []
}

// 处理公文办公发送框的发送事件
const handleOfficeChatSend = async (messageData) => {
  console.log('公文办公发送框消息:', messageData)

  isLoading.value = true
  ElMessage.info('正在跳转到专业对话界面...')

  try {
    // 现在 messageData 直接就是表单数据，不再有 formData 包装
    const formData = messageData
    
    // 构建跳转参数
    const files = [
      ...(formData.referenceTemplate || []),
      ...(formData.referenceMaterials || [])
    ]
    const knowledgeIds = [...selectedKnowledgeIds.value]
    const documentIds = [...selectedDocumentIds.value]

    // 构建初始消息内容（包含表单数据）
    let initialMessage = `请根据以下信息生成${formData.subjectContent || '公文'}：\n\n`

    if (formData.title) initialMessage += `标题：${formData.title}\n`
    if (formData.documentNumber) initialMessage += `发文字号：${formData.documentNumber}\n`
    if (formData.issuingUnit) initialMessage += `发文单位：${formData.issuingUnit}\n`
    if (formData.toPrimary) initialMessage += `主送机关：${formData.toPrimary}\n`
    if (formData.documentDate) initialMessage += `成文日期：${formData.documentDate}\n`
    if (formData.contentRequirements) initialMessage += `\n写作要求：${formData.contentRequirements}`

    // 跳转到公文办公专用对话页面
    await router.push({
      name: 'OfficialNewDialog',
      params: {
        agentId: '799592ca740609d28525d878d539fdc1',
        agentName: '公文办公'
      },
      query: {
        initialMessage: initialMessage,
        files: JSON.stringify(files),
        modes: JSON.stringify([]), // 公文办公不需要模式
        knowledgeList: JSON.stringify(selectedKnowledgeList.value),
        knowledgeIds: JSON.stringify(knowledgeIds),
        documentIds: JSON.stringify(documentIds),
        cardType: selectedCard.value,
        fromDocumentOffice: 'true',
        workflowId: '1945775535429709826', // 公文办公工作流ID
        // 新增：传递原始表单数据，确保不被解析丢失
        formData: JSON.stringify(formData)
      }
    })
  } catch (error) {
    console.error('跳转到公文办公对话页面失败:', error)
    ElMessage.error('跳转失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// 处理公文公告发送框的发送事件
const handleAnnouncementChatSend = async (messageData) => {
  console.log('公文公告发送框消息:', messageData)

  isLoading.value = true
  ElMessage.info('正在跳转到专业对话界面...')

  try {
    // 现在 messageData 直接就是表单数据，不再有 formData 包装
    const formData = messageData

    // 构建跳转参数
    const files = [
      ...(formData.referenceTemplate || []),
      ...(formData.referenceMaterials || [])
    ]
    const knowledgeIds = [...selectedKnowledgeIds.value]
    const documentIds = [...selectedDocumentIds.value]

    // 构建初始消息内容（包含表单数据）
    let initialMessage = `请根据以下信息生成公文公告：\n\n`

    if (formData.title) initialMessage += `标题：${formData.title}\n`
    if (formData.subjectContent) initialMessage += `主题内容：${formData.subjectContent}\n`
    if (formData.issuingUnit) initialMessage += `发文单位：${formData.issuingUnit}\n`
    if (formData.toPrimary) initialMessage += `主送机关：${formData.toPrimary}\n`
    if (formData.documentNumber) initialMessage += `发文字号：${formData.documentNumber}\n`
    if (formData.releaseDate) initialMessage += `发布日期：${formData.releaseDate}\n`
    if (formData.createDate) initialMessage += `成文日期：${formData.createDate}\n`
    if (formData.contentRequirements) initialMessage += `\n内容要求：${formData.contentRequirements}`

    // 跳转到公文公告专用对话页面
    await router.push({
      name: 'AnnouncementNewDialog',
      params: {
        agentId: '799592ca740609d28525d878d539fdc1',
        agentName: '公文公告'
      },
      query: {
        initialMessage: initialMessage,
        files: JSON.stringify(files),
        modes: JSON.stringify([]), // 公文公告不需要模式
        knowledgeList: JSON.stringify(selectedKnowledgeList.value),
        knowledgeIds: JSON.stringify(knowledgeIds),
        documentIds: JSON.stringify(documentIds),
        cardType: selectedCard.value,
        fromDocumentOffice: 'true',
        workflowId: '1947848702138769410', // 公文公告工作流ID
        // 新增：传递原始表单数据，确保不被解析丢失
        formData: JSON.stringify(formData)
      }
    })
  } catch (error) {
    console.error('跳转到公文公告对话页面失败:', error)
    ElMessage.error('跳转失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// 处理普通发送框的发送事件
const handleNormalChatSend = async (messageData) => {
  console.log('普通发送框消息:', messageData)

  isLoading.value = true
  ElMessage.info('正在跳转到对话界面...')

  try {
    // 构建跳转参数
    const files = messageData.files || []
    const modes = messageData.modes || []
    const knowledgeIds = [...selectedKnowledgeIds.value]
    const documentIds = [...selectedDocumentIds.value]

    // 跳转到普通对话页面
    await router.push({
      name: 'NewDialog',
      params: {
        agentId: '799592ca740609d28525d878d539fdc1',
        agentName: '公文办公'
      },
      query: {
        initialMessage: messageData.content,
        files: JSON.stringify(files),
        modes: JSON.stringify(modes),
        knowledgeList: JSON.stringify(selectedKnowledgeList.value),
        knowledgeIds: JSON.stringify(knowledgeIds),
        documentIds: JSON.stringify(documentIds),
        cardType: selectedCard.value,
        fromDocumentOffice: 'true'
      }
    })
  } catch (error) {
    console.error('跳转到对话页面失败:', error)
    ElMessage.error('跳转失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// TODO: 预留其他专用发送框的处理函数
// 当需要为其他卡片类型添加专用发送框时，可以在此处添加对应的处理函数：
/*
const handleAnalysisChatSend = async (messageData) => {
  console.log('数据分析发送框消息:', messageData)

  isLoading.value = true
  ElMessage.info('正在跳转到数据分析界面...')

  try {
    // 构建跳转参数
    const files = messageData.files || []
    const knowledgeIds = [...selectedKnowledgeIds.value]
    const documentIds = [...selectedDocumentIds.value]

    // 跳转到数据分析专用对话页面
    await router.push({
      name: 'AnalysisNewDialog',
      params: {
        agentId: 'analysis-agent-id',
        agentName: '数据分析'
      },
      query: {
        initialMessage: messageData.content,
        files: JSON.stringify(files),
        modes: JSON.stringify(messageData.modes || []),
        knowledgeList: JSON.stringify(selectedKnowledgeList.value),
        knowledgeIds: JSON.stringify(knowledgeIds),
        documentIds: JSON.stringify(documentIds),
        cardType: selectedCard.value,
        fromDocumentOffice: 'true'
      }
    })
  } catch (error) {
    console.error('跳转到数据分析对话页面失败:', error)
    ElMessage.error('跳转失败，请重试')
  } finally {
    isLoading.value = false
  }
}

const handleReportChatSend = async (messageData) => {
  console.log('报告生成发送框消息:', messageData)

  isLoading.value = true
  ElMessage.info('正在跳转到报告生成界面...')

  try {
    // 构建跳转参数
    const files = messageData.files || []
    const knowledgeIds = [...selectedKnowledgeIds.value]
    const documentIds = [...selectedDocumentIds.value]

    // 跳转到报告生成专用对话页面
    await router.push({
      name: 'ReportNewDialog',
      params: {
        agentId: 'report-agent-id',
        agentName: '报告生成'
      },
      query: {
        initialMessage: messageData.content,
        files: JSON.stringify(files),
        modes: JSON.stringify(messageData.modes || []),
        knowledgeList: JSON.stringify(selectedKnowledgeList.value),
        knowledgeIds: JSON.stringify(knowledgeIds),
        documentIds: JSON.stringify(documentIds),
        cardType: selectedCard.value,
        fromDocumentOffice: 'true'
      }
    })
  } catch (error) {
    console.error('跳转到报告生成对话页面失败:', error)
    ElMessage.error('跳转失败，请重试')
  } finally {
    isLoading.value = false
  }
}
*/
</script>

<style scoped>
.document-office-container {
  width: 100%;
  min-width: 1400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-image: url('@/assets/image/bg.png'); 
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  --color-primary: #3b82f6;
  --color-primary-middle: #2563eb;
}

/* CSS 变量定义 */
.bg-primary {
  background-color: var(--color-primary);
}

.text-primary {
  color: var(--color-primary);
}

.hover\:bg-primary-middle:hover {
  background-color: var(--color-primary-middle);
}

.border-primary {
  border-color: var(--color-primary);
}

.main-content{
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.top-title {
  position: fixed;
  top: 20px;
  left: 10rem;
  z-index: 10;
  padding: 8px 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.top-content {
  flex-shrink: 0;
  margin-top: 40px;
}

.bottom-content {
  flex-shrink: 0;
}

.robot-greeting-section {
  margin-top: 2rem;
  width: 100%;
  max-width: 1000px;
}

.robot-avatar img {
  border-radius: 50%;
}

.cards-section {
  max-width: 1000px;
  padding: 0 1rem;
}

.menu-section {
  max-width: 1000px;
  padding: 0 1rem;
}

.menu-tabs {
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.menu-tab {
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  font-weight: 500;
  white-space: nowrap;
}

.menu-tab:hover {
  background-color: #f3f4f6;
  transform: translateY(-1px);
}

.input-section {
  max-width: 1000px;
  padding: 0 1rem;
}

.feature-card {
  background: linear-gradient(30deg, rgba(89,109,244,0.14) 13.4%,rgba(174,209,255,0.34) 85.87%);
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(89, 109, 244, 0.1);
  aspect-ratio: auto;
}

.feature-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(89, 109, 244, 0.2);
  border-color: rgba(89, 109, 244, 0.3);
}

.selected-card {
  border: 3px solid #3b82f6 !important;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3) !important;
  background: linear-gradient(30deg, rgba(59,130,246,0.18) 13.4%, rgba(174,209,255,0.4) 85.87%) !important;
}

.selected-card:hover {
  border: 3px solid #2563eb !important;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4) !important;
}

.input-container {
  width: 100%;
  min-height: 10rem;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 1rem;
}

.uploaded-files-preview {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
  width: 100%;
}

.file-preview-item {
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.file-preview-item:hover {
  background-color: #e5e7eb;
}

.file-preview-item button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.file-preview-item:hover button {
  opacity: 1;
}

textarea {
  height: 6rem;
  min-height: 6rem;
  margin-bottom: 0.5rem;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-2 {
  gap: 0.5rem;
}

.fixed {
  position: fixed;
}

/* 公文办公聊天容器样式 */
.official-chat-wrapper {
  width: 100%;
  max-width: 1000px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .cards-container {
    gap: 1rem;
  }
  
  .feature-card {
    min-height: 100px;
  }
  
  .feature-card h3 {
    font-size: 0.9rem;
  }
  
  .feature-card p {
    font-size: 0.75rem;
    line-height: 1.3;
  }
  
  .cards-section {
    padding: 0 0.5rem;
  }
  
  .menu-section {
    padding: 0 0.5rem;
  }
  
  .input-section {
    padding: 0 0.5rem;
  }
  
  .top-title {
    left: 1rem;
    font-size: 1.25rem;
  }
  
  .office-chat-wrapper {
    padding: 0 0.5rem;
  }

  .announcement-chat-wrapper {
    padding: 0 0.5rem;
  }
}
</style> 