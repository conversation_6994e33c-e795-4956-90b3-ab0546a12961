<template>
  <div class="flex min-h-screen bg-gray-50 ml-28">
    <!-- Sidebar -->
    <div class="w-64 bg-white border-r border-gray-200">
      <div class="p-4">
        <!-- Knowledge Base Header -->
        <div class="flex items-center mb-6 space-x-2">
          <el-icon size="20" color="#3b82f6">
            <Folder />
          </el-icon>
          <span class="font-medium text-gray-900">{{ datasetName }}</span>
        </div>

        <!-- Navigation Menu -->
        <el-menu
          :default-active="activeMenu"
          class="border-none"
          @select="handleMenuSelect"
        >
          <el-menu-item index="documents">
            <el-icon><Document /></el-icon>
            <span>文档</span>
          </el-menu-item>
          <el-menu-item index="recall-test">
            <el-icon><Search /></el-icon>
            <span>召回测试</span>
          </el-menu-item>
          <!-- <el-menu-item index="settings">
            <el-icon><Setting /></el-icon>
            <span>设置</span>
          </el-menu-item> -->
        </el-menu>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex flex-col flex-1">
      <!-- Header -->
      <div class="p-6 bg-white border-b border-gray-200">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <el-button :icon="ArrowLeft" circle size="small" @click="goBack" />
            <div class="flex items-center space-x-2">
              <el-icon size="20" color="#3b82f6">
                <Document />
              </el-icon>
              <span class="font-medium text-gray-900">{{ documentName }}</span>
              <el-icon size="16" color="#9ca3af">
                <ArrowDown />
              </el-icon>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <el-button :icon="Plus" @click="openChunkDrawer()">添加分段</el-button>
            <el-tag :type="fileInfo.status === '1' ? 'success' : 'warning'" size="small">
              {{ fileInfo.status === '1' ? '可用' : '处理中' }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- Content Area -->
      <div class="flex flex-1">
        <!-- Segments List -->
        <div v-if="activeMenu === 'documents'" class="flex-1 p-6 min-w-0">
          <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-4">
              <span class="font-medium">{{ total }} 分段</span>
              <el-select v-model="filterStatus" placeholder="全部" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="已启用" value="enabled" />
                <el-option label="已禁用" value="disabled" />
              </el-select>
              <el-input
                v-model="searchQuery"
                placeholder="搜索"
                :prefix-icon="Search"
                style="width: 200px"
                clearable
              />
            </div>
            <div class="flex items-center space-x-2">
              <el-icon><Grid /></el-icon>
              <span class="text-sm text-gray-500">元数据</span>
            </div>
          </div>

          <!-- Segments -->
          <div class="space-y-4 min-h-[400px] max-w-full overflow-hidden">
            <div v-if="!loading && (!segments || segments.length === 0)" class="flex flex-col justify-center items-center py-20">
              <el-empty description="暂无数据" />
            </div>
            <div
              v-else
              v-for="segment in filteredSegments"
              :key="segment.id"
              class="p-4 bg-white rounded-lg border border-gray-200 transition-all cursor-pointer"
              :class="{
                'border-blue-500 bg-blue-50': selectedSegments.includes(segment.id),
                'hover:border-gray-300': !selectedSegments.includes(segment.id)
              }"
              @click="handleCardClick(segment)"
            >
              <div class="flex justify-between items-start mb-3">
                <div class="flex items-center space-x-3">
                  <el-checkbox
                    :model-value="selectedSegments.includes(segment.id)"
                    @change="handleCheckboxChange(segment.id)"
                    @click.stop
                  />
                  <span class="text-sm font-medium text-blue-600">{{ segment.id }}</span>
                  <span class="text-sm text-gray-500">{{ segment.content?.length || 0 }} 字符</span>
                </div>
                <div class="flex items-center space-x-2">
                  <el-tag :type="segment.available ? 'success' : 'info'" size="small">
                    {{ segment.available ? '已启用' : '已禁用' }}
                  </el-tag>
                  <el-button
                    type="danger"
                    link
                    :icon="Delete"
                    @click.stop="handleDeleteSegment(segment)"
                  />
                </div>
              </div>
              <div class="text-sm text-gray-700 break-words line-clamp-3">
                {{ segment.content }}
              </div>
              <div class="flex flex-wrap gap-2 mt-3">
                <el-tag
                  v-for="keyword in getValidKeywords(segment.importantKeywords)"
                  :key="keyword"
                  size="small"
                  type="info"
                  effect="plain"
                >
                  {{ keyword }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <div class="flex justify-center mt-8">
            <el-pagination
              v-model:current-page="currentPage"
              :page-size="pageSize"
              :total="total"
              layout="prev, pager, next"
              small
            />
          </div>
        </div>

        <!-- Recall Test View -->
        <RecallTest v-else-if="activeMenu === 'recall-test'" 
          :datasetIds="[route.query.datasetId]" 
          :documentIds="[route.query.documentId]" 
        />

        <!-- Right Sidebar - Document Info -->
        <div v-if="activeMenu === 'documents'" class="flex-shrink-0 p-6 w-72 bg-white border-l border-gray-200">
          <div class="mb-6">
            <h3 class="mb-4 font-medium text-gray-900">技术参数</h3>
            <div class="space-y-3 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-500">分段模式</span>
                <span>{{ fileInfo.chunkMethod || '默认' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">文件大小</span>
                <span>{{ formatFileSize(fileInfo.size) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">分段数量</span>
                <span>{{ fileInfo.chunkCount || 0 }} 段</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">创建时间</span>
                <span>{{ formatDate(fileInfo.createTime) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">更新时间</span>
                <span>{{ formatDate(fileInfo.updateTime) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">处理进度</span>
                <span>{{ fileInfo.progress || '0.0' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">处理状态</span>
                <span>{{ fileInfo.run || 'UNSTART' }}</span>
              </div>
            </div>
          </div>

          <!-- Keywords Panel -->
          <div class="p-6 mb-6 bg-white rounded-lg shadow-sm" v-if="hasKeywords">
            <h2 class="mb-4 text-lg font-medium text-gray-900">关键词</h2>
            <div class="flex flex-wrap gap-2">
              <el-tag 
                v-for="keyword in fileInfo.keywords" 
                :key="keyword"
                size="small"
              >
                {{ keyword }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom Action Bar -->
      <div
        v-if="selectedSegments.length > 0 && activeMenu === 'documents'"
        class="flex justify-between items-center p-4 bg-white border-t border-gray-200"
      >
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <el-icon color="#3b82f6"><CircleCheckFilled /></el-icon>
            <span class="text-sm font-medium">已选择 {{ selectedSegments.length }} 项</span>
          </div>
          <el-button @click="handleBatchEnable">启用</el-button>
          <el-button @click="handleBatchDisable">禁用</el-button>
          <el-button type="danger" @click="handleBatchDelete">删除</el-button>
          <el-button @click="clearSelection">取消</el-button>
        </div>
      </div>
    </div>

    <!-- Chunk Drawer -->
    <chunk-drawer
      v-model:visible="showChunkDrawer"
      :dataset-id="route.query.datasetId"
      :document-id="route.query.documentId"
      :edit-data="editingChunk"
      @success="handleChunkSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, reactive } from 'vue'
import {
  Folder,
  Document,
  Search,
  Setting,
  ArrowLeft,
  ArrowDown,
  Plus,
  MoreFilled,
  Grid,
  CircleCheckFilled,
  QuestionFilled,
  Delete,
  RefreshRight
} from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getFileChunkList, 
  retrieveFileChunk, 
  editFileChunk, 
  deleteFileChunk,
  parseFile,  
} from '@/api/knowledge'
import ChunkDrawer from './components/ChunkDrawer.vue'
import RecallTest from './components/RecallTest.vue'

const router = useRouter()
const route = useRoute()

// Reactive data
const activeMenu = ref('documents')
const documentEnabled = ref(true)
const filterStatus = ref('all')
const searchQuery = ref('')
const selectedSegments = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const showChunkDrawer = ref(false)
const editingChunk = ref(null)
const loading = ref(false)
const total = ref(0)
const segments = ref([])

// 文件信息
const fileInfo = reactive({
  id: '',
  name: '',
  status: '',
  segmentCount: 0,
  characterCount: 0,
  enabled: false,
  keywords: [],
  technicalParams: {
    segmentMode: '',
    fileSize: '',
    segmentCount: 0
  },
  size: '',
  chunkMethod: '',
  chunkCount: 0,
  createTime: '',
  updateTime: '',
  progress: '',
  run: ''
})

// 文件名和数据库名
const documentName = computed(() => {
  const name = route.query.documentName
  return Array.isArray(name) ? name[0] : (name || '')
})

const datasetName = computed(() => {
  const name = route.query.datasetName
  return Array.isArray(name) ? name[0] : (name || '')
})

// Computed
const filteredSegments = computed(() => {
  let filtered = segments.value || []
  
  if (filterStatus.value !== 'all') {
    filtered = filtered.filter(segment => 
      filterStatus.value === 'enabled' ? segment.available : !segment.available
    )
  }
  
  if (searchQuery.value) {
    filtered = filtered.filter(segment =>
      segment.content?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      (segment.importantKeywords || []).some(keyword => 
        keyword?.toLowerCase().includes(searchQuery.value.toLowerCase())
      )
    )
  }
  
  return filtered
})

// 计算属性：判断是否有关键词
const hasKeywords = computed(() => fileInfo.keywords && fileInfo.keywords.length > 0)

// 判断分块是否有关键词
const hasChunkKeywords = (chunk) => chunk.important_keywords && chunk.important_keywords.length > 0

// 计算属性：过滤分块的有效关键词
const getValidKeywords = (keywords) => {
  if (!keywords || keywords.length === 0) {
    return []
  }
  return keywords.filter(keyword => keyword && keyword.trim() !== '')
}

// Methods
const toggleSegmentSelection = (segmentId) => {
  const index = selectedSegments.value.indexOf(segmentId)
  if (index > -1) {
    selectedSegments.value.splice(index, 1)
  } else {
    selectedSegments.value.push(segmentId)
    // 打开编辑抽屉
    if (selectedSegments.value.length === 1) {
      const segment = segments.value.find(s => s.id === segmentId)
      if (segment) {
        openChunkDrawer(segment)
      }
    }
  }
}

const clearSelection = () => {
  selectedSegments.value = []
  showChunkDrawer.value = false
}

const openChunkDrawer = (chunk = null) => {
  editingChunk.value = chunk
  showChunkDrawer.value = true
}

const handleBatchEnable = async () => {
  if (!selectedSegments.value.length) return
  
  try {
    const promises = selectedSegments.value.map(id => 
      editFileChunk({
        id,
        datasetId: route.query.datasetId,
        documentId: route.query.documentId,
        available: true
  })
    )
    
    await Promise.all(promises)
    ElMessage.success('启用成功')
  clearSelection()
    getChunkList() // 刷新列表
  } catch (error) {
    console.error('Enable failed:', error)
    ElMessage.error('启用失败')
  }
}

const handleBatchDisable = async () => {
  if (!selectedSegments.value.length) return
  
  try {
    const promises = selectedSegments.value.map(id => 
      editFileChunk({
        id,
        datasetId: route.query.datasetId,
        documentId: route.query.documentId,
        available: false
  })
    )
    
    await Promise.all(promises)
    ElMessage.success('禁用成功')
  clearSelection()
    getChunkList() // 刷新列表
  } catch (error) {
    console.error('Disable failed:', error)
    ElMessage.error('禁用失败')
  }
}

const handleBatchDelete = async () => {
  if (!selectedSegments.value.length) return

  try {
    await ElMessageBox.confirm(
      '确定要删除选中的分段吗？此操作不可恢复。',
      '删除确认',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const res = await deleteFileChunk({
      datasetId: route.query.datasetId,
      documentId: route.query.documentId,
      ids: selectedSegments.value
    })

    if (res?.code === 200) {
      ElMessage.success('删除成功')
      clearSelection()
      getChunkList() // 刷新列表
    } else {
      ElMessage.error(res?.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete failed:', error)
      ElMessage.error('删除失败')
    }
  }
}

const goBack = () => {
  router.go(-1)
}

const handleMenuSelect = (index) => {
  activeMenu.value = index
}

// Watch for selection changes
watch(selectedSegments, (newSelection) => {
  if (newSelection.length === 0) {
    showChunkDrawer.value = false
  }
})

// 获取分块列表
const getChunkList = async () => {
  const { documentId, datasetId } = route.query
  if (!documentId || !datasetId) {
    ElMessage.error('参数错误')
    return
  }

  loading.value = true
  try {
    const res = await getFileChunkList({
      documentId,
      datasetId,
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      keywords: searchQuery.value
    })
    
    if (res?.code === 200) {
      segments.value = res.data.chunks || []
      total.value = res.data.total || 0
      
      // 将res.data.doc赋值给fileInfo
      const doc = res.data.doc;
      fileInfo.id = doc.id;
      fileInfo.name = doc.name;
      fileInfo.size = doc.size;
      fileInfo.chunkMethod = doc.chunkMethod;
      fileInfo.chunkCount = doc.chunkCount;
      fileInfo.createTime = doc.createTime;
      fileInfo.updateTime = doc.updateTime;
      fileInfo.progress = doc.progress;
      fileInfo.run = doc.run;
      fileInfo.status = doc.status;
    } else {
      ElMessage.error(res?.message || '获取分块列表失败')
      segments.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('Error:', error)
    ElMessage.error('系统错误')
    segments.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 监听分页变化
watch([currentPage, pageSize], ([newPage, newSize]) => {
  currentPage.value = newPage
  pageSize.value = newSize
  getChunkList()
})

// 搜索相关
const handleSearch = async () => {
  const { documentId, datasetId } = route.query
  if (!documentId || !datasetId) {
    ElMessage.error('参数错误')
    return
  }

  if (!searchQuery.value) {
    getChunkList()
    return
  }

  loading.value = true
  try {
    const res = await retrieveFileChunk({
      question: searchQuery.value,
      datasetIds: [datasetId],
      documentIds: [documentId],
      page: currentPage.value,
      pageSize: pageSize.value,
      similarityThreshold: 0.7,
      vectorSimilarityWeight: 0.7,
      topK: 10,
      keyword: true,
      highlight: true
    })
    
    if (res?.code === 200) {
      segments.value = res.data.chunks || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error(res?.message || '检索失败')
      segments.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('Error:', error)
    ElMessage.error('系统错误')
    segments.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 修改搜索关键字监听
watch(searchQuery, (newQuery) => {
  currentPage.value = 1
  searchQuery.value = newQuery
  handleSearch()
})

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  let fileSize = parseInt(size)
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }
  return `${fileSize.toFixed(2)} ${units[index]}`
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(Number(timestamp))
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 处理抽屉操作成功
const handleChunkSuccess = () => {
  getChunkList() // 刷新列表
}

// 删除单个分块
const handleDeleteSegment = async (segment) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个分段吗？此操作不可恢复。',
      '删除确认',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const res = await deleteFileChunk({
      datasetId: route.query.datasetId,
      documentId: route.query.documentId,
      ids: [segment.id]
    })

    if (res?.code === 200) {
      ElMessage.success('删除成功')
      getChunkList() // 刷新列表
    } else {
      ElMessage.error(res?.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete failed:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleCardClick = (segment) => {
  // 点击卡片时，先选择该分段，然后打开编辑抽屉
  const segmentId = segment.id
  const index = selectedSegments.value.indexOf(segmentId)
  if (index > -1) {
    selectedSegments.value.splice(index, 1)
  } else {
    selectedSegments.value.push(segmentId)
  }
  
  // 打开编辑抽屉
  if (selectedSegments.value.length === 1) {
    openChunkDrawer(segment)
  }
}

const handleCheckboxChange = (segmentId) => {
  // 点击复选框时，只处理选择/取消选择，不打开抽屉
  const index = selectedSegments.value.indexOf(segmentId)
  if (index > -1) {
    selectedSegments.value.splice(index, 1)
  } else {
    selectedSegments.value.push(segmentId)
  }
}

onMounted(() => {
  const { documentId, datasetId } = route.query
  if (!documentId || !datasetId || Array.isArray(documentId) || Array.isArray(datasetId)) {
    ElMessage.error('参数错误')
    return
  }
  getChunkList()
})
</script>

<style scoped>
:deep(.el-menu) {
  border-right: none;
  transition: background-color 0.3s ease;
}

:deep(.el-menu-item) {
  border-radius: 8px;
  margin-bottom: 6px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

:deep(.el-menu-item.is-active) {
  background-color: #e0f2fe;
  color: #0284c7;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  overflow-wrap: break-word;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-drawer__body) {
  padding: 0;
}

:deep(.el-tag) {
  margin-right: 4px;
  margin-bottom: 4px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

:deep(.el-button) {
  transition: background-color 0.3s ease, color 0.3s ease;
}

:deep(.el-button:hover) {
  background-color: #3b82f6;
  color: #ffffff;
}

:deep(.el-empty) {
  color: #9ca3af;
}

:deep(.el-pagination) {
  margin-top: 16px;
}

:deep(.el-icon) {
  transition: transform 0.3s ease;
}

:deep(.el-icon:hover) {
  transform: scale(1.1);
}

/* 确保分段卡片不会超出容器宽度 */
.space-y-4 > div {
  max-width: 100%;
  box-sizing: border-box;
}

/* 确保关键词标签不会导致横向滚动 */
.flex.flex-wrap {
  max-width: 100%;
}
</style>