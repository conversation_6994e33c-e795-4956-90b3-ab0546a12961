<template>
  <div class="h-full bg-gray-50">
    <div class="flex mx-auto max-w-[90rem]">
      <!-- Left Panel - Configuration -->
      <div class="pt-6 pb-6 w-3/4">
        <!-- Segmentation Settings -->
        <div class="p-6 mb-6 bg-white rounded-lg border border-gray-200">
          <h2 class="mb-6 text-lg font-medium text-gray-900">分段设置</h2>
          
          <!-- General Option -->
          <div class="p-4 mb-4 bg-blue-50 rounded-lg border border-blue-200">
            <div class="flex items-center mb-3 space-x-3">
              <el-radio v-model="segmentationType" value="general" size="large">
                <div class="flex items-center space-x-2">
                  <el-icon color="#3b82f6"><Setting /></el-icon>
                  <span class="font-medium">通用</span>
                </div>
              </el-radio>
            </div>
            <p class="ml-8 text-sm text-gray-600">
              适用于大多数场景，您可以设置自定义的分段标识符
            </p>
            
            <!-- General Settings -->
            <div v-if="segmentationType === 'general'" class="mt-4 ml-8">
              <el-row :gutter="24" class="mb-4">
                <el-col :span="8">
                  <div class="mb-2">
                    <label class="text-sm font-medium text-gray-700">分段标识符</label>
                    <el-tooltip content="用于分割文档的标识符" placement="top">
                      <el-icon size="14" color="#9ca3af" class="ml-1">
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                  <el-input v-model="generalSettings.separator" placeholder="分段标识符,不填则使用默认设置" />
                </el-col>
                <el-col :span="8">
                  <div class="mb-2">
                    <label class="text-sm font-medium text-gray-700">分段最大长度</label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <el-input-number v-model="generalSettings.maxLength" :min="100" :max="2000" class="flex-1" />
                    <span class="text-sm text-gray-500">tokens</span>
                    <el-tooltip content="最大分段长度设置" placement="top">
                      <el-icon size="14" color="#9ca3af">
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="mb-2">
                    <label class="text-sm font-medium text-gray-700">分段重叠长度</label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <el-input-number v-model="generalSettings.overlap" :min="0" :max="200" class="flex-1" />
                    <span class="text-sm text-gray-500">tokens</span>
                    <el-tooltip content="分段之间的重叠长度" placement="top">
                      <el-icon size="14" color="#9ca3af">
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </el-col>
              </el-row>
              <!-- Action Buttons -->
              <div class="flex items-center space-x-4">
                <el-button :icon="Refresh" size="small" @click="handlePreview">预览</el-button>
                <el-button size="small" @click="handleReset">重置</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- Retrieval Settings -->
        <!-- <div class="p-6 bg-white rounded-lg border border-gray-200">
          <div class="flex items-center mb-6 space-x-2">
            <h2 class="text-lg font-medium text-gray-900">检索设置</h2>
          </div>
          <div class="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div class="flex items-center mb-3 space-x-3">
              <el-radio v-model="retrievalMethod" value="hybrid" size="large">
                <div class="flex items-center space-x-2">
                  <el-icon color="#3b82f6"><Grid /></el-icon>
                  <span class="font-medium">混合检索</span>
                  <el-tag size="small" type="primary">推荐</el-tag>
                </div>
              </el-radio>
            </div>
            <p class="ml-8 text-sm text-gray-600">
              同时执行全文检索和向量检索，并应用重排序步骤，从两类查询结果中选择匹配用户问题的最佳结果，用户可以选择设置权重或重新排序模型。
            </p>
            <div class="mb-4 ml-8">
              <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-700">Rerank 模型</span>
                  <el-tooltip content="重排序模型设置" placement="top">
                    <el-icon size="14" color="#9ca3af">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
                <el-switch v-model="rerankEnabled" />
              </div>
            </div>
            
            <div class="ml-8">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="mb-2">
                    <div class="flex items-center space-x-2">
                      <span class="text-sm font-medium text-gray-700">Top K</span>
                      <el-tooltip content="返回最相关的K个结果" placement="top">
                        <el-icon size="14" color="#9ca3af">
                          <QuestionFilled />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="flex items-center space-x-3">
                    <el-input-number v-model="topK" :min="1" :max="10" size="small" />
                    <el-slider v-model="topK" :min="1" :max="10" class="flex-1" />
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="mb-2">
                    <div class="flex items-center space-x-2">
                      <span class="text-sm font-medium text-gray-700">Score 阈值</span>
                      <el-tooltip content="相似度分数阈值" placement="top">
                        <el-icon size="14" color="#9ca3af">
                          <QuestionFilled />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="flex items-center space-x-3">
                    <el-input-number v-model="scoreThreshold" :min="0" :max="1" :step="0.1" placeholder="请输入" size="small" style="width: 120px" />
                    <el-slider v-model="scoreThreshold" :min="0" :max="1" :step="0.1" class="flex-1" />
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div> -->

        <!-- Bottom Actions -->
        <div class="flex justify-between mt-8">
          <el-button :icon="ArrowLeft" size="large" @click="goBack">上一步</el-button>
          <el-button type="primary" size="large" :loading="processing" @click="saveAndProcess">
            保存并处理
          </el-button>
        </div>
      </div>

      <!-- Right Panel - File Preview -->
      <div class="p-6 w-1/3">
        <div class="h-full bg-white rounded-lg border border-gray-200">
          <div class="p-4 border-b border-gray-200">
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm text-gray-500">预览</span>
            </div>
            <div class="flex items-center space-x-2">
              <el-icon color="#3b82f6"><Document /></el-icon>
              <div class="font-medium">{{ Array.isArray(fileName) ? fileName.join(', ') : fileName }}</div>
              <el-icon size="14" color="#9ca3af"><ArrowDown /></el-icon>
              <span class="text-sm text-gray-500">{{ previewSegments.length }} 段</span>
            </div>
          </div>
          
          <div v-if="!previewSegments.length" class="p-8 text-center">
            <el-icon size="64" color="#d1d5db" class="mb-4">
              <Search />
            </el-icon>
            <p class="text-sm text-gray-500">
              点击左侧的"预览"按钮来预览效果
            </p>
          </div>
          <div v-else class="p-4">
            <div
              v-for="(segment, index) in previewSegments"
              :key="index"
              class="preview-chunk-card"
            >
              <div class="preview-chunk-header">
                <span class="preview-chunk-title">Chunk-{{ index + 1 }}</span>
                <span class="preview-chunk-meta">· {{ segment.length }} characters</span>
              </div>
              <div class="preview-chunk-content">{{ segment }}</div>
              <div class="preview-chunk-divider"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import {
  ArrowLeft,
  ArrowDown,
  Setting,
  Grid,
  Document,
  Search,
  Refresh,
  QuestionFilled
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { parseFileChunk, previewFileChunk } from '@/api/knowledge'

const props = defineProps({
  fileName: {
    type: [String, Array],
    default: ''
  },
  datasetId: {
    type: String,
    default: ''
  },
  fileIds: {
    type: Array,
    default: () => []
  },
  initialState: {
    type: Object,
    default: () => ({
      segmentationType: 'general',
      retrievalMethod: 'hybrid',
      rerankEnabled: true,
      topK: 2,
      scoreThreshold: '',
      scoreThresholdSlider: 0.5,
      generalSettings: {
        separator: '\\n\\n,\\n',
        maxLength: 500,
        overlap: 50
      },
      preprocessingRules: {
        removeExtraSpaces: true,
        removeUrls: true
      }
    })
  }
})

const emit = defineEmits(['prev-step', 'next-step', 'state-update'])

// Reactive data - 使用初始状态
const segmentationType = ref(props.initialState.segmentationType)
const retrievalMethod = ref(props.initialState.retrievalMethod)
const rerankEnabled = ref(props.initialState.rerankEnabled)
const topK = ref(props.initialState.topK)
const scoreThreshold = ref(props.initialState.scoreThreshold)
const scoreThresholdSlider = ref(props.initialState.scoreThresholdSlider)
const processing = ref(false)
const previewSegments = ref([])

const generalSettings = reactive({
  separator: props.initialState.generalSettings.separator || '\\n\\n,\\n',
  maxLength: props.initialState.generalSettings.maxLength,
  overlap: props.initialState.generalSettings.overlap
})

const preprocessingRules = reactive({
  removeExtraSpaces: props.initialState.preprocessingRules.removeExtraSpaces,
  removeUrls: props.initialState.preprocessingRules.removeUrls
})

// 监听状态变化并向父组件发送更新
watch([segmentationType, retrievalMethod, rerankEnabled, topK, scoreThreshold, scoreThresholdSlider, generalSettings, preprocessingRules], 
  () => {
    emit('state-update', {
      segmentationType: segmentationType.value,
      retrievalMethod: retrievalMethod.value,
      rerankEnabled: rerankEnabled.value,
      topK: topK.value,
      scoreThreshold: scoreThreshold.value,
      scoreThresholdSlider: scoreThresholdSlider.value,
      generalSettings: {
        separator: generalSettings.separator,
        maxLength: generalSettings.maxLength,
        overlap: generalSettings.overlap
      },
      preprocessingRules: {
        removeExtraSpaces: preprocessingRules.removeExtraSpaces,
        removeUrls: preprocessingRules.removeUrls
      }
    })
  },
  { deep: true }
)

// Methods
const goBack = () => {
  emit('prev-step')
}

const handlePreview = async () => {
  if (!props.datasetId || props.fileIds.length === 0) {
    ElMessage.error('缺少必要参数')
    return
  }

  try {
    // 只预览第一个文件
    const fileId = props.fileIds[0]
    const params = {
      datasetId: props.datasetId,
      id: fileId,
      chunkSize: generalSettings.maxLength,
      chunkOverlap: generalSettings.overlap,
      separators: generalSettings.separator,
      numPreviewChunks: 10 // 预览10个分块
    }

    const res = await previewFileChunk(params)  
    
    if (res?.code === 200 && Array.isArray(res.data.chunks)) {
      previewSegments.value = res.data.chunks
    } else {
      previewSegments.value = []
      ElMessage.error('数据解析结果为0，请检查文件是否正确')
    }
  } catch (error) {
    console.error('预览失败:', error)
    ElMessage.error('预览失败')
    previewSegments.value = []
  }
}

const handleReset = () => {
  generalSettings.separator = "\\n\\n,\\n"
  generalSettings.maxLength = 500
  generalSettings.overlap = 50
  preprocessingRules.removeExtraSpaces = true
  preprocessingRules.removeUrls = true
  previewSegments.value = []
}

const saveAndProcess = async () => {
  if (!props.datasetId || props.fileIds.length === 0) {
    ElMessage.error('缺少必要参数')
    return
  }

  processing.value = true
  try {
    // 处理所有文件
    const promises = props.fileIds.map(fileId => {
      const params = {
        datasetId: props.datasetId,
        id: fileId,
        chunkSize: generalSettings.maxLength,
        chunkOverlap: generalSettings.overlap,
        separators: generalSettings.separator
      }
      return parseFileChunk(params).then(res => res || {})
    })

    const results = await Promise.all(promises)
    
    // 检查所有请求是否都成功
    const allSuccess = results.every(res => {
      return res.code === 200 && res.data && res.data.failCount === 0
    })
    console.log(results)
    console.log(allSuccess)
    if (allSuccess) {
      // 汇总所有处理结果
      const totalStats = results.reduce((acc, res) => {
        const data = res.data || {}
        return {
          totalCount: (acc.totalCount || 0) + (data.totalCount || 0),
          successCount: (acc.successCount || 0) + (data.successCount || 0),
          failCount: (acc.failCount || 0) + (data.failCount || 0)
        }
      }, {})
      
      ElMessage.success(`处理完成：共处理 ${totalStats.totalCount} 个分块, ${totalStats.successCount} 个成功, ${totalStats.failCount} 个失败`)
      emit('next-step')
    } else {
      const failedCount = results.filter(res => res.code !== 200 || (res.data && res.data.failCount > 0)).length
      ElMessage.error(`${failedCount}个文件处理失败`)
    }
  } catch (error) {
    console.error('处理失败:', error)
    ElMessage.error('处理失败')
  } finally {
    processing.value = false
  }
}
</script>

<style scoped>
:deep(.el-radio__label) {
  padding-left: 0;
}

:deep(.el-radio__input) {
  margin-right: 8px;
}

:deep(.el-slider) {
  margin: 0;
}

:deep(.el-input-number) {
  width: 120px;
}

:deep(.el-tag--primary) {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

:deep(.el-tag--warning) {
  background-color: #f59e0b;
  border-color: #f59e0b;
  color: white;
}

:deep(.el-alert) {
  border-radius: 6px;
}

:deep(.el-alert--warning) {
  background-color: #fef3cd;
  border-color: #fde68a;
}

:deep(.h-full) {
  height: 100%;
  overflow: auto;
}

:deep(.w-1\/3) {
  width: 50%; /* 增加预览区域的宽度 */
}

/* 分块预览样式优化 */
.preview-chunk-card {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 18px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
  padding: 0 0 12px 0;
  border: 1px solid #f0f0f0;
  position: relative;
}
.preview-chunk-header {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 600;
  color: #2563eb;
  padding: 12px 18px 0 18px;
}
.preview-chunk-title {
  font-weight: 700;
  margin-right: 8px;
}
.preview-chunk-meta {
  font-size: 13px;
  color: #64748b;
  font-weight: 400;
}
.preview-chunk-content {
  font-size: 15px;
  color: #222;
  padding: 10px 18px 0 18px;
  line-height: 1.7;
  word-break: break-all;
}
.preview-chunk-divider {
  border-bottom: 1.5px dashed #e5e7eb;
  margin: 16px 18px 0 18px;
}
</style> 