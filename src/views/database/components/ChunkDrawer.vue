<template>
  <el-drawer
    v-model="dialogVisible"
    :title="isEdit ? '编辑分段' : '新增分段'"
    size="500px"
    direction="rtl"
  >
    <div class="p-4">
      <div v-if="isEdit" class="mb-4">
        <div class="mb-2 text-sm text-gray-500">{{ form.id }}</div>
        <div class="text-sm text-gray-500">{{ form.content?.length || 0 }} 字符</div>
      </div>
      
      <div class="mb-6">
        <label class="block mb-2 text-sm font-medium text-gray-700">分段内容：</label>
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="12"
          placeholder="请输入分段内容"
        />
      </div>

      <!--<div class="mb-6">
        <label class="block mb-2 text-sm font-medium text-gray-700">问题列表：</label>
        <div class="flex flex-wrap gap-2 mb-3">
          <el-tag
            v-for="(question, index) in form.questions"
            :key="index"
            closable
            @close="removeQuestion(index)"
          >
            {{ question }}
          </el-tag>
        </div>
        <div class="flex space-x-2">
          <el-input
            v-model="newQuestion"
            placeholder="添加问题"
            @keyup.enter="addQuestion"
          />
          <el-button @click="addQuestion">添加</el-button>
        </div>
      </div>-->

      <!--<div class="mb-6">
        <label class="block mb-2 text-sm font-medium text-gray-700">关键词：</label>
        <div class="flex flex-wrap gap-2 mb-3">
          <el-tag
            v-for="(keyword, index) in form.important_keywords"
            :key="index"
            closable
            @close="removeKeyword(index)"
          >
            {{ keyword }}
          </el-tag>
        </div>
        <div class="flex space-x-2">
          <el-input
            v-model="newKeyword"
            placeholder="添加关键词"
            @keyup.enter="addKeyword"
          />
          <el-button @click="addKeyword">添加</el-button>
        </div>
      </div>-->

      <div class="flex justify-end space-x-3">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createFileChunk, editFileChunk } from '@/api/knowledge'

export default {
  name: 'ChunkDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    datasetId: {
      type: String,
      required: true
    },
    documentId: {
      type: String,
      required: true
    },
    editData: {
      type: Object,
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const dialogVisible = computed({
      get: () => props.visible,
      set: (val) => emit('update:visible', val)
    })

    const isEdit = computed(() => !!props.editData)

    const form = reactive({
      id: '',
      content: '',
      datasetId: props.datasetId,
      documentId: props.documentId,
      questions: [],
      important_keywords: []
    })

    const newQuestion = ref('')
    const newKeyword = ref('')

    // 重置表单
    const resetForm = () => {
      form.id = ''
      form.content = ''
      form.questions = []
      form.important_keywords = []
      newQuestion.value = ''
      newKeyword.value = ''
    }

    // 初始化编辑数据
    const initEditData = () => {
      if (props.editData) {
        form.id = props.editData.id
        form.content = props.editData.content
        form.questions = [...(props.editData.questions || [])]
        form.important_keywords = [...(props.editData.important_keywords || [])]
      } else {
        resetForm()
      }
    }

    // 监听编辑数据变化
    watch(() => props.editData, () => {
      initEditData()
    }, { immediate: true })

    // 添加问题
    const addQuestion = () => {
      if (newQuestion.value.trim() && !form.questions.includes(newQuestion.value.trim())) {
        form.questions.push(newQuestion.value.trim())
        newQuestion.value = ''
      }
    }

    // 删除问题
    const removeQuestion = (index) => {
      form.questions.splice(index, 1)
    }

    // 添加关键词
    const addKeyword = () => {
      if (newKeyword.value.trim() && !form.important_keywords.includes(newKeyword.value.trim())) {
        form.important_keywords.push(newKeyword.value.trim())
        newKeyword.value = ''
      }
    }

    // 删除关键词
    const removeKeyword = (index) => {
      form.important_keywords.splice(index, 1)
    }

    // 提交
    const handleSubmit = async () => {
      if (!form.content.trim()) {
        ElMessage.warning('请输入分块内容')
        return
      }

      try {
        const api = isEdit.value ? editFileChunk : createFileChunk
        const res = await api(form)
        
        if (res?.code === 200) {
          ElMessage.success(isEdit.value ? '保存成功' : '添加成功')
          dialogVisible.value = false
          emit('success')
          resetForm()
        } else {
          ElMessage.error(res?.message || (isEdit.value ? '保存失败' : '添加失败'))
        }
      } catch (error) {
        console.error(isEdit.value ? '保存分块失败:' : '添加分块失败:', error)
        ElMessage.error(isEdit.value ? '保存失败' : '添加失败')
      }
    }

    // 关闭对话框
    const handleClose = () => {
      dialogVisible.value = false
      resetForm()
    }

    return {
      dialogVisible,
      isEdit,
      form,
      newQuestion,
      newKeyword,
      addQuestion,
      removeQuestion,
      addKeyword,
      removeKeyword,
      handleSubmit,
      handleClose
    }
  }
}
</script> 