<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Main Content -->
    <div class="flex mx-auto max-w-7xl">
      <!-- Left Panel - Processing Status -->
      <div class="p-6 w-2/3">
        <div class="mx-auto max-w-2xl">
          <!-- Success Header -->
          <div class="mb-8 text-center">
            <div class="flex justify-center items-center mb-4 space-x-2">
              <span class="text-2xl">🎉</span>
              <h1 class="text-2xl font-medium text-gray-900">文档已上传</h1>
            </div>
            <p class="text-gray-600">
              文档已上传至知识库，你可以在知识库的文档列表中找到它。
            </p>
          </div>

          <!-- Processing Status -->
          <div class="p-6 mb-8 bg-white rounded-lg border border-gray-200">
            <div class="space-y-4">
              <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3">
                  <el-icon color="#3b82f6">
                    <Document />
                  </el-icon>
                  <span class="font-medium text-gray-900">{{ displayFileName }}</span>
                </div>
                <div class="flex items-center space-x-3">
                  <span class="text-sm text-gray-500">{{ processingProgress }}%</span>
                  <div class="overflow-hidden w-24 h-2 bg-gray-200 rounded-full">
                    <div 
                      class="h-full bg-blue-500 transition-all duration-300"
                      :style="{ width: processingProgress + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Processing Summary -->
          <!-- <div class="p-6 mb-8 bg-white rounded-lg border border-gray-200">
            <h3 class="mb-4 font-medium text-gray-900">处理配置摘要</h3>
            
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-gray-600">分段模式</span>
                <span class="font-medium">自定义</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-gray-600">最大分段长度</span>
                <span class="font-medium">500</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-gray-600">文本预处理规则</span>
                <span class="text-sm font-medium">删除连续的空格符，换行符和制表符</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-gray-600">索引方式</span>
                <el-tag type="warning" size="small">高质量</el-tag>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-gray-600">检索设置</span>
                <el-tag color="#8b5cf6" size="small">向量检索</el-tag>
              </div>
            </div>
          </div> -->

          <!-- Action Buttons -->
          <div class="flex justify-center space-x-4">
            <el-button type="primary" size="large" :icon="ArrowRight" @click="goToDocuments">
              前往文档
            </el-button>
          </div>
        </div>
      </div>

      <!-- Right Panel - Next Steps -->
      <div class="p-6 w-1/3">
        <div class="p-6 bg-white rounded-lg border border-gray-200">
          <div class="flex items-center mb-4 space-x-2">
            <el-icon color="#3b82f6">
              <InfoFilled />
            </el-icon>
            <h3 class="font-medium text-gray-900">接下来做什么</h3>
          </div>
          
          <p class="text-sm leading-relaxed text-gray-600">
            当文档完成嵌入处理后，知识库即可准备使用。你可以为上下文使用，你可以在接下来编辑中找到下文设置，通过创建智能体来进行发布。
          </p>

          <!-- Quick Actions -->
          <div class="mt-6 space-y-3">
            <el-button class="justify-start w-full" text>
              <el-icon class="mr-2"><Document /></el-icon>
              查看文档列表
            </el-button>
            
            <el-button class="justify-start w-full" text>
              <el-icon class="mr-2"><Search /></el-icon>
              测试召回效果
            </el-button>
            
            <el-button class="justify-start w-full" text>
              <el-icon class="mr-2"><Setting /></el-icon>
              配置知识库设置
            </el-button>
            
            <el-button class="justify-start w-full" text>
              <el-icon class="mr-2"><ChatDotRound /></el-icon>
              创建 AI 应用
            </el-button>
          </div>

          <!-- Processing Tips -->
          <div class="p-4 mt-6 bg-blue-50 rounded-lg">
            <div class="flex items-start space-x-2">
              <el-icon color="#3b82f6" size="16" class="mt-0.5">
                <InfoFilled />
              </el-icon>
              <div>
                <h4 class="mb-1 text-sm font-medium text-blue-900">处理提示</h4>
                <p class="text-xs text-blue-700">
                  文档处理时间取决于文档大小和复杂度。处理完成后，你将收到通知。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  ArrowRight,
  Document,
  Loading,
  Link,
  InfoFilled,
  Search,
  Setting,
  ChatDotRound
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// Props定义
const router = useRouter()
const props = defineProps({
  databaseId: {
    type: String,
    required: true
  },
  fileIds: {
    type: Array,
    required: true
  },
  fileNames: {
    type: Array,
    required: true
  },
  databaseName: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['prev-step'])

// Reactive data
const processingProgress = ref(0)
let progressInterval = null

// Simulate processing progress
const startProcessing = () => {
  progressInterval = setInterval(() => {
    if (processingProgress.value < 100) {
      processingProgress.value = parseFloat((processingProgress.value + Math.random() * 10).toFixed(2))
      if (processingProgress.value > 100) {
        processingProgress.value = 100
      }
    } else {
      if (progressInterval) {
        clearInterval(progressInterval)
      }
      ElMessage.success('文档处理完成！')
    }
  }, 100)
}

const goBack = () => {
  emit('prev-step')
}

const goToDocuments = () => {
  router.push({
    name: 'DatabaseDetail',
    query: { 
      id: props.databaseId,
      fileName: props.fileNames.join(','),
      name: props.databaseName
    }
  })
}

// 计算文件名显示
const displayFileName = computed(() => {
  return props.fileNames.join(', ')
})

onMounted(() => {
  startProcessing()
})

onUnmounted(() => {
  if (progressInterval) {
     clearInterval(progressInterval)
   }
})
</script>

<style scoped>
:deep(.el-tag--primary) {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

:deep(.el-tag--warning) {
  background-color: #f59e0b;
  border-color: #f59e0b;
  color: white;
}

:deep(.el-button--text) {
  color: #6b7280;
  padding: 8px 12px;
}

:deep(.el-button--text:hover) {
  color: #3b82f6;
  background-color: #f8faff;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 