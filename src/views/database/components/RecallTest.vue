<template>
  <div class="flex flex-1">
    <!-- Test Area -->
    <div class="flex-grow p-6">
      <!-- Header -->
      <div class="mb-6">
        <h1 class="mb-2 text-xl font-medium text-gray-900">召回测试</h1>
        <p class="text-sm text-gray-600">
          根据给定的查询文本测试知识库召回效果。
        </p>
      </div>

      <!-- Test Input -->
      <div class="p-6 mb-6 bg-white rounded-lg border border-gray-200">
        <div class="flex items-end space-x-4">
          <div class="flex-1">
            <label class="block mb-2 text-sm font-medium text-gray-700">测试文本</label>
            <el-input
              v-model="testQuery"
              type="textarea"
              :rows="10"
              placeholder="请输入测试查询文本..."
              class="w-full"
              @keyup.enter="runTest"
            />
            <div class="flex justify-between items-center mt-2">
              <span class="text-sm text-gray-500">{{ testQuery.length }} / 200</span>
            </div>
          </div>
          <el-button 
            type="primary" 
            size="large"
            :loading="testing"
            :disabled="!testQuery.trim()"
            @click="runTest"
          >
            测试
          </el-button>
        </div>
      </div>
      <!-- Advanced Settings Section -->
      <div class="mt-12 max-w-4xl">
        <el-divider />
        <div class="mb-8">
          <h3 class="mb-2 text-lg font-medium text-gray-900">设置</h3>
          <p class="text-sm text-gray-600">
            配置召回测试的参数，包括Embedding模型、检索设置、权重设置等。
          </p>
        </div>
        <!-- Embedding Model -->
        <div class="mb-8">
          <h4 class="mb-4 text-base font-medium text-gray-900">Embedding 模型</h4>
          <el-select v-model="embeddingModel" class="w-80">
            <el-option value="bge-m3" label="bge-m3">
              <span>bge-reranker-v2-m3@Xinference</span>
            </el-option>
          </el-select>
        </div>

        <!-- Retrieval Settings -->
        <div class="mb-8">
          <h4 class="mb-4 text-base font-medium text-gray-900">检索设置</h4>
          <!-- Hybrid Search -->
          <div class="p-6 mb-6 bg-blue-50 rounded-lg border-2 border-blue-200">
            <div class="flex items-center mb-3 space-x-3">
              <el-icon color="#8b5cf6" size="20">
                <Grid />
              </el-icon>
              <h5 class="font-medium text-gray-900">混合检索</h5>
            </div>
            <p class="mb-4 text-sm text-gray-600">
              同时执行全文检索和向量检索，并应用重排序步骤，从两类查询结果中选择匹配用户问题的最佳结果，用户可以选择重排序重配置重新排序模型。
            </p>

            <!-- Rerank Settings -->
            <div class="p-4 mb-4 bg-white rounded-lg">
              <div class="flex justify-between items-center mb-4">
                <div class="flex items-center space-x-2">
                  <el-icon color="#3b82f6" size="16">
                    <Sort />
                  </el-icon>
                  <span class="font-medium text-gray-900">权重设置</span>
                </div>
                <div class="flex items-center space-x-3">
                  <el-icon color="#10b981" size="16">
                    <CircleCheck />
                  </el-icon>
                  <span class="font-medium text-gray-900">Rerank 模型</span>
                </div>
              </div>
              
              <p class="mb-4 text-sm text-gray-600">
                通过调整分配的权重，重新排序策略确定是优先进行文义匹配还是关键字匹配，用户可以以选择设置权重配置重新排序模型。
              </p>
              <!-- Top K and Score Settings -->
              <div class="ml-6">
                <el-row :gutter="16">
                  <el-col :span="8">
                    <div class="mb-2">
                      <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-gray-700">Top K</span>
                        <el-tooltip content="返回最相关的K个结果" placement="top">
                          <el-icon size="14" color="#9ca3af">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <el-input-number v-model="advancedSettings.topK" :min="0" :max="1024" size="small" style="width: 100px" />
                      <el-slider v-model="advancedSettings.topK" :min="0" :max="1024" class="flex-1" size="small" />
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="mb-2">
                      <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-gray-700">Score 阈值</span>
                        <el-tooltip content="相似度分数阈值" placement="top">
                          <el-icon size="14" color="#9ca3af">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <el-input 
                        v-model="advancedSettings.scoreThreshold" 
                        placeholder="请输入" 
                        size="small" 
                        style="width: 80px"
                        @input="onScoreThresholdInputChange"
                      />
                      <el-slider 
                        v-model="advancedSettings.scoreThresholdSlider" 
                        :min="0" 
                        :max="1" 
                        :step="0.1" 
                        class="flex-1" 
                        size="small"
                        @change="onScoreThresholdSliderChange"
                      />
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="mb-2">
                      <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-gray-700">向量相似度权重</span>
                        <el-tooltip content="向量相似度加权，范围0~1" placement="top">
                          <el-icon size="14" color="#9ca3af">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <el-input-number
                        v-model="advancedSettings.vectorSimilarityWeight"
                        :min="0"
                        :max="1"
                        :step="0.01"
                        size="small"
                        style="width: 80px"
                        @input="onVectorWeightInputChange"
                      />
                      <el-slider
                        v-model="advancedSettings.vectorSimilarityWeightSlider"
                        :min="0"
                        :max="1"
                        :step="0.01"
                        class="flex-1"
                        size="small"
                        @change="onVectorWeightSliderChange"
                      />
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Test Records -->
      
      <!-- <div class="bg-white rounded-lg border border-gray-200">
        <div class="p-6 border-b border-gray-200">
          <h3 class="font-medium text-gray-900">测试记录</h3>
        </div>
        
        <el-table
          :data="testRecords"
          style="width: 100%"
          :loading="loadingRecords"
        >
          <el-table-column prop="type" label="数据源" width="120">
            <template #default="{ row }">
              <div class="flex items-center space-x-2">
                <el-icon v-if="row.type === 'retrieval'" color="#10b981">
                  <Search />
                </el-icon>
                <el-icon v-else color="#6b7280">
                  <DataLine />
                </el-icon>
                <span class="text-sm">{{ row.type === 'retrieval' ? 'Retrieval Test' : 'App' }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="content" label="查询文本" min-width="300">
            <template #default="{ row }">
              <span class="text-sm text-gray-700">{{ row.content }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="timestamp" label="测试时间" width="150">
            <template #default="{ row }">
              <span class="text-sm text-gray-500">{{ formatTimestamp(row.timestamp) }}</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="p-4 border-t border-gray-200">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="totalRecords"
            layout="prev, pager, next"
            small
            class="justify-center"
          />
        </div>
      </div> -->
    </div>

    <!-- Results Sidebar -->
    <div class="w-1/3 max-w-[400px] bg-white border-l border-gray-200">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="font-medium text-gray-900">{{ recallResults.length }} 个召回答案</h3>
        </div>

        <!-- Results List -->
        <div class="space-y-4">
          <div
            v-for="(result, index) in recallResults"
            :key="result.id"
            class="p-4 bg-white rounded-lg border border-gray-100 transition-all duration-200 cursor-pointer hover:border-blue-100 hover:shadow-lg"
            @click="openResultDialog(result)"
          >
            <!-- Result Header -->
            <div class="flex justify-between items-center mb-2">
              <div class="flex items-center space-x-3">
                <span class="px-2.5 py-0.5 text-xs text-blue-600 bg-blue-50 rounded-full">Chunk-{{ String(index + 1).padStart(2, '0') }}</span>
                <span class="text-xs text-gray-400">{{ result.characterCount || 286 }} 字符</span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">SCORE</span>
                <span class="text-sm font-medium text-blue-600">{{ Number(result.similarity).toFixed(2) }}</span>
              </div>
            </div>

            <!-- Document Name -->
            <div class="mb-2 text-sm text-gray-600 truncate">
              <el-icon size="12" class="mr-1"><Document /></el-icon>
              {{ result.documentKeyword }}
            </div>

            <!-- Result Content -->
            <div class="mb-2 text-sm leading-relaxed text-gray-700 line-clamp-3" v-html="result.content"></div>

            <!-- Keywords -->
            <div v-if="result.importantKeywords?.length && result.importantKeywords[0] !== ''" class="flex flex-wrap gap-1">
              <el-tag
                v-for="keyword in result.importantKeywords"
                :key="keyword"
                size="small"
                class="!px-2 !py-0.5 !text-xs !bg-gray-50 !border-gray-100 !text-gray-600"
                effect="plain"
              >
                {{ keyword }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Result Dialog -->
  <el-dialog 
    v-model="showDialog" 
    title="查询结果详情" 
    width="60%"
    class="result-dialog"
    destroy-on-close
  >
    <template #default>
      <div v-if="selectedResult" class="space-y-6">
        <!-- 文档信息 -->
        <div class="p-4 bg-gray-50 rounded-lg">
          <div class="flex justify-between items-center mb-3">
            <div class="flex items-center space-x-3">
              <span class="px-2.5 py-0.5 text-sm text-blue-600 bg-blue-50 rounded-full">Chunk-{{ String(selectedResult.displayIndex).padStart(2, '0') }}</span>
              <span class="text-sm text-gray-500">{{ selectedResult.characterCount || 286 }} 字符</span>
            </div>
            <div class="flex items-center space-x-4">
              <div class="px-3 text-center">
                <div class="mb-1 text-xs text-gray-500">向量相似度</div>
                <div class="text-base font-medium text-blue-600">{{ Number(selectedResult.vectorSimilarity).toFixed(3) }}</div>
              </div>
              <div class="px-3 text-center border-gray-200 border-x">
                <div class="mb-1 text-xs text-gray-500">词项相似度</div>
                <div class="text-base font-medium text-green-600">{{ Number(selectedResult.termSimilarity).toFixed(3) }}</div>
              </div>
              <div class="px-3 text-center">
                <div class="mb-1 text-xs text-gray-500">综合得分</div>
                <div class="text-base font-medium text-purple-600">{{ Number(selectedResult.similarity).toFixed(3) }}</div>
              </div>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">文档名称:</span>
              <span class="text-sm text-gray-900">{{ selectedResult.documentKeyword }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">文档ID:</span>
              <span class="font-mono text-sm text-gray-900">{{ selectedResult.documentId }}</span>
            </div>
          </div>
        </div>

        <!-- 匹配内容 -->
        <div class="space-y-3">
          <h3 class="text-base font-medium text-gray-900">匹配内容</h3>
          <div class="p-4 bg-gray-50 rounded-lg">
            <p class="text-sm leading-relaxed text-gray-700 whitespace-pre-wrap" v-html="selectedResult.content"></p>
          </div>
        </div>

        <!-- 关键词 -->
        <div v-if="selectedResult.importantKeywords?.length && selectedResult.importantKeywords[0] !== ''" class="space-y-3">
          <h3 class="text-base font-medium text-gray-900">关键词</h3>
          <div class="flex flex-wrap gap-2">
            <el-tag
              v-for="keyword in selectedResult.importantKeywords"
              :key="keyword"
              size="default"
              class="!px-3 !py-1 !bg-gray-50 !border-gray-100 !text-gray-600"
              effect="plain"
            >
              {{ keyword }}
            </el-tag>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <el-button type="primary" plain @click="showDialog = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import {
  Document,
  Search,
  Setting,
  DataLine
} from '@element-plus/icons-vue'
import { ElMessage, ElDialog } from 'element-plus'
import { retrieveFileChunk } from '@/api/knowledge'

// Props
const props = defineProps({
  documentIds: {
    type: Array,
    required: true,
    default: () => []
  },
  datasetIds: {
    type: Array,
    required: true
  }
})

// 响应式变量
const testQuery = ref('')
const testing = ref(false)
const loadingRecords = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalRecords = ref(0)
const showDialog = ref(false)
const selectedResult = ref(null)
const recallResults = ref([])

const advancedSettings = reactive({
  topK: 10,
  scoreThreshold: 0.2,
  scoreThresholdSlider: 0.2,
  vectorSimilarityWeight: 0.7,
  vectorSimilarityWeightSlider: 0.7
})

const embeddingModel = ref('bge-reranker-v2-m3@Xinference')

// 方法
const openResultDialog = (result) => {
  console.log('Opening dialog with result:', result)
  selectedResult.value = result
  selectedResult.value.displayIndex = recallResults.value.findIndex(r => r.id === result.id) + 1
  showDialog.value = true
}

const runTest = async () => {
  if (!testQuery.value.trim()) return
  
  testing.value = true
  
  try {
    const params = {
      page: 1,
      pageSize: 10,
      similarityThreshold: advancedSettings.scoreThreshold,
      vectorSimilarityWeight: advancedSettings.vectorSimilarityWeight,
      rerankId: "bge-reranker-v2-m3",
      topK: advancedSettings.topK,
      keyword: false,
      highlight: true
    }

    const res = await retrieveFileChunk({
      question: testQuery.value,
      datasetIds: props.datasetIds,
      documentIds: props.documentIds,
      page: currentPage.value,
      pageSize: pageSize.value,
      similarityThreshold: parseFloat(params.similarityThreshold),
      vectorSimilarityWeight: params.vectorSimilarityWeight,
      topK: params.topK,
      keyword: params.keyword,
      highlight: true,
      rerankId: "bge-reranker-v2-m3"
    })
    
    if (res?.code === 200) {
      if (!res.data.chunks && !res.data.docAggs && res.data.total === 0) {
        ElMessage.warning('无法检索相关信息')
      } else {
        recallResults.value = res.data.chunks || []
        totalRecords.value = res.data.total || 0
        ElMessage.success('查询完成')
      }
    } else {
      ElMessage.error(res?.message || '查询失败')
    }
  } catch (error) {
    console.error('Test failed:', error)
    ElMessage.error('系统错误')
  } finally {
    testing.value = false
  }
}

const formatTimestamp = (timestamp) => {
  return timestamp
}

const onScoreThresholdInputChange = (val) => {
  advancedSettings.scoreThreshold = val
  advancedSettings.scoreThresholdSlider = parseFloat(val) || 0
}

const onScoreThresholdSliderChange = (val) => {
  advancedSettings.scoreThresholdSlider = val
  advancedSettings.scoreThreshold = val.toString()
}

const onVectorWeightInputChange = (val) => {
  advancedSettings.vectorSimilarityWeightSlider = val
}

const onVectorWeightSliderChange = (val) => {
  advancedSettings.vectorSimilarityWeight = val
}

const getRetrieveParams = () => ({
  page: 1,
  pageSize: 10,
  similarityThreshold: advancedSettings.scoreThreshold,
  vectorSimilarityWeight: advancedSettings.vectorSimilarityWeight,
  rerankId: "bge-reranker-v2-m3",
  topK: advancedSettings.topK,
  keyword: false,
  highlight: true
})

onMounted(() => {
  // Load initial data
})
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

:deep(.result-dialog .el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__header) {
  margin-right: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
}

:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-table .el-table__row:hover > td) {
  background-color: #f8faff;
}

:deep(.el-pagination) {
  justify-content: center;
}

:deep(.el-tag) {
  margin-right: 4px;
  margin-bottom: 4px;
}

:deep(.el-textarea__inner) {
  resize: none;
}
</style> 