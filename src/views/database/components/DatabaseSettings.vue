<template>
  <div class="flex-1 p-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center mb-4 space-x-3">
        <el-icon size="24" color="#3b82f6">
          <Folder />
        </el-icon>
        <h1 class="text-2xl font-medium text-gray-900">知识库设置</h1>
      </div>
      <p class="text-gray-600">
        在这里，您可以修改此知识库的属性和检索设置
      </p>
    </div>

    <!-- Settings Form -->
    <div class="max-w-2xl">
      <el-form
        ref="settingsFormRef"
        :model="settingsForm"
        :rules="formRules"
        label-position="top"
        size="large"
      >
        <!-- Knowledge Base Name -->
        <el-form-item label="知识库名称" prop="name" class="mb-8">
          <el-input
            v-model="settingsForm.name"
            placeholder="请输入知识库名称"
            class="w-full"
          />
        </el-form-item>

        <!-- Knowledge Base Description -->
        <el-form-item label="知识库描述" prop="description" class="mb-8">
          <el-input
            v-model="settingsForm.description"
            type="textarea"
            :rows="6"
            placeholder="描述该数据集的内容，详细描述可以让 AI 更好地访问数据集的内容，如果为空，LangGenius 将使用默认的命中策略。"
            class="w-full"
          />
        </el-form-item>

        <!-- Visibility Permissions -->
        <!--<el-form-item label="可见权限" prop="visibility" class="mb-8">
          <el-select
            v-model="settingsForm.visibility"
            placeholder="请选择可见权限"
            class="w-full"
          >
            <el-option
              value="me"
              label="个人"
            >
              <div class="flex items-center space-x-2">
                <el-avatar :size="24" class="text-white bg-blue-500">
                  G
                </el-avatar>
                <span>个人</span>
              </div>
            </el-option>
            <el-option
              value="team"
              label="部门团队"
            >
              <div class="flex items-center space-x-2">
                <el-icon size="20" color="#6b7280">
                  <UserFilled />
                </el-icon>
                <span>部门团队</span>
              </div>
            </el-option>
            <el-option
              value="public"
              label="公共知识库"
            >
              <div class="flex items-center space-x-2">
                <el-icon size="20" color="#6b7280">
                  <Connection />
                </el-icon>
                <span>公共知识库</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>-->
      </el-form>
    </div>

    <!-- Advanced Settings Section -->
    <div class="mt-12 max-w-4xl">
      <el-divider />
      
      <div class="mb-8">
        <h3 class="mb-2 text-lg font-medium text-gray-900">高级设置</h3>
        <p class="text-sm text-gray-600">
          配置知识库的高级参数和检索策略
        </p>
      </div>
      <!-- Embedding Model -->
      <div class="mb-8">
        <h4 class="mb-4 text-base font-medium text-gray-900">Embedding 模型</h4>
        <el-select v-model="settingsForm.embeddingModel" class="w-80">
          <el-option value="bge-reranker-v2-m3@Xinference" label="bge-reranker-v2-m3@Xinference">
            <span>bge-reranker-v2-m3@Xinference</span>
          </el-option>
        </el-select>
      </div>

      <!-- Retrieval Settings -->
      <!--<div class="mb-8">
        <h4 class="mb-4 text-base font-medium text-gray-900">检索设置</h4>
        注释：Hybrid Search
        <div class="p-6 mb-6 bg-blue-50 rounded-lg border-2 border-blue-200">
          <div class="flex items-center mb-3 space-x-3">
            <el-icon color="#8b5cf6" size="20">
              <Grid />
            </el-icon>
            <h5 class="font-medium text-gray-900">混合检索</h5>
            <el-tag size="small" type="primary">推荐</el-tag>
          </div>
          <p class="mb-4 text-sm text-gray-600">
            同时执行全文检索和向量检索，并应用重排序步骤，从两类查询结果中选择匹配用户问题的最佳结果，用户可以选择重排序重配置重新排序模型。
          </p>

          注释：Rerank Settings
          <div class="p-4 mb-4 bg-white rounded-lg">
            <div class="flex justify-between items-center mb-4">
              <div class="flex items-center space-x-2">
                <el-icon color="#3b82f6" size="16">
                  <Sort />
                </el-icon>
                <span class="font-medium text-gray-900">权重设置</span>
              </div>
              <div class="flex items-center space-x-3">
                <el-icon color="#10b981" size="16">
                  <CircleCheck />
                </el-icon>
                <span class="font-medium text-gray-900">Rerank 模型</span>
                <el-radio :model-value="true" disabled>
                  <span class="ml-1">启用</span>
                </el-radio>
              </div>
            </div>
            
            <p class="mb-4 text-sm text-gray-600">
              通过调整分配的权重，重新排序策略确定是优先进行文义匹配还是关键字匹配，用户可以以选择设置权重配置重新排序模型。
            </p>

            注释：Top K and Score Settings
            <div class="ml-6">
              <el-row :gutter="16">
                <el-col :span="12">
                  <div class="mb-2">
                    <div class="flex items-center space-x-2">
                      <span class="text-sm font-medium text-gray-700">Top K</span>
                      <el-tooltip content="返回最相关的K个结果" placement="top">
                        <el-icon size="14" color="#9ca3af">
                          <QuestionFilled />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <el-input-number v-model="settingsForm.topK" :min="1" :max="10" size="small" style="width: 80px" />
                    <el-slider v-model="settingsForm.topK" :min="1" :max="10" class="flex-1" size="small" />
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="mb-2">
                    <div class="flex items-center space-x-2">
                      <span class="text-sm font-medium text-gray-700">Score 阈值</span>
                      <el-tooltip content="相似度分数阈值" placement="top">
                        <el-icon size="14" color="#9ca3af">
                          <QuestionFilled />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <el-input 
                      v-model="settingsForm.similarityThreshold" 
                      placeholder="请输入" 
                      size="small" 
                      style="width: 80px"
                      @input="onScoreThresholdInputChange"
                    />
                    <el-slider 
                      v-model="settingsForm.similarityThresholdSlider" 
                      :min="0" 
                      :max="1" 
                      :step="0.1" 
                      class="flex-1" 
                      size="small"
                      @change="onScoreThresholdSliderChange"
                    />
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>-->
    </div>
     <el-button type="primary" size="large" :loading="saving" @click="saveSettings" class="px-8">保存</el-button>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getKnowledgeDetail, editKnowledge } from '@/api/knowledge'
import {
  Folder,
  Search,
  UserFilled,
  Connection,
  WarningFilled,
  Star,
  Coin,
  Grid,
  Document,
  Sort,
  CircleCheck,
  Minus,
  Plus,
  QuestionFilled
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  datasetId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update'])

const router = useRouter()
const saving = ref(false)
const deleting = ref(false)
const showDeleteDialog = ref(false)
const deleteConfirmText = ref('')
const indexMode = ref('high_quality')
const embeddingModel = ref('bge-reranker-v2-m3')

const settingsFormRef = ref(null)
const settingsForm = reactive({
  name: '',
  description: '',
  visibility: 'me',
  embeddingModel: '',
  topK: 10,
  similarityThreshold: 0.5,
  similarityThresholdSlider: 0.5,
  vectorSimilarityWeight: 0.7,
  chunkMethod: 'general',
  chunkFlag: 1
})

const formRules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 1, max: 50, message: '名称长度应在 1 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' }
  ],
  visibility: [
    { required: true, message: '请选择可见权限', trigger: 'change' }
  ]
}

// Methods
const increaseTopK = () => {
  if (settingsForm.topK < 20) {
    settingsForm.topK++
  }
}

const decreaseTopK = () => {
  if (settingsForm.topK > 1) {
    settingsForm.topK--
  }
}

const increaseScore = () => {
  if (settingsForm.similarityThresholdSlider < 1) {
    settingsForm.similarityThresholdSlider = Math.min(1, settingsForm.similarityThresholdSlider + 0.01)
    settingsForm.similarityThreshold = settingsForm.similarityThresholdSlider.toString()
  }
}

const decreaseScore = () => {
  if (settingsForm.similarityThresholdSlider > 0) {
    settingsForm.similarityThresholdSlider = Math.max(0, settingsForm.similarityThresholdSlider - 0.01)
    settingsForm.similarityThreshold = settingsForm.similarityThresholdSlider.toString()
  }
}

const onScoreThresholdInputChange = (value) => {
  const numValue = parseFloat(value)
  if (!isNaN(numValue) && numValue >= 0 && numValue <= 1) {
    settingsForm.similarityThresholdSlider = numValue
  }
}

const onScoreThresholdSliderChange = (value) => {
  settingsForm.similarityThreshold = value.toString()
}

const fetchKnowledgeDetail = async () => {
  saving.value = true
  try {
      const res = await getKnowledgeDetail({ id: props.datasetId })
      const data = res
      settingsForm.name = data.name || ''
      settingsForm.description = data.description || ''
      settingsForm.visibility = data.permission || 'me'
      settingsForm.embeddingModel = data.embeddingModel || ''
      settingsForm.topK = data.topK || 10
      settingsForm.similarityThreshold = data.similarityThreshold || 0.5
      settingsForm.vectorSimilarityWeight = data.vectorSimilarityWeight || 0.7
      settingsForm.chunkMethod = data.chunkMethod || 'general'
      settingsForm.chunkFlag = data.chunkFlag || 1

  } catch (error) {
    console.error('获取知识库详情失败:', error)
    ElMessage.error('获取知识库详情失败')
  } finally {
    saving.value = false
  }
}

const saveSettings = async () => {
  if (!settingsFormRef.value) {
    ElMessage.warning('表单未初始化')
    return
  }

  try {
    await settingsFormRef.value.validate()
    
    saving.value = true
    const params = {
      id: props.datasetId,
      name: settingsForm.name,
      description: settingsForm.description,
      permission: settingsForm.visibility,
      embeddingModel: settingsForm.embeddingModel,
      chunkMethod: settingsForm.chunkMethod,
      chunkFlag: settingsForm.chunkFlag,
      topK: settingsForm.topK,
      similarityThreshold: settingsForm.similarityThreshold
    }

    const res = await editKnowledge(params)
    
    if (res?.code === 200) {
      ElMessage.success('设置保存成功')
      emit('update')
    } else {
      ElMessage.error(res?.message || '保存失败')
    }
  } catch (error) {
    if (error.message) {
      // 表单验证错误
      console.log('表单验证失败:', error.message)
    } else {
      // API 调用错误
      console.error('保存设置失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saving.value = false
  }
}

const deleteKnowledgeBase = async () => {
  try {
    deleting.value = true
    
    // TODO: 实现实际的API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success('知识库已删除')
    showDeleteDialog.value = false
    
  } catch (error) {
    console.error('Delete failed:', error)
    ElMessage.error('删除失败，请重试')
  } finally {
    deleting.value = false
  }
}

const goToDocuments = () => {
  router.push({
    name: 'DatabaseDetail',
    params: { id: props.datasetId }
  })
}

onMounted(() => {
  fetchKnowledgeDetail()
})
</script>

<style scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-divider) {
  margin: 32px 0;
}

:deep(.el-select .el-input__wrapper) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__wrapper) {
  width: 100%;
}

:deep(.el-slider) {
  margin: 12px 0;
}

:deep(.el-slider__input) {
  width: 60px;
}

:deep(.el-card.border-2) {
  border-width: 2px;
}

:deep(.el-card) {
  --el-card-border-radius: 8px;
}

:deep(.el-radio.is-disabled .el-radio__label) {
  color: #606266;
}
</style> 