<template>
  <div class="flex min-h-screen bg-gray-50 ml-28">
    <!-- Sidebar -->
    <div class="w-64 bg-blue-50 border-r border-gray-200">
      <div class="p-4">
        <!-- Knowledge Base Header -->
        <div class="flex items-center mb-6 space-x-4">
          <div class="flex items-center space-x-2">
            <el-icon size="20" color="#3b82f6">
              <Folder />
            </el-icon>
            <span class="font-medium text-gray-900">个人知识库</span>
          </div>
        </div>
                    
        <!-- Navigation Menu -->
        <el-menu
          :default-active="activeMenu"
          class="border-none"
          @select="handleMenuSelect"
        >
          <el-menu-item index="documents">
            <el-icon><Document /></el-icon>
            <span>文档</span>
          </el-menu-item>
          <el-menu-item index="recall-test">
            <el-icon><Search /></el-icon>
            <span>召回测试</span>
          </el-menu-item>
          <!-- <el-menu-item index="settings">
            <el-icon><Setting /></el-icon>
            <span>设置</span>
          </el-menu-item> -->
        </el-menu>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1">
      <!-- Documents View -->
      <div v-if="activeMenu === 'documents'" class="p-6">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <div>
            <div class="flex items-center mb-2 space-x-2">
              <el-icon size="24" color="#3b82f6">
                <Folder />
              </el-icon>
              <h1 class="text-xl font-medium text-gray-900">文档</h1>
            </div>
            <p class="text-sm text-gray-600">
              知识库的所有文件都在这里显示。
            </p>
          </div>
          <el-button type="primary" :icon="Plus" @click="goToCreateDatabase">
            添加文档
          </el-button>
        </div>

        <!-- Search Bar -->
        <div class="mb-6">
          <el-input 
            v-model="searchQuery"
            placeholder="搜索"
            :prefix-icon="Search"
            style="width: 300px"
            clearable
          />
        </div>

        <!-- Empty State -->
        <div v-if="filteredDocuments.length === 0 && !loading" class="flex flex-col justify-center items-center py-20">
          <el-icon size="64" color="#d1d5db" class="mb-4">
            <FolderOpened />
          </el-icon>
          <h3 class="mb-2 text-lg font-medium text-gray-900">暂未创建个人知识库</h3>
          <p class="mb-6 max-w-md text-center text-gray-500">
            您可以上传文件，从网站同步，或者从网站应用程序(如GitHub等)同步。
          </p>
          <el-button type="primary" :icon="Plus" @click="handleAddDocument">
            上传文件
          </el-button>
        </div>
        
        <!-- Documents Table -->
        <div v-else>
          <el-table
            :data="filteredDocuments"
            style="width: 100%"
            :loading="loading"
            stripe
          >
            <el-table-column type="index" label="#" width="50" />
            
            <el-table-column prop="name" label="名称" min-width="250">
              <template #default="{ row }">
                <div class="flex items-center space-x-2">
                  <el-icon color="#3b82f6">
                    <Document />
                  </el-icon>
                  <span 
                    class="text-blue-600 cursor-pointer hover:underline" 
                    @click="goToFileDetail(row)"
                  >
                    {{ row.name }}
                  </span>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="chunkCount" label="分段数量" width="100" align="center">
              <template #default="{ row }">
                <el-link type="primary" :underline="false">{{ row.chunkCount }}</el-link>
              </template>
            </el-table-column>

            <el-table-column prop="size" label="字符数" width="100" align="center">
              <template #default="{ row }">
                {{ formatFileSize(row.size) }}
              </template>
            </el-table-column>

            <el-table-column prop="recallCount" label="召回次数" width="100" align="center" />

            <el-table-column prop="createTime" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>

            <el-table-column prop="updateTime" label="更新时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.updateTime) }}
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.status === '1' ? 'success' : 'warning'" size="small">
                  {{ row.status === '1' ? '可用' : '处理中' }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="启用" width="80" align="center">
              <template #default="{ row }">
                <el-switch
                  v-model="row.enabled"
                  @change="handleToggleEnable(row)"
                />
              </template>
            </el-table-column>

            <el-table-column label="操作" width="150" align="center">
              <template #default="{ row }">
                <el-button
                  type="text"
                  :icon="Setting"
                  @click="() => handleAction({ action: 'chunk-config', row })"
                  style="margin-right: 4px;"
                >
                  分段配置 |
                </el-button>
                <el-dropdown @command="handleAction">
                  <el-button type="text" :icon="MoreFilled" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{ action: 'download', row }">
                        <el-icon><Download /></el-icon>
                        <span class="ml-1">下载</span>
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'edit', row }">
                        <el-icon><Edit /></el-icon>
                        <span class="ml-1">编辑</span>
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'delete', row }" divided>
                        <el-icon><Delete /></el-icon>
                        <span class="ml-1">删除</span>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <!-- Recall Test View -->
      <template v-else-if="activeMenu === 'recall-test'">
        <recall-test
          v-if="isDataLoaded"
          :datasetIds="currentDatasetIds"
          :documentIds="[]"
        />
        <div v-else class="flex flex-col items-center justify-center h-[calc(100vh-64px)]">
          <el-empty description="正在加载知识库数据..." />
        </div>
      </template>

      <!-- Settings View (placeholder) -->
      <database-settings v-else-if="activeMenu === 'settings'" />
    </div>
  </div>

  <!-- 编辑文件对话框 -->
  <el-dialog
    v-model="showEditDialog"
    title="编辑文件"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form :model="editForm" label-width="80px">
      <el-form-item label="文件名称" required>
        <el-input 
          v-model="editForm.name"
          placeholder="请输入文件名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-end space-x-2">
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="submitEdit">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 分段配置对话框 -->
  <el-dialog
    v-model="showChunkConfigDialog"
    title="分段配置"
    width="90%"
    :close-on-click-modal="false"
    class="chunk-config-dialog"
  >
    <text-processing
      v-if="showChunkConfigDialog"
      :file-name="currentFile?.name"
      :dataset-id="databaseInfo.id"
      :file-ids="[currentFile?.id]"
      :initial-state="chunkConfigState"
      @prev-step="showChunkConfigDialog = false"
      @next-step="handleChunkConfigComplete"
      @state-update="handleChunkConfigUpdate"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watchEffect } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  Folder,
  Document,
  Search,
  Setting,
  Plus,
  FolderOpened,
  MoreFilled,
  QuestionFilled,
  ArrowLeft,
  Download,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { personKnowledge, downloadKnowledge, updateKnowledgeFile, deleteKnowledgeFile } from '@/api/knowledge'
import RecallTest from './components/RecallTest.vue'
import DatabaseSettings from './components/DatabaseSettings.vue'
import TextProcessing from './components/TextProcessing.vue'

const router = useRouter()
const route = useRoute()
const activeMenu = ref('documents')
const searchQuery = ref('')
const loading = ref(false)
const databaseInfo = ref({
  id: '',
  name: '个人知识库',
  description: ''
})

// 添加一个标志来控制组件显示
const isDataLoaded = ref(false)

// 监听数据变化
watchEffect(() => {
  if (databaseInfo.value && databaseInfo.value.id) {
    isDataLoaded.value = true
  }
})

// 编辑文件对话框
const showEditDialog = ref(false)
const editForm = reactive({
  id: '',
  name: '',
  datasetId: ''
})

// 分段配置相关
const showChunkConfigDialog = ref(false)
const currentFile = ref(null)
const chunkConfigState = reactive({
  segmentationType: 'general',
  retrievalMethod: 'hybrid',
  rerankEnabled: true,
  topK: 2,
  scoreThreshold: '',
  scoreThresholdSlider: 0.5,
  generalSettings: {
    separator: null,
    maxLength: 500,
    overlap: 50
  },
  preprocessingRules: {
    removeExtraSpaces: true,
    removeUrls: true
  }
})

// 文档数据
const documents = ref([])

// 根据搜索过滤文档
const filteredDocuments = computed(() => {
  if (!searchQuery.value) return documents.value
  return documents.value.filter(doc => 
    doc.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 计算 datasetIds
const currentDatasetIds = computed(() => {
  return databaseInfo.value?.id ? [databaseInfo.value.id] : []
})

// 获取个人知识库列表
const fetchPersonalKnowledgeList = async () => {
  loading.value = true
  isDataLoaded.value = false  // 重置加载状态
  try {
    const res = await personKnowledge({
      "name": "",
      "description": ""
    })
    if (res?.code === 200) {
      documents.value = (res.data?.files || []).map(item => ({
        ...item,
        recallCount: item.recallCount || 0, // 召回次数，默认为0
        updateTime: item.updateTime || item.createTime, // 更新时间，如果没有则使用创建时间
        enabled: item.enabled !== false // 启用状态，默认为true
      }))
      // 保存知识库ID和名称
      databaseInfo.value = {
        id: res.data?.datasetId || '',
        name: res.data?.name || '个人知识库',
        description: res.data?.description || ''
      }
    } else {
      ElMessage.error(res?.message || '获取个人知识库列表失败')
      documents.value = []
    }
  } catch (error) {
    console.error('获取个人知识库列表失败:', error)
    ElMessage.error(error?.message || '获取个人知识库列表失败')
    documents.value = []
  } finally {
    loading.value = false
  }
}

const handleMenuSelect = (index) => {
  console.log('Current Database Info:', databaseInfo.value)
  if (index === 'recall-test') {
    if (!databaseInfo?.value?.id) {
      ElMessage.warning('未获取到知识库ID，请稍后再试')
      return
    }
    console.log('Switching to recall-test with ID:', databaseInfo.value.id)
  }
  activeMenu.value = index
}

const handleAddDocument = () => {
  router.push({
    name: 'UploadFileStep',
    query: { 
      databaseId: databaseInfo.value.id,
      databaseName: databaseInfo.value.name
    }
  })
}

const handleAction = ({ action, row }) => {
  if (action === 'edit') {
    openEditDialog(row)
  } else if (action === 'delete') {
    ElMessageBox.confirm(
      '确定要删除这个文档吗？此操作不可恢复。',
      '删除确认',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      handleDelete(row)
    }).catch(() => {})
  } else if (action === 'download') {
    handleDownload(row)
  } else if (action === 'chunk-config') {
    currentFile.value = row
    showChunkConfigDialog.value = true
  }
}

// 打开编辑对话框
const openEditDialog = (row) => {
  editForm.id = row.id
  editForm.name = row.name
  editForm.datasetId = databaseInfo.value.id
  showEditDialog.value = true
}

// 提交编辑表单
const submitEdit = async () => {
  if (!editForm.name.trim()) {
    ElMessage.warning('请输入文件名称')
    return
  }

  try {
    const res = await updateKnowledgeFile(editForm)
    if (res?.code === 200) {
      ElMessage.success('编辑成功')
      showEditDialog.value = false
      fetchPersonalKnowledgeList() // 刷新列表
    } else {
      ElMessage.error(res?.msg || '编辑失败')
    }
  } catch (error) {
    console.error('编辑文件失败:', error)
    ElMessage.error('编辑失败')
  }
}

// 删除文件
const handleDelete = async (row) => {
  try {
    const res = await deleteKnowledgeFile({
      datasetId: databaseInfo.value.id,
      ids: [row.id]
    })
    
    if (res?.code === 200) {
      ElMessage.success('删除成功')
      fetchPersonalKnowledgeList() // 刷新列表
    } else {
      ElMessage.error(res?.msg || '删除失败')
    }
  } catch (error) {
    console.error('删除文件失败:', error)
    ElMessage.error('删除失败')
  }
}

// 处理启用开关变化
const handleToggleEnable = (row) => {
  console.log('Toggle enable for:', row.name, row.enabled)
  // 这里可以调用API来更新文件的启用状态
  // 目前暂时只做本地状态更新
}

// 下载文件
const handleDownload = async (row) => {
  try {
    const res = await downloadKnowledge({
      datasetId: databaseInfo.value.id,
      id: row.id
    })
    
    // 检查响应是否成功
    if (!res) {
      throw new Error('下载失败，未收到文件数据')
    }

    // 创建 Blob 对象
    const blob = new Blob([res], { 
      type: 'application/octet-stream'
    })
    
    // 创建下载链接
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(blob)
    downloadLink.download = row.name // 使用原始文件名
    
    // 触发下载
    document.body.appendChild(downloadLink)
    downloadLink.click()
    
    // 清理
    document.body.removeChild(downloadLink)
    URL.revokeObjectURL(downloadLink.href)
    
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error(error.message || '下载失败')
  }
}

// 处理分段配置更新
const handleChunkConfigUpdate = (newState) => {
  Object.assign(chunkConfigState, newState)
}

// 处理分段配置完成
const handleChunkConfigComplete = () => {
  showChunkConfigDialog.value = false
  ElMessage.success('分段配置已更新')
  fetchPersonalKnowledgeList() // 刷新文件列表
}

const formatFileSize = (size) => {
  if (!size) return '0 B'
  const num = parseInt(size)
  if (num < 1024) return num + ' B'
  if (num < 1024 * 1024) return (num / 1024).toFixed(1) + ' KB'
  if (num < 1024 * 1024 * 1024) return (num / (1024 * 1024)).toFixed(1) + ' MB'
  return (num / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
}

const formatDate = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(Number(timestamp))
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const goToFileDetail = (row) => {
  router.push({ 
    name: 'FileDetail',
    query: { 
      documentId: row.id,
      datasetId: databaseInfo.value.id,
      documentName: row.name,
      datasetName: databaseInfo.value.name
    }
  })
}

const goToCreateDatabase = () => {
  router.push({
    name: 'UploadFileStep',
    query: { 
      databaseId: databaseInfo.value.id,
      databaseName: databaseInfo.value.name
    }
  })
}

onMounted(async () => {
  await fetchPersonalKnowledgeList()
})
</script>

<style scoped>
:deep(.el-menu) {
  border-right: none;
  background-color: #eff6ff !important; /* 浅蓝色背景 bg-blue-50 */
}

:deep(.el-menu-item) {
  border-radius: 6px;
  background-color: #eff6ff !important; /* 浅蓝色背景 bg-blue-50 */
}

:deep(.el-menu-item.is-active) {
  background-color: #3b82f6 !important; /* 更柔和的蓝色 bg-blue-500 */
  color: white !important;
}

:deep(.el-menu-item:hover) {
  background-color: #bfdbfe !important; /* 鼠标悬停时的颜色 bg-blue-200 */
}

:deep(.el-menu-item.is-active:hover) {
  background-color: #3b82f6 !important; /* 选中状态下保持相同的蓝色 */
}

:deep(.el-table .el-table__row:hover > td) {
  background-color: #f8faff;
}

:deep(.el-link) {
  font-weight: normal;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
}

:deep(.chunk-config-dialog .el-dialog__body) {
  padding: 0;
  height: 600px;
  overflow: hidden;
}

:deep(.chunk-config-dialog .el-dialog__header) {
  margin-right: 0;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.chunk-config-dialog .el-dialog) {
  margin-top: 5vh !important;
}
</style>