<template>
  <div class="min-h-screen bg-gray-50 ml-28">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200">
      <div class="flex justify-between items-center px-8 mx-auto max-w-full h-16">
        <!-- 左侧 Tab -->
        <div class="flex items-center space-x-8">
          <button
            class="px-2 pb-1 text-base font-medium border-b-2"
            :class="currentTab === 'knowledge' ? 'text-blue-600 border-blue-600' : 'text-gray-500 border-transparent hover:text-gray-700'"
            @click="switchTab('knowledge')"
          >
            知识库
          </button>
        </div>
        <!-- 右侧操作区 -->
        <div class="flex items-center space-x-3">
          <!-- 标签下拉 -->
          <el-select
            v-model="selectedTags"
            multiple
            filterable
            clearable
            placeholder="全部标签"
            class="w-40"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag.dictCode"
              :label="tag.dictLabel"
              :value="tag.dictValue"
            />
          </el-select>
          <!-- 关键字搜索 -->
          <el-input
            v-model="searchKeyword"
            placeholder="搜索"
            prefix-icon="Search"
            class="w-56"
            clearable
          />
        </div>
      </div>
    </header>

    <!-- Tab内容区 -->
    <div v-if="currentTab === 'knowledge'">
      <!-- 知识库内容 -->
      <main class="px-4 py-8 mx-auto max-w-full">
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          <!-- 创建知识库卡片 -->
          <div
            class="flex flex-col h-[200px] bg-white rounded-[10px] border border-[#E4E7ED] shadow-sm transition-shadow duration-200 px-6 pt-5 pb-4 m-2 cursor-pointer hover:shadow-md"
            @click="onCreateKnowledge"
          >
            <div class="flex flex-col flex-1 items-start">
              <div class="flex items-center justify-center w-10 h-10 mb-2 bg-[#F2F6FC] rounded-full">
                <svg class="w-6 h-6 text-[#596DF4]" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path d="M12 5v14m7-7H5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="mb-2 text-base font-semibold text-gray-900">创建知识库</div>
              <div class="mb-2 text-xs text-gray-500">
                导入您自己的文本数据或通过 Webhook 实时写入数据以增强 LLM 的上下文。
              </div>
            </div>
            <div>
            </div>
          </div>
          <!-- Knowledge Base Cards -->
          <div 
            v-for="item in knowledgeItems" 
            :key="item.id"
            class="flex flex-col h-[200px] bg-white rounded-[10px] border border-[#E4E7ED] shadow-sm hover:shadow-md transition-shadow duration-200 px-6 pt-5 pb-4 m-2 cursor-pointer"
            @click="goToDetail(item)"
          >
            <!-- Header with icon and title -->
            <div class="overflow-hidden flex-1 p-0">
              <div class="flex items-start mb-2 space-x-3">
                <div class="flex-shrink-0">
                  <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="mb-1 text-base font-medium leading-tight text-gray-900">{{ item.name }}</h3>
                  <!-- <div class="mb-1 text-xs text-gray-500">
                    {{ item.document_count }} 文档 · {{ Math.floor(item.word_count / 1000) }} 千字符 · {{ item.app_count }} 次使用
                  </div> -->
                </div>
              </div>

              <!-- Description -->
              <div v-if="item.description" class="h-[36px]">
                <p class="text-xs text-gray-600 line-clamp-2">{{ item.description }}</p>
              </div>
            </div>

            <!-- Footer -->
            <div class="flex justify-between items-center pt-3 border-t border-[#F2F6FC] mt-2">
              <!-- Tags Section -->
              <div class="flex items-center flex-1 space-x-2">
                <div class="relative">
                  <el-button 
                    @click="toggleTagDropdown(item.id)"
                    @click.stop
                    class="flex items-center px-0 space-x-1 text-xs text-gray-500 hover:text-gray-700"
                    text
                  >
                    <el-icon><PriceTag /></el-icon>
                    <span>添加标签</span>
                  </el-button>
                
                  <!-- Tag Dropdown -->
                  <div 
                    v-if="activeTagDropdown === item.id"
                    class="absolute left-0 top-full z-10 mt-2 w-64 bg-white rounded-lg border border-gray-200 shadow-lg"
                    @click.stop
                  >
                    <div class="p-3" @click.stop>
                      <!-- Available Tags -->
                      <div class="space-y-2" @click.stop>
                        <el-checkbox-group 
                          v-model="item.selectedLabels" 
                          @change="onTagSelectionChange(item, $event)"
                        >
                          <el-checkbox 
                            v-for="tag in availableTags" 
                            :key="tag.dictCode"
                            :label="tag.dictValue"
                            class="flex items-center space-x-2"
                            @click.stop
                          >
                            <span class="text-sm text-gray-700">{{ tag.dictLabel }}</span>
                          </el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Selected Labels Display -->
                <div v-if="item.labels && item.labels.length > 0" class="flex flex-wrap gap-1 ml-2">
                  <el-tag 
                    v-for="label in item.labels" 
                    :key="label.labelId || label.dictCode"
                    size="small"
                    class="transparent-tag"
                  >
                    {{ label.dictLabel || label.labelName }}
                  </el-tag>
                </div>
              </div>

              <!-- More Actions Menu -->
              <div class="relative">
                <el-button 
                  @click="toggleMoreMenu(item.id)"
                  @click.stop
                  class="p-1 text-gray-400 hover:text-gray-600"
                  text
                >
                  <el-icon><More /></el-icon>
                </el-button>
                
                <!-- More Menu Dropdown -->
                <div 
                  v-if="activeMoreMenu === item.id"
                  class="absolute right-0 top-full z-10 mt-2 w-32 bg-white rounded-lg border border-gray-200 shadow-lg"
                >
                  <div class="py-1">
                    <el-button 
                      @click="openSettings(item)"
                      @click.stop
                      class="flex justify-start items-center px-2 py-2 w-full text-xs"
                      text
                    >
                      <el-icon class="mr-1"><Edit /></el-icon>
                      <span>编辑</span>
                    </el-button>
                    <el-button 
                      @click="deleteKnowledgeBase(item.id)"
                      @click.stop
                      class="flex justify-start items-center px-2 py-2 w-full text-xs"
                      text
                      :style="{ marginLeft: 0 }"
                    >
                      <el-icon class="mr-1"><Delete /></el-icon>
                      <span class="text-red-600">删除</span>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="flex justify-center mt-8">
          <el-pagination
            v-model:current-page="pagination.pageNo"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[12, 24, 36, 48]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </main>



      <!-- Create Knowledge Base Dialog -->
      <el-dialog
        v-model="showCreateKnowledgeDialog"
        :title="dialogTitle"
        width="700px"
        :show-close="true"
        center
      >
        <div class="mb-6">
          <p class="text-sm leading-relaxed text-gray-600">
            您可以创建空知识库，或者直接上传文档来创建知识库。
          </p>
        </div>
        
        <el-form :model="createKnowledgeForm" label-position="top">
          <!-- 显示归属部门和创建人信息（仅在编辑时显示） -->
          <div v-if="isEdit" class="mb-4 p-4 bg-gray-50 rounded-lg border">
            <div class="text-sm text-gray-600 mb-3">知识库信息</div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-center space-x-2">
                <el-icon><OfficeBuilding /></el-icon>
                <span class="text-sm text-gray-700">归属部门:</span>
                <span class="text-sm font-medium">{{ knowledgeDetail.deptName }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <el-icon><User /></el-icon>
                <span class="text-sm text-gray-700">创建人:</span>
                <span class="text-sm font-medium">{{ knowledgeDetail.createUser?.nickName || knowledgeDetail.createUser?.userName || '未知' }}</span>
              </div>
            </div>
          </div>
          
          <el-form-item label="知识库名称" required>
            <el-input
              v-model="createKnowledgeForm.name"
              placeholder="请输入知识库名称"
              size="large"
            />
          </el-form-item>

          <el-form-item label="知识库描述">
            <el-input
              v-model="createKnowledgeForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入知识库描述（可选）"
              :maxlength="65535"
              show-word-limit
            />
          </el-form-item>

          <!-- Embedding Model -->
          <el-form-item label="Embedding 模型">
            <el-select v-model="createKnowledgeForm.embeddingModel" style="width: 100%" placeholder="请选择Embedding模型">
              <el-option
                value="bge-m3@Xinference"
                label="bge-m3@Xinference"
              >
                <div class="flex items-center space-x-2">
                  <el-icon size="20" color="#3b82f6"><Avatar /></el-icon>
                  <span>bge-m3@Xinference</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <!-- Permission -->
          <!--<el-form-item label="权限设置">
            <el-select v-model="createKnowledgeForm.permission" style="width: 100%" placeholder="请选择权限">
              <el-option value="me" label="个人">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Lock /></el-icon>
                  <span>个人</span>
                </div>
              </el-option>
              <el-option value="team" label="部门团队">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><View /></el-icon>
                  <span>部门团队</span>
                </div>
              </el-option>
              <el-option value="public" label="公共知识库">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Connection /></el-icon>
                  <span>公共知识库</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>-->

          <!-- Chunk Method -->
          <!--<el-form-item label="分块方法">
            <el-select v-model="createKnowledgeForm.chunkMethod" style="width: 100%" placeholder="请选择分块方法">
              <el-option value="naive" label="不分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>不分块</span>
                </div>
              </el-option>
              <el-option value="book" label="按书块分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>按书块分块</span>
                </div>
              </el-option>
              <el-option value="email" label="按邮件分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>按邮件分块</span>
                </div>
              </el-option>
              <el-option value="laws" label="按法律法规分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>按法律法规分块</span>
                </div>
              </el-option>
              <el-option value="manual" label="手动分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>手动分块</span>
                </div>
              </el-option>
              <el-option value="one" label="按文件分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>按文件分块</span>
                </div>
              </el-option>
              <el-option value="paper" label="按论文分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>按论文分块</span>
                </div>
              </el-option>
              <el-option value="picture" label="按图片分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>按图片分块</span>
                </div>
              </el-option>
              <el-option value="presentation" label="按演讲分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>按演讲分块</span>
                </div>
              </el-option>
              <el-option value="qa" label="按问答分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>按问答分块</span>
                </div>
              </el-option>
              <el-option value="table" label="按表格分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>按表格分块</span>
                </div>
              </el-option>
              <el-option value="tag" label="按标签分块">
                <div class="flex items-center space-x-2">
                  <el-icon size="20"><Document /></el-icon>
                  <span>按标签分块</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>-->
        </el-form>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <el-button @click="closeCreateKnowledgeDialog">取消</el-button>
            <el-button 
              type="primary" 
              @click="submitForm"
              :disabled="!createKnowledgeForm.name.trim()"
              :loading="creating"
            >
              {{ isEdit ? '更新知识库' : '创建知识库' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- Click outside to close dropdowns -->
      <div 
        v-if="activeTagDropdown || activeMoreMenu"
        @click="closeAllDropdowns"
        class="fixed inset-0 z-5"
      ></div>
    </div>
    <div v-else>
      <!-- API子页面占位 -->
      <div class="flex justify-center items-center h-96 text-xl text-gray-400">API 页面内容占位</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { 
  PriceTag, 
  More, 
  Edit, 
  Delete, 
  View, 
  ArrowRight,
  UploadFilled,
  Avatar,
  Grid,
  QuestionFilled,
  Document,
  Lock,
  Connection,
  OfficeBuilding,
  User
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getKnowledgeModelList,
  addKnowledge, 
  editKnowledge, 
  deleteKnowledge,
  getKnowledgeDetail 
} from '@/api/knowledge'
import { 
  getLabelByType,
  saveLabel,
  getLabelList 
} from '@/api/label'
import { useUserStore } from '@/stores/user'

// Router
const router = useRouter()
const userStore = useUserStore()

// Reactive data
const currentTab = ref('knowledge')
const selectedTags = ref([])
const searchKeyword = ref('')
const showApiDialog = ref(false)
const apiForm = reactive({ name: '', endpoint: '', key: '' })
const externalApis = ref([
  { id: 1, name: 'RAGFlow-API', endpoint: 'http://**********:9380/api/v1/dify' }
])
const activeTagDropdown = ref(null)
const activeMoreMenu = ref(null)

// 创建知识库相关数据
const showCreateKnowledgeDialog = ref(false)
const creating = ref(false)
const dialogTitle = ref('创建知识库')
const isEdit = ref(false)
const createKnowledgeForm = reactive({
  id: '',
  name: '',
  avatar: '',
  description: '',
  embeddingModel: 'bge-m3@Xinference',
  permission: 'team',
  chunkMethod: 'naive'
})

// 知识库详情信息（用于显示归属部门和创建人）
const knowledgeDetail = reactive({
  deptName: '',
  createUser: null,
  createTime: null
})

const editingItem = reactive({
  id: '',
  name: '',
  description: ''
})

const knowledgeItems = ref([])

const availableTags = ref([])

// 分页和搜索相关数据
const pagination = reactive({
  pageNo: 1,
  pageSize: 12,
  total: 0
})

const searchForm = reactive({
  name: ''
})

// Methods
const switchTab = (tab) => {
  currentTab.value = tab
}

const toggleTagDropdown = (itemId) => {
  if (activeTagDropdown.value === itemId) {
    activeTagDropdown.value = null
  } else {
    activeTagDropdown.value = itemId
    // 确保标签数据已加载
    if (availableTags.value.length === 0) {
      getAvailableTags()
    }
  }
  activeMoreMenu.value = null
}

const toggleMoreMenu = (itemId) => {
  activeMoreMenu.value = activeMoreMenu.value === itemId ? null : itemId
  activeTagDropdown.value = null
}

const closeAllDropdowns = () => {
  activeTagDropdown.value = null
  activeMoreMenu.value = null
}

// 获取标签数据
const getAvailableTags = async () => {
  try {
    const res = await getLabelByType({ dictType: 'sys_knowledge_base_label' })
    console.log('获取标签响应:', res)
    if (res.code === 200) {
      availableTags.value = res.data || []
      console.log('可用标签:', availableTags.value)
    } else {
      console.error('获取标签失败:', res.msg)
      ElMessage.error(res.msg || '获取标签失败')
    }
  } catch (error) {
    console.error('获取标签失败:', error)
    ElMessage.error('获取标签失败，请检查网络连接')
  }
}

// 标签选择变化时保存
const onTagSelectionChange = async (item, selectedValues) => {
  try {
    console.log('标签选择变化:', selectedValues, item.id)
    
    // 获取选中的标签对应的dictCode
    const selectedLabelIds = selectedValues.map(labelValue => {
      const tag = availableTags.value.find(t => t.dictValue === labelValue)
      return tag ? tag.dictCode : null
    }).filter(id => id !== null)

    console.log('选中的标签IDs:', selectedLabelIds)

    const res = await saveLabel({
      baseId: item.id,
      labelIds: selectedLabelIds
    })
    
    console.log('保存标签响应:', res)
    
    if (res.code === 200) {
      ElMessage.success('标签保存成功')
      // 更新本地显示的标签
      if (res.data && Array.isArray(res.data)) {
        item.labels = res.data
      } else {
        // 如果API没有返回标签数据，根据选中的值构造标签数据
        item.labels = selectedValues.map(labelValue => {
          const tag = availableTags.value.find(t => t.dictValue === labelValue)
          return {
            dictCode: tag?.dictCode,
            dictValue: tag?.dictValue,
            dictLabel: tag?.dictLabel,
            labelId: tag?.dictCode
          }
        }).filter(label => label.dictCode)
      }
    } else {
      ElMessage.error(res.msg || '标签保存失败')
    }
  } catch (error) {
    console.error('保存标签失败:', error)
    ElMessage.error('标签保存失败，请重试')
  }
}



const openSettings = async (item) => {
  isEdit.value = true
  dialogTitle.value = '编辑知识库'
  Object.assign(createKnowledgeForm, {
    id: item.id,
    name: item.name,
    avatar: item.avatar || '',
    description: item.description || '',
    embeddingModel: item.embeddingModel || 'bge-m3@Xinference',
    permission: item.permission || 'team',
    chunkMethod: item.chunkMethod || 'naive'
  })
  
  // 获取知识库详情信息
  if (item.id) {
    try {
      const res = await getKnowledgeDetail({ id: item.id })
      if (res) {
        Object.assign(knowledgeDetail, {
          deptName: res.dept.deptName || '',
          createUser: res.createUser || null,
          createTime: res.createTime || null
        })
      }
    } catch (error) {
      console.error('获取知识库详情失败:', error)
    }
  }
  
  showCreateKnowledgeDialog.value = true
  closeAllDropdowns()
}

const submitForm = async () => {
  if (!createKnowledgeForm.name.trim()) {
    ElMessage.warning('请输入知识库名称')
    return
  }
  
  creating.value = true
  try {
    let res
    if (isEdit.value) {
      res = await editKnowledge(createKnowledgeForm)
    } else {
      res = await addKnowledge(createKnowledgeForm)
    }

    if (res.code === 200) {
      ElMessage.success(isEdit.value ? '编辑成功！' : '创建成功！')
      closeCreateKnowledgeDialog()
      getKnowledgeList()
    } else {
      ElMessage.error(res.msg || (isEdit.value ? '编辑失败' : '创建失败'))
    }
  } catch (error) {
    console.error(isEdit.value ? '编辑知识库失败:' : '创建知识库失败:', error)
    ElMessage.error(isEdit.value ? '编辑失败，请重试' : '创建失败，请重试')
  } finally {
    creating.value = false
  }
}

const closeCreateKnowledgeDialog = () => {
  showCreateKnowledgeDialog.value = false
  resetCreateKnowledgeForm()
  resetKnowledgeDetail()
}

const resetCreateKnowledgeForm = () => {
  Object.assign(createKnowledgeForm, {
    id: '',
    name: '',
    avatar: '',
    description: '',
    embeddingModel: 'bge-m3@Xinference',
    permission: 'team',
    chunkMethod: 'naive'
  })
}

const resetKnowledgeDetail = () => {
  Object.assign(knowledgeDetail, {
    deptName: '',
    createUser: null,
    createTime: null
  })
}

const deleteKnowledgeBase = async (itemId) => {
  try {
    const confirmed = await ElMessageBox.confirm('确定要删除这个知识库吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    if (confirmed) {
      const res = await deleteKnowledge({ ids: [itemId] })
      if (res.code === 200) {
        ElMessage.success('删除成功')
        getKnowledgeList() // 刷新列表
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除知识库失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
  closeAllDropdowns()
}

const onCreateKnowledge = () => {
  isEdit.value = false
  dialogTitle.value = '创建知识库'
  resetCreateKnowledgeForm()
  showCreateKnowledgeDialog.value = true
}

const goToDetail = (item) => {
  router.push({name: 'DatabaseDetail', query: { id: item.id, name: item.name }})
}

// 监听搜索关键字变化
watch(searchKeyword, (newVal) => {
  searchForm.name = newVal
  pagination.pageNo = 1 // 重置页码
  getKnowledgeList()
})

// 监听标签选择变化
watch(selectedTags, () => {
  pagination.pageNo = 1 // 重置页码
  getKnowledgeList()
})

// 获取知识库列表
const getKnowledgeList = async () => {
  try {
    const params = {
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      name: searchForm.name,
      permission:'team',
      deptId: userStore.userInfo?.deptId
    }
    
    // 如果有选中的标签，添加标签筛选参数
    if (selectedTags.value && selectedTags.value.length > 0) {
      params.labelValues = selectedTags.value.join(',')
    }
    
    const res = await getKnowledgeModelList(params)
    if (res.code === 200) {
      // 处理知识库数据，添加标签相关字段
      knowledgeItems.value = (res.rows || []).map(item => {
        console.log('知识库项目:', item.name, '标签:', item.labels)
        return {
          ...item,
          labels: item.labels || [], // 知识库已有的标签
          selectedLabels: item.labels ? item.labels.map(label => 
            label.dictValue || label.labelValue || label.dictLabel
          ).filter(Boolean) : [] // 用于checkbox选择的标签值
        }
      })
      pagination.total = res.total || 0
      console.log('处理后的知识库数据:', knowledgeItems.value)
    } else {
      ElMessage.error(res.msg || '获取知识库列表失败')
    }
  } catch (error) {
    console.error('获取知识库列表失败:', error)
    ElMessage.error('获取知识库列表失败')
  }
}

// 处理页码变化
const handleCurrentChange = (val) => {
  pagination.pageNo = val
  getKnowledgeList()
}

// 处理每页条数变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.pageNo = 1
  getKnowledgeList()
}

// 在组件挂载时获取列表和标签数据
onMounted(() => {
  getAvailableTags()
  getKnowledgeList()
})
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

:deep(.el-dialog__body) {
  padding-top: 20px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-upload-dragger) {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background-color: #fafafa;
  text-align: center;
  transition: all 0.3s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #3b82f6;
  background-color: #f8faff;
}

:deep(.el-radio__label) {
  padding-left: 0;
}

:deep(.el-radio__input) {
  margin-right: 8px;
}

:deep(.el-slider) {
  margin: 0;
}

:deep(.el-input-number.is-small) {
  width: 80px;
}

:deep(.el-dialog .el-slider) {
  margin: 0;
  height: 32px;
}

:deep(.el-dialog .el-switch) {
  --el-switch-core-border-radius: 10px;
}

:deep(.el-tag--primary) {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
  font-weight: 500;
}

.transparent-tag {
  background-color: transparent !important;
  border: 1px solid #d1d5db;
  color: #6b7280;
  font-size: 11px;
  transition: all 0.2s ease;
}

.transparent-tag:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}
</style>