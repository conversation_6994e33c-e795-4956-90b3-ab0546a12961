<template>
  <div class="min-h-screen bg-gray-50 ml-28">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200">
      <div class="px-6 py-4 mx-auto max-w-6xl">
        <div class="flex justify-between items-center">
          <!-- Left side - Back button and title -->
          <div class="flex items-center space-x-4">
            <el-button 
              :icon="ArrowLeft" 
              circle 
              size="small"
              @click="goBack"
            />
            <h1 class="text-xl font-medium text-gray-900">上传文件</h1>
          </div>
          
          <!-- Right side - Steps -->
          <div class="flex items-center">
            <div class="flex items-center space-x-2">
              <el-tag 
                v-if="currentStep === 1"
                type="primary" 
                size="small"
              >STEP 1</el-tag>
              <span 
                class="text-sm font-medium"
                :class="currentStep === 1 ? 'text-blue-600' : 'text-gray-400'"
              >选择数据源</span>
            </div>
            
            <div class="mx-4 w-8 h-px bg-gray-300"></div>
            
            <div class="flex items-center space-x-2">
              <el-tag 
                v-if="currentStep === 2"
                type="primary" 
                size="small"
              >STEP 2</el-tag>
              <span 
                class="flex justify-center items-center w-6 h-6 text-sm rounded-full"
                :class="currentStep === 2 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 text-gray-500'"
              >2</span>
              <span 
                class="text-sm"
                :class="currentStep === 2 ? 'text-blue-600 font-medium' : 'text-gray-400'"
              >文本分段与清洗</span>
            </div>
            
            <div class="mx-4 w-8 h-px bg-gray-300"></div>
            
            <div class="flex items-center space-x-2">
              <el-tag 
                v-if="currentStep === 3"
                type="primary" 
                size="small"
              >STEP 3</el-tag>
              <span 
                class="flex justify-center items-center w-6 h-6 text-sm rounded-full"
                :class="currentStep === 3 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 text-gray-500'"
              >3</span>
              <span 
                class="text-sm"
                :class="currentStep === 3 ? 'text-blue-600 font-medium' : 'text-gray-400'"
              >处理并完成</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div v-if="currentStep === 1" class="px-6 py-8 mx-auto max-w-4xl">
      <!-- Data Source Selection -->
      <div class="mb-8">
        <h2 class="mb-6 text-lg font-medium text-gray-900">选择数据源</h2>
        <div class="grid grid-cols-1 gap-4 mb-8 md:grid-cols-3">
          <!-- Import Text -->
          <div 
            class="p-6 bg-blue-50 rounded-lg border-2 border-blue-500 transition-colors cursor-pointer hover:bg-blue-100"
            :class="{ 'border-blue-500 bg-blue-50': selectedDataSource === 'text' }"
            @click="selectedDataSource = 'text'"
          >
            <div class="flex items-center space-x-3">
              <el-icon size="24" color="#3b82f6">
                <Document />
              </el-icon>
              <span class="font-medium text-gray-900">导入已有文本</span>
            </div>
          </div>
        </div>
      </div>

      <!-- File Upload Section -->
      <div class="mb-8">
        <h3 class="mb-4 text-base font-medium text-gray-900">上传文本文件</h3>
        
        <el-upload
          class="upload-demo"
          drag
          :auto-upload="false"
          multiple
          :show-file-list="true"
          :file-list="uploadFiles"
          accept=".txt,.md,.pdf,.docx,.xlsx,.csv,.doc"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          ref="uploadRef"
        >
          <div class="py-12">
            <el-icon size="48" color="#c0c4cc" class="mb-4">
              <UploadFilled />
            </el-icon>
            <div class="mb-2 text-gray-600">
              拖拽文件至此，或者 
              <span class="text-blue-600 cursor-pointer hover:underline">选择文件</span>
            </div>
            <div class="text-sm text-gray-400">
              已支持 TXT、MARKDOWN、MDX、PDF、HTML、XLSX、XLS、DOCX、CSV、MD、HTM 等个文件不
              超过 50MB。
            </div>
          </div>
        </el-upload>

        <!-- 显示已上传的文件列表 -->
        <div v-if="hasFiles" class="mt-4">
          <div class="mb-2 text-sm text-gray-500">已选择的文件：</div>
          <div class="space-y-2">
            <div v-for="file in uploadFiles" :key="file.uid" class="flex justify-between items-center p-2 bg-gray-50 rounded">
              <div class="flex items-center space-x-2">
                <el-icon color="#3b82f6"><Document /></el-icon>
                <span class="text-sm">{{ file.name }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <template v-if="fileUploadStatus[file.uid] === 'uploading'">
                  <div class="w-20">
                    <el-progress 
                      :percentage="fileUploadProgress[file.uid]" 
                      :show-text="false"
                      :stroke-width="4"
                    />
                  </div>
                  <span class="text-xs text-gray-500">{{ fileUploadProgress[file.uid] }}%</span>
                </template>
                <template v-else>
                  <el-icon v-if="fileUploadStatus[file.uid] === 'success'" color="#10B981">
                    <CircleCheck />
                  </el-icon>
                  <el-icon v-else-if="fileUploadStatus[file.uid] === 'error'" color="#EF4444">
                    <CircleClose />
                  </el-icon>
                  <el-icon v-else color="#9CA3AF">
                    <Clock />
                  </el-icon>
                </template>
              </div>
            </div>
          </div>
        </div>

        <!-- 上传按钮 -->
        <div v-if="hasFiles" class="flex justify-center mt-8 space-x-4">
          <el-button 
            type="primary" 
            :loading="uploading"
            @click="handleUpload"
          >
            上传文件
          </el-button>
          <el-button 
            type="success" 
            v-if="allFilesUploaded"
            @click="currentStep = 2"
          >
            下一步
          </el-button>
        </div>
      </div>
    </div>

    <!-- Step 2: Text Processing -->
    <text-processing
      v-else-if="currentStep === 2"
      :file-name="uploadedFileNames"
      :dataset-id="route.query.databaseId"
      :file-ids="uploadedFileIds"
      :initial-state="textProcessingState"
      @state-update="handleTextProcessingStateUpdate"
      @prev-step="currentStep = 1"
      @next-step="currentStep = 3"
    />

    <!-- Step 3: Processing Complete -->
    <processing-complete
      :databaseId="route.query.databaseId"
      v-else-if="currentStep === 3"
      :file-ids="uploadedFileIds"
      :file-names="uploadedFileNames"
      :database-name="route.query.databaseName"
      @prev-step="currentStep = 2"
    />

  </div>
</template>

<script setup>
import { ref, onMounted, reactive, watch, onUnmounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, 
  Document, 
  UploadFilled, 
  Plus,
  Avatar,
  Grid,
  QuestionFilled,
  CircleCheck,
  CircleClose,
  Clock
} from '@element-plus/icons-vue'
import { uploadKnowledgeFile,createFileChunk,uploadKnowledgeFileSingle,parseFile } from '@/api/knowledge'
import TextProcessing from './components/TextProcessing.vue'
import ProcessingComplete from './components/ProcessingComplete.vue'

// Router
const router = useRouter()
const route = useRoute()

// Reactive data
const currentStep = ref(1)
const selectedDataSource = ref('text')
const hasFiles = ref(false)
const uploadedFileNames = ref([])
const showCreateEmptyDialog = ref(false)
const uploading = ref(false)
const uploadRef = ref(null)
const uploadFiles = ref([])
const uploadedFileIds = ref([])

// 添加文件上传状态和进度
const fileUploadStatus = ref({}) // 记录每个文件的上传状态：'pending', 'uploading', 'success', 'error'
const fileUploadProgress = ref({}) // 记录每个文件的上传进度

// 计算属性：检查是否所有文件都上传成功
const allFilesUploaded = computed(() => {
  if (uploadFiles.value.length === 0) {
    return false
  }
  return uploadFiles.value.every(file => fileUploadStatus.value[file.uid] === 'success')
})

// 保存文本处理步骤的状态
const textProcessingState = reactive({
  segmentationType: 'general',
  retrievalMethod: 'hybrid',
  rerankEnabled: true,
  topK: 2,
  scoreThreshold: '',
  scoreThresholdSlider: 0.5,
  generalSettings: {
    separator: '\\n\\n,\\n',
    maxLength: 500,
    overlap: 50
  },
  preprocessingRules: {
    removeExtraSpaces: true,
    removeUrls: true
  }
})

// 监听步骤变化，当返回步骤1时恢复上传组件的状态
watch(currentStep, (newStep) => {
  if (newStep === 1 && uploadRef.value) {
    // 恢复已上传的文件到上传组件
    uploadFiles.value.forEach(file => {
      uploadRef.value.handleStart(file.raw)
    })
  }
})

// Methods
const goBack = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  } else {
    // 使用浏览器的返回功能，或者返回到个人知识库
    if (window.history.length > 1) {
      router.go(-1)
    } else {
      router.push({ name: 'PersonalDatabase' })
    }
  }
}

const handleFileChange = (file) => {
  // 当文件被选择时，将文件添加到列表中
  if (file.raw) {
    // 检查文件是否已存在
    const existingFile = uploadFiles.value.find(f => f.uid === file.uid)
    if (!existingFile) {
    uploadFiles.value.push(file)
      uploadedFileNames.value.push(file.name)
      // 初始化文件状态为待上传
      fileUploadStatus.value[file.uid] = 'pending'
  }
    hasFiles.value = uploadFiles.value.length > 0
  }
}

const handleFileRemove = (file) => {
  // 当文件被移除时，从列表中删除
  const index = uploadFiles.value.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    // 从uploadFiles中移除
    uploadFiles.value.splice(index, 1)
    // 从uploadedFileNames中移除
    uploadedFileNames.value.splice(index, 1)
    
    // 如果文件已经成功上传，从uploadedFileIds中移除对应的ID
    if (fileUploadStatus.value[file.uid] === 'success' && file.response?.data?.[0]?.id) {
      const fileId = file.response.data[0].id
      const idIndex = uploadedFileIds.value.indexOf(fileId)
      if (idIndex > -1) {
        uploadedFileIds.value.splice(idIndex, 1)
      }
    }
    
    // 清理文件状态和进度
    delete fileUploadStatus.value[file.uid]
    delete fileUploadProgress.value[file.uid]
  }
  hasFiles.value = uploadFiles.value.length > 0
}

const simulateProgress = (fileUid) => {
  let progress = 0
  const interval = setInterval(() => {
    if (fileUploadStatus.value[fileUid] === 'uploading') {
      progress += Math.random() * 30
      if (progress > 90) {
        progress = 90
      }
      fileUploadProgress.value[fileUid] = Math.floor(progress)
    } else {
      if (fileUploadStatus.value[fileUid] === 'success') {
        fileUploadProgress.value[fileUid] = 100
      }
      clearInterval(interval)
    }
  }, 500)
  return interval
}

const uploadSingleFile = async (file) => {
  const formData = new FormData()
  formData.append('file', file.raw)
  const datasetId = Array.isArray(route.query.databaseId) 
    ? route.query.databaseId[0] 
    : (route.query.databaseId || '')
  formData.append('datasetId', datasetId)

  // 初始化上传状态和进度
  fileUploadStatus.value[file.uid] = 'uploading'
  fileUploadProgress.value[file.uid] = 0
  
  // 开始模拟进度
  const progressInterval = simulateProgress(file.uid)

  try {
    const res = await uploadKnowledgeFileSingle(formData)
    
    if (res?.code === 200 && Array.isArray(res.data) && res.data.length > 0) {
      fileUploadStatus.value[file.uid] = 'success'
      const fileData = res.data[0]
      if (fileData.id) {
        uploadedFileIds.value.push(fileData.id)
      }
      return true
    } else {
      fileUploadStatus.value[file.uid] = 'error'
      ElMessage.error(`文件 ${file.name} 上传失败`)
      return false
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    fileUploadStatus.value[file.uid] = 'error'
    ElMessage.error(`文件 ${file.name} 上传失败`)
    return false
  } finally {
    // 清理进度条定时器
    clearInterval(progressInterval)
  }
}

// 添加延迟函数
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))

const handleUpload = async () => {
  if (!hasFiles.value) {
    ElMessage.warning('请至少上传一个文件')
    return
  }

  uploading.value = true
  let successCount = 0
  const filesToUpload = uploadFiles.value.filter(file => fileUploadStatus.value[file.uid] !== 'success')
  const totalFiles = uploadFiles.value.length
  const isMultipleFiles = totalFiles > 1

  try {
    for (let i = 0; i < uploadFiles.value.length; i++) {
      const file = uploadFiles.value[i]
      
      if (fileUploadStatus.value[file.uid] !== 'success') {
        const success = await uploadSingleFile(file)
        if (success) {
          successCount++
        }
        
        // 如果是多文件上传且不是最后一个文件，添加1秒延迟
        if (isMultipleFiles && i < uploadFiles.value.length - 1) {
          await sleep(1000)
        }
      } else {
        successCount++
      }
    }

    if (successCount > 0) {
      ElMessage.success(`成功上传 ${successCount} 个文件`)
      if (successCount === uploadFiles.value.length) {
      currentStep.value = 2
      }
    }
  } catch (error) {
    console.error('上传过程出错:', error)
    ElMessage.error('上传过程出错')
  } finally {
    uploading.value = false
  }
}

const handleTextProcessingStateUpdate = (newState) => {
  Object.assign(textProcessingState, newState)
}

// 在组件传递给下一步时，确保传递正确的数据
watch(() => currentStep.value, (newStep) => {
  if (newStep === 2) {
    // 传递给 TextProcessing 组件的数据
    const fileNames = uploadFiles.value
      .filter(file => fileUploadStatus.value[file.uid] === 'success')
      .map(file => file.name)
    
    uploadedFileNames.value = fileNames
  }
})

// 组件卸载时清理所有定时器
onUnmounted(() => {
  // 清理所有可能存在的进度条定时器
  Object.keys(fileUploadStatus.value).forEach(fileUid => {
    if (fileUploadStatus.value[fileUid] === 'uploading') {
      fileUploadStatus.value[fileUid] = 'error'
    }
  })
})

onMounted(() => {
  // 初始化操作
})
</script>

<style scoped>
:deep(.el-upload-dragger) {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background-color: #fafafa;
  text-align: center;
  transition: all 0.3s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #3b82f6;
  background-color: #f8faff;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 0 20px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 0 20px 20px 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-tag--primary) {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
  font-weight: 500;
}

:deep(.el-radio__label) {
  padding-left: 0;
}

:deep(.el-radio__input) {
  margin-right: 8px;
}

:deep(.el-slider) {
  margin: 0;
}

:deep(.el-input-number.is-small) {
  width: 80px;
}

:deep(.el-dialog .el-slider) {
  margin: 0;
  height: 32px;
}

:deep(.el-dialog .el-switch) {
  --el-switch-core-border-radius: 10px;
}
</style>