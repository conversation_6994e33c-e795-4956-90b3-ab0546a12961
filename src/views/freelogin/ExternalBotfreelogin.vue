<template>
  <div class="external-bot-layout">
    <!-- 修改会话名称弹窗 -->
    <a-modal
      v-model:open="modalOpen"
      title="修改会话名称"
      ok-text="确认"
      cancel-text="取消"
      @ok="updateTitle"
      @cancel="hideModal"
    >
      <a-input
        v-model:value="newTitle"
        placeholder="请输入新的会话标题"
      />
    </a-modal>

    <!-- 移动端顶部栏 -->
    <div class="mobile-header">
      <a-button
        type="text"
        class="menu-toggle-btn"
        @click="toggleMobileMenu"
      >
        <template #icon>
          <MoreOutlined />
        </template>
      </a-button>
      <div class="mobile-logo-section">
        <img
          :src="agentPic"
          alt="logo"
          class="mobile-logo-img"
        />
        <span class="mobile-logo-text">{{ botInfo?.title }}</span>
      </div>
      <div class="mobile-placeholder"></div>
    </div>

    <!-- 左侧菜单 -->
    <div class="menu-container" :class="{ 'mobile-menu-open': isMobileMenuOpen }">
      <!-- 桌面端Logo区域 -->
      <div class="logo-section desktop-only">
        <div class="logo-content">
          <img
            :src="agentPic"
            alt="logo"
            class="logo-img"
          />
          <span class="logo-text">{{ botInfo?.title }}</span>
        </div>
      </div>

      <!-- 新建会话按钮 -->
      <a-button
        type="link"
        class="add-btn"
        @click="onAddConversation"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        新建会话
      </a-button>

      <!-- 会话列表 -->
      <div class="conversations-container">
        <div
          v-for="item in conversationsItems"
          :key="item.key"
          class="conversation-item"
          :class="{ active: item.key === activeKey }"
          @click="onConversationClick(item.key)"
          @contextmenu.prevent="showContextMenu($event, item)"
        >
          <div class="conversation-label">{{ item.label }}</div>
          <a-dropdown
            :trigger="['click']"
            placement="bottomRight"
            @click.stop
          >
            <a-button
              type="text"
              size="small"
              class="conversation-menu-btn"
              @click.stop
            >
              <template #icon>
                <MoreOutlined />
              </template>
            </a-button>
            <template #overlay>
              <a-menu @click="handleMenuClick($event, item)">
                <a-menu-item key="update">
                  <template #icon>
                    <EditOutlined />
                  </template>
                  重命名
                </a-menu-item>
                <a-menu-item key="delete" class="danger-item">
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                  删除
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </div>

    <!-- 移动端遮罩层 -->
    <div 
      v-if="isMobileMenuOpen" 
      class="mobile-overlay"
      @click="closeMobileMenu"
    ></div>

    <!-- 右侧聊天区域 -->
    <div class="chat-container">
      <AiProChat
        v-model:chats="chats"
        :hello-message="welcomeMessage"
        :bot-avatar="agentPic"
        :agent-id="botId"
        :conversation-id="currentConversationId"
        :user-id="'external-user'"
        :preset-questions="presetQuestions"
        @clear-message="clearMessage"
        @update:conversationId="handleConversationIdUpdate"
        :style="{ height: '100%' }"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, watch, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  ExclamationCircleFilled
} from '@ant-design/icons-vue'
import AiProChat from './AiProChat.vue'
import type { ChatMessage } from './AiProChat.vue'
import { getConversationList, getMessageList, removeConversation, renameConversation } from '@/api/ai'
import { getAgentDetail } from '@/api/freelogin'
import agentPic from '@/assets/image/robot.png'

const route = useRoute()
const router = useRouter()

// 从 URL 参数获取智能体 ID，支持多种参数名
const botId = (route.params.id || route.query.agentId || route.query.id) as string

// 响应式数据
const modalOpen = ref(false)
const newTitle = ref('')
const conversationsItems = ref<{ key: string; label: string }[]>([])
const activeKey = ref('')
const chats = ref<ChatMessage[]>([])
const botInfo = ref<any>(null)
const welcomeMessage = ref('欢迎使用科宝，我是你的专属机器人，有什么问题可以随时问我。')
const currentConversationId = ref<string | null>(null)
const isInNewConversation = ref(false) // 标记是否处于新建会话模式
const presetQuestions = ref<any[]>([])
const isMobileMenuOpen = ref(false) // 移动端菜单开关状态

// 移动端菜单控制
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

// 检查免密登录状态
const checkFreeLoginStatus = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const loginFree = urlParams.get('loginfree')
  return loginFree === '1'
}

// 获取智能体详情
const getBotInfo = async () => {
  try {
    const params = {
      agentId: botId
    }
    const response = await getAgentDetail(params)
    console.log('response',response)
    if (response.code === 200 && response.data) {
      const agentData = response.data
      let extractedPresetQuestions = []
      
      // 从 options 字段中提取预设问题
      if (agentData.options) {
        try {
          const uiOptions = typeof agentData.options === 'string' 
            ? JSON.parse(agentData.options) 
            : agentData.options
          
          if (uiOptions.presetQuestions && Array.isArray(uiOptions.presetQuestions)) {
            extractedPresetQuestions = uiOptions.presetQuestions.map(q => ({ text: q.description || q.text || '' }))
          }
        } catch (error) {
          console.warn('解析UI配置失败:', error)
        }
      }
            
      return {
        title: agentData.agentName || `Bot ${botId}`,
        presetQuestions: extractedPresetQuestions
      }
    } else {
      console.error('获取智能体详情失败:', response.msg || '未知错误')
      // 返回默认值
      return {
        title: `Bot ${botId}`,
        presetQuestions: []
      }
    }
  } catch (error) {
    console.error('获取智能体详情失败:', error)
    // 返回默认值
    return {
      title: `Bot ${botId}`,
      presetQuestions: []
    }
  }
}

// 刷新会话列表
const refreshConversationList = async () => {
  try {
    const params = {
      agentId: botId,
      pageNo: '1',
      pageSize: '50'
    }
    const response = await getConversationList(params)
    
    if (response.code === 200 && response.rows) {
      // 保存当前的会话列表，用于保留临时名称
      const currentItems = [...conversationsItems.value]
      
      conversationsItems.value = response.rows.map(item => {
        // 如果API返回的conversationName为空，尝试使用已有的临时名称
        const existingItem = currentItems.find(current => current.key === item.conversationId)
        const displayName = item.conversationName || existingItem?.label || '新建会话'
        
        return {
          key: item.conversationId,
          label: displayName
        }
      })
    } else {
      console.error('获取会话列表失败:', response.msg || '未知错误')
    }
  } catch (error) {
    console.error('刷新会话列表失败:', error)
  }
}

// 事件处理
const onAddConversation = async () => {
  // 如果当前处于新建会话且有聊天记录，先刷新会话列表
  if (isInNewConversation.value && chats.value.length > 0) {
    await refreshConversationList()
  }
  
  // 清空聊天记录，准备新的会话
  chats.value = []
  
  // 设置为新建会话模式
  isInNewConversation.value = true
  currentConversationId.value = null
  
  // 设置一个特殊的activeKey表示新建会话状态
  activeKey.value = 'NEW_CONVERSATION'
}

const onConversationClick = async (key: string) => {
  // 如果当前处于新建会话且有聊天记录，先刷新会话列表
  if (isInNewConversation.value && chats.value.length > 0) {
    await refreshConversationList()
  }
  
  // 切换到指定会话
  activeKey.value = key
  isInNewConversation.value = false  // 退出新建会话模式
  
  // 移动端点击会话后关闭菜单
  closeMobileMenu()
  
  // 更新当前会话的 conversationId
  currentConversationId.value = key
  
  try {
    // 调用真正的 getMessageList API
    const params = {
      agentId: botId,
      conversationId: key,
      pageNo: '1',
      pageSize: '100'
    }
    const response = await getMessageList(params)
    
    // 处理 API 响应
    if (response.code === 200 && response.rows) {
      // 将 API 返回的消息转换为 ChatMessage 格式
      const messages: ChatMessage[] = []
      
      response.rows.forEach((item: any) => {
        // 添加用户消息
        if (item.query) {
          messages.push({
            id: `user_${item.messageId}`,
            role: 'user',
            content: item.query,
            created: new Date(item.createTime).getTime(),
            updateAt: new Date(item.updateTime || item.createTime).getTime()
          })
        }
        
        // 添加AI回复
        if (item.answer) {
          messages.push({
            id: `assistant_${item.messageId}`,
            role: 'assistant',
            content: item.answer,
            messageId: item.messageId,
            created: new Date(item.createTime).getTime(),
            updateAt: new Date(item.updateTime || item.createTime).getTime()
          })
        }
      })
      
      // 按时间排序
      messages.sort((a, b) => a.created - b.created)
      chats.value = messages
    } else {
      console.error('获取消息列表失败:', response.msg || '未知错误')
      chats.value = []
    }
  } catch (error) {
    console.error('获取消息列表失败:', error)
    chats.value = []
    message.error('获取历史消息失败')
  }
}

const showContextMenu = (event: MouseEvent, item: any) => {
  // 右键菜单逻辑可以在这里实现
}

const handleMenuClick = async (menuInfo: any, item: any) => {
  if (menuInfo.key === 'delete') {
    Modal.confirm({
      title: '删除对话',
      icon: h(ExclamationCircleFilled),
      content: '删除后，该对话将不可恢复。确认删除吗？',
      onOk: async () => {
        try {
          // 使用真正的删除会话API
          const params = {
            agentId: botId,
            conversationId: item.key
          }
          const result = await removeConversation(params)
          
          // 根据API文档，成功返回的code是200
          if (result.code === 200) {
            message.success('删除成功')
            chats.value = []
            // 从列表中移除
            conversationsItems.value = conversationsItems.value.filter(conv => conv.key !== item.key)
            // 如果删除的是当前活跃会话，清空activeKey
            if (activeKey.value === item.key) {
              activeKey.value = ''
              currentConversationId.value = null
            }
          } else {
            message.error(result.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除会话失败:', error)
          message.error('删除失败')
        }
      }
    })
  } else if (menuInfo.key === 'update') {
    newTitle.value = item.label
    modalOpen.value = true
  }
}

const updateTitle = async () => {
  try {
    // 使用真正的重命名会话API
    const params = {
      conversationId: activeKey.value,
      conversationName: newTitle.value.trim()
    }
    const result = await renameConversation(params)
    
    // 根据API文档，成功返回的code是200
    if ( result.code === 200) {
      // 更新本地状态
      const index = conversationsItems.value.findIndex(item => item.key === activeKey.value)
      if (index !== -1) {
        // 使用API返回的新名称，或者使用输入的名称
        conversationsItems.value[index].label = result.data?.conversationName || newTitle.value
      }
      message.success('重命名成功')
      modalOpen.value = false
      newTitle.value = ''
    } else {
      message.error(result.msg || '重命名失败')
    }
  } catch (error) {
    console.error('重命名会话失败:', error)
    message.error('重命名失败')
  }
}

const hideModal = () => {
  modalOpen.value = false
  newTitle.value = ''
}

const clearMessage = async () => {
  // 如果是新建会话状态，直接清空聊天记录即可
  if (isInNewConversation.value || !currentConversationId.value) {
    chats.value = []
    message.success('对话已清除')
    return
  }

  // 如果是已有会话，弹出删除确认对话框
  Modal.confirm({
    title: '删除对话记录',
    icon: h(ExclamationCircleFilled),
    content: '删除后，该对话记录将不可恢复。确认删除吗？',
    onOk: async () => {
      try {
        // 调用删除会话API
        const params = {
          agentId: botId,
          conversationId: currentConversationId.value
        }
        const result = await removeConversation(params)
        
        // 根据API文档，成功返回的code是200
        if (result.code === 200) {
          message.success('删除成功')
          
          // 清空当前聊天记录
          chats.value = []
          
          // 从左侧会话列表中移除
          conversationsItems.value = conversationsItems.value.filter(
            conv => conv.key !== currentConversationId.value
          )
          
          // 跳转到新建会话界面
          isInNewConversation.value = true
          currentConversationId.value = null
          activeKey.value = 'NEW_CONVERSATION'
          
          // 刷新会话列表
          await refreshConversationList()
        } else {
          message.error(result.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除对话记录失败:', error)
        message.error('删除失败')
      }
    }
  })
}

const handleConversationIdUpdate = async (newId: string) => {
  currentConversationId.value = newId
  
  // 如果是新建会话收到第一条消息的回复，更新状态
  if (isInNewConversation.value && newId) {
    isInNewConversation.value = false
    activeKey.value = newId
    
    // 获取用户的第一条消息作为临时会话名称
    const firstUserMessage = chats.value.find(chat => chat.role === 'user')
    const tempConversationName = firstUserMessage?.content 
      ? (firstUserMessage.content.length > 20 
          ? firstUserMessage.content.substring(0, 20) + '...' 
          : firstUserMessage.content)
      : '新建会话'
    
    // 先临时添加到会话列表，使用用户第一条消息作为名称
    const existingIndex = conversationsItems.value.findIndex(item => item.key === newId)
    if (existingIndex === -1) {
      conversationsItems.value.unshift({
        key: newId,
        label: tempConversationName
      })
    }
    
    // 然后刷新会话列表以获取API返回的真实名称
    await refreshConversationList()
  }
}

// 验证智能体ID和免密登录状态
const validateAccess = () => {
  if (!botId) {
    message.error('缺少智能体ID参数，无法访问')
    return false
  }
  
  // 检查是否启用免密登录
  const isFreeLogin = checkFreeLoginStatus()
  if (!isFreeLogin) {
    message.warning('当前链接未启用免密登录模式')
  }
  
  // 检查是否有 token
  const token = localStorage.getItem('token')
  if (!token) {
    message.warning('未检测到登录凭证，某些功能可能受限')
  }
  
  return true
}

// 生命周期
onMounted(async () => {
  try {
    // 隐藏可能存在的布局元素，确保全屏显示
    const bodyElement = document.body
    if (bodyElement) {
      bodyElement.style.overflow = 'hidden'
    }
    
    // 验证访问权限
    if (!validateAccess()) {
      return
    }
    
    // 在页面加载时显示免密登录提示
    const isFreeLogin = checkFreeLoginStatus()
    if (isFreeLogin) {
      message.success('免密登录模式已启用', 3)
    }
    
    // 获取智能体详情
    const info = await getBotInfo()
    botInfo.value = info
    presetQuestions.value = info.presetQuestions
    
    // 初始化为新建会话模式
    isInNewConversation.value = true
    currentConversationId.value = null
    activeKey.value = 'NEW_CONVERSATION'
    
    // 获取会话列表
    await refreshConversationList()
  } catch (error) {
    console.error('初始化失败:', error)
    // 错误时使用空列表
    conversationsItems.value = []
  }
})

// 组件卸载时恢复body样式
onUnmounted(() => {
  const bodyElement = document.body
  if (bodyElement) {
    bodyElement.style.overflow = ''
  }
})

// 监听聊天变化，自动更新会话列表
watch(() => chats.value.length, async (newLength) => {
  if (newLength === 2 && chats.value[1]?.content?.length < 1) {
    // 当有新的对话开始时，刷新会话列表
    try {
      await refreshConversationList()
    } catch (error) {
      console.error('刷新会话列表失败:', error)
    }
  }
})
</script>

<style scoped>
.external-bot-layout {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  z-index: 9999;
  overflow: hidden;
}

/* 移动端顶部栏 */
.mobile-header {
  display: none;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1001;
}

.mobile-logo-section {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
  margin-left: -40px; /* 补偿左侧按钮的宽度，让logo居中 */
}

.mobile-logo-img {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  margin-right: 8px;
}

.mobile-logo-text {
  color: #1a1a1a;
  font-weight: 600;
  font-size: 16px;
}

.menu-toggle-btn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 40px;
  height: 40px;
  border-radius: 6px;
  color: #666;
  flex-shrink: 0;
}

.mobile-placeholder {
  width: 40px; /* 与左侧按钮宽度保持一致，实现平衡布局 */
  height: 40px;
  flex-shrink: 0;
}

/* 移动端遮罩层 */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.menu-container {
  background: rgba(240, 242, 245, 0.5);
  width: 300px;
  min-width: 300px;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #f0f0f0;
  transition: transform 0.3s ease;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.logo-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.logo-img {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  margin-right: 10px;
  padding-left: 10px;
}

.logo-text {
  color: #1a1a1a;
  font-weight: 600;
  font-size: 16px;
}

.add-btn {
  background: rgba(22, 119, 255, 0.06);
  border: 1px solid rgba(22, 119, 255, 0.2);
  width: calc(100% - 24px);
  margin: 12px 12px 24px 12px;
  height: 44px;
  border-radius: 6px;
  color: #1677ff;
  font-weight: 500;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 6px;
  font-size: 14px;
  flex-shrink: 0;
  box-sizing: border-box;
}

.add-btn:hover {
  background: rgba(22, 119, 255, 0.1);
  border-color: rgba(22, 119, 255, 0.3);
}

.conversations-container {
  padding: 0 12px;
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 16px;
  margin-bottom: 6px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  min-height: 48px;
}

.conversation-item:hover {
  background: rgba(0, 0, 0, 0.04);
}

.conversation-item.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.conversation-label {
  flex: 1;
  font-size: 14px;
  color: #262626;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.conversation-menu-btn {
  opacity: 0;
  transition: opacity 0.2s;
  color: #8c8c8c;
  width: 32px;
  height: 32px;
}

.conversation-item:hover .conversation-menu-btn {
  opacity: 1;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.danger-item {
  color: #ff4d4f;
}

.danger-item:hover {
  background: #fff2f0;
}

/* 滚动条样式 */
.conversations-container::-webkit-scrollbar {
  width: 6px;
}

.conversations-container::-webkit-scrollbar-track {
  background: transparent;
}

.conversations-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.conversations-container::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 桌面端样式 */
@media (min-width: 769px) {
  .external-bot-layout {
    flex-direction: row;
  }
  
  .chat-container {
    padding: 24px;
    gap: 16px;
  }
  
  .desktop-only {
    display: flex !important;
  }
}

/* 移动端样式 */
@media (max-width: 768px) {
  .external-bot-layout {
    flex-direction: column;
  }
  
  .mobile-header {
    display: flex;
  }
  
  .menu-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 260px;
    max-width: 80vw;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    padding-top: 20px; /* 为移动端添加顶部内边距 */
    background: rgb(240, 242, 245) !important; /* 移动端完全不透明 */
  }
  
  .menu-container.mobile-menu-open {
    transform: translateX(0);
  }
  
  .mobile-overlay {
    display: block;
  }
  
  .chat-container {
    margin-top: 0;
    padding: 16px;
    gap: 12px;
    height: calc(100vh - 64px);
  }
  
  .desktop-only {
    display: none !important;
  }
  
  .add-btn {
    height: 48px;
    font-size: 16px;
    margin: 60px 12px 16px 12px; /* 调整移动端上边距 */
    flex-shrink: 0; /* 防止按钮被压缩 */
    display: flex !important; /* 确保移动端显示 */
    visibility: visible !important; /* 强制可见 */
    opacity: 1 !important; /* 确保不透明 */
    position: relative; /* 确保定位正确 */
    z-index: 1; /* 确保层级正确 */
  }
  
  .conversation-item {
    padding: 16px;
    margin-bottom: 8px;
    min-height: 56px;
  }
  
  .conversation-label {
    font-size: 16px;
  }
  
  .conversation-menu-btn {
    opacity: 1;
    width: 40px;
    height: 40px;
  }
  
  .logo-section {
    padding: 16px 12px;
  }
  
  .logo-text {
    font-size: 18px;
  }
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .menu-container {
    width: 240px;
  }
  
  .mobile-logo-text {
    font-size: 14px;
  }
  
  .chat-container {
    padding: 12px;
  }
}

/* 超大屏幕适配 */
@media (min-width: 1200px) {
  .menu-container {
    width: 320px;
    min-width: 320px;
  }
  
  .chat-container {
    padding: 32px;
    gap: 20px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .conversation-menu-btn {
    opacity: 1;
  }
  
  .add-btn {
    height: 48px;
  }
  
  .conversation-item {
    min-height: 56px;
  }
}
</style> 