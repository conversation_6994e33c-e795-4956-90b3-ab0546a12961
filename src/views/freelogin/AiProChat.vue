<template>
  <div class="ai-pro-chat" :style="{ ...appStyle, ...style }">
    <!-- 消息容器 -->
    <div 
      ref="messagesContainerRef" 
      class="messages-container"
    >
      <a-spin v-if="loading" tip="加载中..." />
      <template v-else>
        <!-- 欢迎消息 -->
        <div v-if="!chats?.length" class="welcome-container">
          <div class="welcome-content">
            <div class="welcome-text">{{ helloMessage }}</div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div v-else class="messages-list">
          <div 
            v-for="(chat, index) in chats" 
            :key="chat.id + Math.random().toString()"
            class="message-item"
            :class="{ 'user-message': chat.role === 'user', 'ai-message': chat.role === 'assistant' }"
          >
            <!-- 消息头部 -->
            <div class="message-header">
              <div class="message-avatar">
                <img 
                  v-if="chat.role === 'assistant'" 
                  :src="botAvatar" 
                  alt="AI Avatar"
                  class="avatar-img"
                />
                <a-avatar v-else :style="userAvatarStyle">
                  <template #icon>
                    <UserOutlined />
                  </template>
                </a-avatar>
              </div>
              <div class="message-time">
                {{ new Date(chat.created).toLocaleString() }}
              </div>
            </div>

            <!-- 消息内容 -->
            <div class="message-content">
              <div v-if="chat.loading" class="loading-content">
                <a-spin size="small" />
                <span class="loading-text">AI正在思考中...</span>
              </div>
              <div v-else class="content-text">
                <div 
                  v-if="chat.role === 'assistant'" 
                  class="markdown-content"
                  v-html="renderMarkdown(chat.content)"
                />
                <div v-else class="plain-content">
                  {{ chat.content }}
                </div>
              </div>
            </div>

            <!-- 消息操作 -->
            <div v-if="!chat.loading" class="message-actions">
              <a-button 
                v-if="chat.role === 'assistant'"
                type="text" 
                size="small"
                @click="handleRegenerate(index)"
                title="重新生成"
              >
                <template #icon>
                  <SyncOutlined />
                </template>
              </a-button>
              <a-button 
                type="text" 
                size="small"
                @click="copyToClipboard(chat.content)"
                title="复制"
              >
                <template #icon>
                  <CopyOutlined />
                </template>
              </a-button>
            </div>
          </div>
        </div>
        
        <!-- 滚动锚点 -->
        <div ref="messagesEndRef" />
      </template>
    </div>

    <!-- 输入区域 -->
    <div class="input-container">
      <!-- 问题预设栏 -->
      <div v-if="presetQuestions && presetQuestions.length > 0" class="preset-questions-bar">
        <div class="preset-questions-scroll">
          <div 
            v-for="(question, index) in presetQuestions" 
            :key="index"
            class="preset-question-item"
            @click="handlePresetQuestionClick(question.text)"
          >
            {{ question.text }}
          </div>
        </div>
      </div>
      
      <!-- 功能按钮栏 -->
      <div class="mode-buttons">
        <div class="flex justify-between items-center">
          <div class="flex gap-2 items-center">
            <!--<button 
              @click="toggleMode('deep')" 
              class="mode-btn"
              :class="[
                activeModes.includes('deep')
                  ? 'mode-btn-active' 
                  : 'mode-btn-inactive'
              ]"
            >
              <div class="flex gap-1 items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12z"></path>
                  <path d="M12 8v8"></path>
                  <path d="M8 12h8"></path>
                </svg>
                深度思考
              </div>
            </button>-->
            <button 
              v-if="showSearchMode"
              @click="toggleMode('search')" 
              class="mode-btn"
              :class="[
                activeModes.includes('search')
                  ? 'mode-btn-active' 
                  : 'mode-btn-inactive'
              ]"
            >
              <div class="flex gap-1 items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
                联网搜索
              </div>
            </button>
          </div>
          
          <!-- 上传按钮 -->
          <div class="upload-section">
            <a-upload
              class="upload-button"
              :action="`/api/v1/public/chat/uploadFile`"
              :data="uploadData"
              :show-upload-list="false"
              :before-upload="beforeUpload"
              @success="handleUploadSuccess"
              @error="handleUploadError"
            >
              <button class="upload-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
                </svg>
              </button>
            </a-upload>
          </div>
        </div>
      </div>
      
      <div class="input-wrapper">
        <a-textarea
          v-model:value="content"
          :placeholder="inputPlaceholder"
          :auto-size="{ minRows: 1, maxRows: 4 }"
          @keydown="handleKeyDown"
          class="message-input"
        />
        <div class="input-actions">
          <a-button 
            type="text"
            size="large"
            @click="clearMessage"
            title="删除对话记录"
            class="clear-btn"
            :disabled="false"
          >
            <template #icon>
                <ClearOutlined />
            </template>
          </a-button>
          <a-button 
            type="primary"
            size="large"
            :loading="sendLoading || isStreaming"
            @click="handleSubmit"
            class="send-btn"
          >
            <template #icon>
                <SlackOutlined />
            </template>
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  UserOutlined, 
  SyncOutlined, 
  CopyOutlined, 
  DeleteOutlined,
  ClearOutlined,
  SlackOutlined,
  SendOutlined 
} from '@ant-design/icons-vue'
import { marked } from 'marked'
import { handleSendMessage } from '@/api/ai'

// 类型定义
export interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant' | 'aiLoading'
  created: number
  updateAt: number
  loading?: boolean
  documents?: any[]
  messageId?: string
}

// Props 定义
interface Props {
  loading?: boolean
  chats?: ChatMessage[]
  style?: Record<string, any>
  appStyle?: Record<string, any>
  helloMessage?: string
  botAvatar?: string
  inputPlaceholder?: string
  agentId?: string
  conversationId?: string
  userId?: string
  request?: (messages: ChatMessage[]) => Promise<Response>
  presetQuestions?: { text: string }[]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  chats: () => [],
  style: () => ({}),
  appStyle: () => ({}),
  helloMessage: '欢迎使用 AIFlowy',
  botAvatar: '/favicon.png',
  inputPlaceholder: '请输入您的问题...',
  agentId: 'default-agent',
  conversationId: undefined,
  userId: 'default-user',
  presetQuestions: () => []
})

// Emits 定义
const emit = defineEmits<{
  'update:chats': [chats: ChatMessage[]]
  'clear-message': []
  'update:conversationId': [conversationId: string]
}>()

// 响应式数据
const content = ref('')
const sendLoading = ref(false)
const isStreaming = ref(false)
const messagesContainerRef = ref<HTMLDivElement>()
const messagesEndRef = ref<HTMLDivElement>()
const autoScrollEnabled = ref(true)
const isUserScrolledUp = ref(false)
const activeModes = ref<string[]>([])
const showSearchMode = ref(true) // 控制是否显示联网搜索按钮
const uploadData = ref({
  agentId: '',
  conversationId: '',
  userId: ''
})
const uploadedFiles = ref<any[]>([])

// 用户头像样式
const userAvatarStyle = {
  color: '#fff',
  backgroundColor: '#87d068'
}

// 计算属性
const currentChats = computed(() => props.chats || [])

// 方法
const scrollToBottom = () => {
  const container = messagesContainerRef.value
  if (container && autoScrollEnabled.value) {
    container.scrollTop = container.scrollHeight
  }
}

const handleScroll = () => {
  const container = messagesContainerRef.value
  if (!container) return

  const { scrollTop, scrollHeight, clientHeight } = container
  const atBottom = scrollHeight - scrollTop <= clientHeight + 5

  if (atBottom) {
    autoScrollEnabled.value = true
    isUserScrolledUp.value = false
  } else {
    autoScrollEnabled.value = false
    isUserScrolledUp.value = true
  }
}

const renderMarkdown = (content: string) => {
  try {
    return marked(content, {
      breaks: true,
      gfm: true
    })
  } catch (error) {
    console.error('Markdown渲染错误:', error)
    return content.replace(/\n/g, '<br>')
  }
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    message.success('复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault()
    handleSubmit()
  }
}

const handleSubmit = async () => {
  if (!content.value.trim() || sendLoading.value) return
  
  sendLoading.value = true
  isStreaming.value = true
  
  // 准备消息内容
  const messageText = content.value.trim()
  
  // 添加用户消息
  const userMessage: ChatMessage = {
    role: 'user',
    id: Date.now().toString(),
    content: messageText,
    created: Date.now(),
    updateAt: Date.now()
  }
  
  // 添加AI消息占位符
  const aiMessage: ChatMessage = {
    role: 'assistant',
    id: (Date.now() + 1).toString(),
    content: '',
    loading: true,
    created: Date.now(),
    updateAt: Date.now()
  }
  
  // 更新消息列表
  const newChats = [...currentChats.value, userMessage, aiMessage]
  emit('update:chats', newChats)
  
  // 清理输入框
  content.value = ''
  
  // 滚动到底部
  setTimeout(scrollToBottom, 50)
  
  // 设置超时处理
  let hasReceivedResponse = false
  const timeout = setTimeout(() => {
    if (!hasReceivedResponse) {
      sendLoading.value = false
      isStreaming.value = false
      
      const updatedChats = [...newChats]
      const aiMessageIndex = updatedChats.length - 1
      updatedChats[aiMessageIndex].loading = false
      updatedChats[aiMessageIndex].content = '响应超时，请稍后重试。'
      updatedChats[aiMessageIndex].updateAt = Date.now()
      
      emit('update:chats', updatedChats)
      message.error('请求超时，请稍后重试')
    }
  }, 120000) // 2分钟超时
  
  try {
    // 使用 handleSendMessage 进行流式响应
    await handleSendMessage(messageText, {
      agentId: props.agentId || 'default-agent',
      conversationId: props.conversationId || undefined,
      userId: props.userId || 'default-user',
      files: uploadedFiles.value,
      inputs: {},
      onlineSearch: activeModes.value.includes('search'),
      deepResearch: activeModes.value.includes('deep'),
      onData: (text, messageId, conversationId, metadata) => {
        hasReceivedResponse = true
        clearTimeout(timeout)
        
        const updatedChats = [...newChats]
        const aiMessageIndex = updatedChats.length - 1
        updatedChats[aiMessageIndex].loading = false
        
        // 处理文档引用
        if (metadata?.retriever_resources) {
          updatedChats[aiMessageIndex].documents = metadata.retriever_resources
        }
        
        // 处理消息内容 - 参考 ChatContainer.vue 的逻辑
        let processedText = text
        if (text.includes('Thinking...')) {
          const thinkingContent = text.match(/<details[^>]*>([\s\S]*?)<\/details>/)?.[1] || ''
          if (!thinkingContent.trim() || (thinkingContent.includes('</summary>') && thinkingContent.split('</summary>')[1].trim() === '')) {
            processedText = text.replace(/<details[^>]*>[\s\S]*?<\/details>/, '')
          } else {
            processedText = text
              .replace('<details open', '<details')
              .replace(/<\/details>\n+/, '</details>')
          }
        } else {
          processedText = text.replace(/^\n+/, '')
        }
        
        updatedChats[aiMessageIndex].content = processedText
        updatedChats[aiMessageIndex].updateAt = Date.now()
        
        if (messageId) {
          updatedChats[aiMessageIndex].messageId = messageId
        }
        
        // 如果收到了新的 conversationId，通知父组件更新
        if (conversationId && conversationId !== props.conversationId) {
          emit('update:conversationId', conversationId)
        }
        
        emit('update:chats', updatedChats)
        
        // 自动滚动到底部
        if (autoScrollEnabled.value) {
          setTimeout(scrollToBottom, 10)
        }
      },
      onCompleted: (text, metadata) => {
        hasReceivedResponse = true
        clearTimeout(timeout)
        
        // 如果有metadata和引用文档信息，添加到最后一条消息中
        if (metadata && metadata.retriever_resources && metadata.retriever_resources.length > 0) {
          const updatedChats = [...newChats]
          const aiMessageIndex = updatedChats.length - 1
          updatedChats[aiMessageIndex].documents = metadata.retriever_resources
          emit('update:chats', updatedChats)
        }
        
        sendLoading.value = false
        isStreaming.value = false
      },
      onError: (error) => {
        hasReceivedResponse = true
        clearTimeout(timeout)
        
        const updatedChats = [...newChats]
        const aiMessageIndex = updatedChats.length - 1
        updatedChats[aiMessageIndex].loading = false
        updatedChats[aiMessageIndex].content = '抱歉，出现了一些错误，请稍后重试。'
        updatedChats[aiMessageIndex].updateAt = Date.now()
        
        emit('update:chats', updatedChats)
        
        sendLoading.value = false
        isStreaming.value = false
        
        console.error('发送消息失败:', error)
        message.error('发送消息失败，请稍后重试')
      }
    })
  } catch (error) {
    hasReceivedResponse = true
    clearTimeout(timeout)
    
    const updatedChats = [...newChats]
    const aiMessageIndex = updatedChats.length - 1
    updatedChats[aiMessageIndex].loading = false
    updatedChats[aiMessageIndex].content = '抱歉，出现了一些错误，请稍后重试。'
    updatedChats[aiMessageIndex].updateAt = Date.now()
    
    emit('update:chats', updatedChats)
    
    sendLoading.value = false
    isStreaming.value = false
    
    console.error('发送消息错误:', error)
    message.error('发送消息失败，请稍后重试')
  }
}

const handleRegenerate = async (index: number) => {
  if (index <= 0 || sendLoading.value) return
  
  const prevUserMessage = currentChats.value[index - 1]
  if (prevUserMessage.role !== 'user') return
  
  sendLoading.value = true
  isStreaming.value = true
  
  const messageText = prevUserMessage.content
  
  // 添加AI消息占位符
  const aiMessage: ChatMessage = {
    role: 'assistant',
    id: (Date.now() + 1).toString(),
    content: '',
    loading: true,
    created: Date.now(),
    updateAt: Date.now()
  }
  
  // 替换原来的AI消息
  const newChats = [...currentChats.value]
  newChats[index] = aiMessage
  emit('update:chats', newChats)
  
  // 滚动到底部
  setTimeout(scrollToBottom, 50)
  
  // 设置超时处理
  let hasReceivedResponse = false
  const timeout = setTimeout(() => {
    if (!hasReceivedResponse) {
      sendLoading.value = false
      isStreaming.value = false
      
      const updatedChats = [...newChats]
      updatedChats[index].loading = false
      updatedChats[index].content = '响应超时，请稍后重试。'
      updatedChats[index].updateAt = Date.now()
      
      emit('update:chats', updatedChats)
      message.error('请求超时，请稍后重试')
    }
  }, 120000) // 2分钟超时
  
  try {
    // 使用 handleSendMessage 进行流式响应
    await handleSendMessage(messageText, {
      agentId: props.agentId || 'default-agent',
      conversationId: props.conversationId || undefined,
      userId: props.userId || 'default-user',
      files: [],
      inputs: {},
      onlineSearch: activeModes.value.includes('search'),
      deepResearch: activeModes.value.includes('deep'),
      onData: (text, messageId, conversationId, metadata) => {
        hasReceivedResponse = true
        clearTimeout(timeout)
        
        const updatedChats = [...newChats]
        updatedChats[index].loading = false
        
        // 处理文档引用
        if (metadata?.retriever_resources) {
          updatedChats[index].documents = metadata.retriever_resources
        }
        
        // 处理消息内容
        let processedText = text
        if (text.includes('Thinking...')) {
          const thinkingContent = text.match(/<details[^>]*>([\s\S]*?)<\/details>/)?.[1] || ''
          if (!thinkingContent.trim() || (thinkingContent.includes('</summary>') && thinkingContent.split('</summary>')[1].trim() === '')) {
            processedText = text.replace(/<details[^>]*>[\s\S]*?<\/details>/, '')
          } else {
            processedText = text
              .replace('<details open', '<details')
              .replace(/<\/details>\n+/, '</details>')
          }
        } else {
          processedText = text.replace(/^\n+/, '')
        }
        
        updatedChats[index].content = processedText
        updatedChats[index].updateAt = Date.now()
        
        if (messageId) {
          updatedChats[index].messageId = messageId
        }
        
        // 如果收到了新的 conversationId，通知父组件更新
        if (conversationId && conversationId !== props.conversationId) {
          emit('update:conversationId', conversationId)
        }
        
        emit('update:chats', updatedChats)
        
        // 自动滚动到底部
        if (autoScrollEnabled.value) {
          setTimeout(scrollToBottom, 10)
        }
      },
      onCompleted: (text, metadata) => {
        hasReceivedResponse = true
        clearTimeout(timeout)
        
        // 如果有metadata和引用文档信息，添加到消息中
        if (metadata && metadata.retriever_resources && metadata.retriever_resources.length > 0) {
          const updatedChats = [...newChats]
          updatedChats[index].documents = metadata.retriever_resources
          emit('update:chats', updatedChats)
        }
        
        sendLoading.value = false
        isStreaming.value = false
      },
      onError: (error) => {
        hasReceivedResponse = true
        clearTimeout(timeout)
        
        const updatedChats = [...newChats]
        updatedChats[index].loading = false
        updatedChats[index].content = '抱歉，出现了一些错误，请稍后重试。'
        updatedChats[index].updateAt = Date.now()
        
        emit('update:chats', updatedChats)
        
        sendLoading.value = false
        isStreaming.value = false
        
        console.error('重新生成失败:', error)
        message.error('重新生成失败，请稍后重试')
      }
    })
  } catch (error) {
    hasReceivedResponse = true
    clearTimeout(timeout)
    
    const updatedChats = [...newChats]
    updatedChats[index].loading = false
    updatedChats[index].content = '抱歉，出现了一些错误，请稍后重试。'
    updatedChats[index].updateAt = Date.now()
    
    emit('update:chats', updatedChats)
    
    sendLoading.value = false
    isStreaming.value = false
    
    console.error('重新生成错误:', error)
    message.error('重新生成失败，请稍后重试')
  }
}

const clearMessage = () => {
  emit('clear-message')
}

// 切换模式按钮
const toggleMode = (mode: string) => {
  const index = activeModes.value.indexOf(mode)
  if (index > -1) {
    activeModes.value.splice(index, 1)
  } else {
    activeModes.value.push(mode)
  }
}

// 文件上传相关方法
const beforeUpload = (file: File) => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  const isValidType = validTypes.indexOf(file.type) > -1
  if (!isValidType) {
    message.error('只能上传 JPG/PNG/GIF/PDF/TXT/DOC/DOCX 格式的文件!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!')
    return false
  }
  
  // 更新上传数据
  uploadData.value.agentId = props.agentId || 'default-agent'
  uploadData.value.conversationId = props.conversationId || ''
  uploadData.value.userId = props.userId || 'default-user'
  
  return true
}

const handleUploadSuccess = (response: any, file: any) => {
  if (response.code === 200) {
    // 根据文件类型设置fileType
    let fileType = 'custom'
    if (file.type) {
      if (file.type.startsWith('image/')) {
        fileType = 'image'
      } else if (file.type.startsWith('audio/')) {
        fileType = 'audio'
      } else if (file.type.startsWith('video/')) {
        fileType = 'video'
      } else if (
        file.type === 'application/pdf' ||
        file.type === 'text/plain' ||
        file.type === 'application/msword' ||
        file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ) {
        fileType = 'document'
      }
    }
    
    uploadedFiles.value.push({
      name: file.name,
      url: response.data.url,
      fileId: response.data.fileId,
      fileType: fileType,
      size: file.size,
      type: file.type
    })
    message.success('文件上传成功!')
  } else {
    message.error(response.msg || '文件上传失败!')
  }
}

const handleUploadError = (error: any) => {
  console.error('上传失败:', error)
  message.error('文件上传失败!')
}

// 处理预设问题点击
const handlePresetQuestionClick = (questionText: string) => {
  content.value = questionText
  // 自动发送或者让用户自己决定发送
  setTimeout(() => {
    handleSubmit()
  }, 100)
}

// 生命周期
onMounted(() => {
  const container = messagesContainerRef.value
  if (container) {
    container.addEventListener('scroll', handleScroll)
  }
  
  scrollToBottom()
})

// 监听消息变化
watch(() => currentChats.value, () => {
  if (autoScrollEnabled.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}, { deep: true })
</script>

<style scoped>
.ai-pro-chat {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border: 1px solid #f3f3f3;
  border-radius: 8px;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  scrollbar-width: none;
}

.messages-container::-webkit-scrollbar {
  display: none;
}

.welcome-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
}

.welcome-content {
  text-align: center;
  color: #666;
}

.welcome-icon {
  margin-bottom: 16px;
}

.avatar-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.welcome-text {
  font-size: 16px;
  color: #666;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-message {
  align-items: flex-end;
}

.ai-message {
  align-items: flex-start;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #999;
}

.user-message .message-header {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  background: #f5f5f5;
  position: relative;
}

.user-message .message-content {
  background: #1890ff;
  color: white;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-text {
  color: #666;
  font-size: 14px;
}

.content-text {
  line-height: 1.6;
}

.markdown-content {
  word-break: break-word;
}

.markdown-content :deep(p) {
  margin: 0 0 8px 0;
}

.markdown-content :deep(p:last-child) {
  margin-bottom: 0;
}

.markdown-content :deep(pre) {
  background: #f6f8fa;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 8px 0;
}

.markdown-content :deep(code) {
  background: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.plain-content {
  white-space: pre-wrap;
  word-break: break-word;
}

.message-actions {
  display: flex;
  gap: 4px;
  margin-top: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.user-message .message-actions {
  justify-content: flex-end;
}

.input-container {
  border-top: 1px solid #eee;
  padding: 12px;
  background: #fafafa;
}

.input-wrapper {
  display: flex;
  gap: 8px;
  align-items: flex-end;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 8px;
  transition: border-color 0.2s;
}

.input-wrapper:hover {
  border-color: #40a9ff;
}

.input-wrapper:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.message-input {
  flex: 1;
  resize: none;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  line-height: 1.5;
}

.message-input:focus {
  box-shadow: none;
}

.input-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.clear-btn {
  color: #999;
  font-size: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s;
}

.clear-btn:hover {
  color: #ff4d4f;
  background-color: #fff2f0;
}

.clear-btn:focus {
  color: #ff4d4f;
  background-color: #fff2f0;
}

.send-btn {
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 16px;
}

.send-btn .anticon {
  font-size: 16px;
}

.mode-buttons {
  margin-bottom: 12px;
  padding: 0 8px;
}

.flex {
  display: flex;
}

.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mode-btn {
  background: none;
  border: none;
  padding: 6px 12px;
  font: inherit;
  cursor: pointer;
  outline: inherit;
  border-radius: 16px;
  font-size: 14px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  flex-shrink: 0;
}

.mode-btn:hover {
  background-color: #f5f5f5;
}

.mode-btn-active {
  background-color: #1890ff;
  color: white;
}

.mode-btn-active:hover {
  background-color: #40a9ff;
}

.mode-btn-inactive {
  background-color: #f5f5f5;
  color: #666;
}

.mode-btn-inactive:hover {
  background-color: #e6f7ff;
  color: #1890ff;
}

.mode-tip {
  font-size: 12px;
  color: #999;
  margin-left: 12px;
}

.upload-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
}

.upload-button:hover {
  background-color: #f5f5f5;
}

.upload-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.upload-btn svg {
  width: 20px;
  height: 20px;
}

.preset-questions-bar {
  margin-bottom: 12px;
}

.preset-questions-scroll {
  overflow-x: auto;
  display: flex;
  gap: 8px;
  padding: 4px 0;
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;
}

.preset-questions-scroll::-webkit-scrollbar {
  height: 4px;
}

.preset-questions-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.preset-questions-scroll::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 2px;
}

.preset-questions-scroll::-webkit-scrollbar-thumb:hover {
  background-color: #bfbfbf;
}

.preset-question-item {
  flex-shrink: 0;
  padding: 6px 12px;
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  border-radius: 16px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  transition: all 0.2s ease;
  user-select: none;
}

.preset-question-item:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
  transform: translateY(-1px);
}
</style> 