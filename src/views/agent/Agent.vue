<template>
  <div class="mx-auto min-h-screen bg-sky-50">
    <div class="max-w-[1920px] mx-auto px-[120px] flex flex-col min-h-screen">
      <!-- 头部区域 -->
      <div class="flex justify-between items-start px-4 py-8">
        <div>
          <h1 class="mb-2 text-xl font-bold text-gray-800">智能体广场</h1>
          <p class="text-gray-600">下载行业AI工具包，扩展重直领域专业能力</p>
        </div>
        <div class="flex gap-3">
          <!-- 标签筛选 -->
          <div class="relative">
            <div class="flex items-center px-3 h-9 bg-white rounded-md border border-gray-200">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-gray-400 lucide lucide-target"><circle cx="12" cy="12" r="10"/><circle cx="12" cy="12" r="6"/><circle cx="12" cy="12" r="2"/></svg>
              <select v-model="queryParams.agentTag"
                      class="pr-6 w-full text-gray-700 bg-transparent border-none appearance-none outline-none"
                      @change="handleSearch">
                <option value="">不限领域</option>
                <option v-for="tag in allTags" :key="tag" :value="tag">{{ tag }}</option>
              </select>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute right-3 text-gray-400 lucide lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>
            </div>
          </div>

          <!-- 搜索框 -->
          <div class="relative">
            <div class="flex items-center px-3 h-9 bg-white rounded-md border border-gray-200">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-gray-400 lucide lucide-search"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg>
              <input
                v-model="queryParams.agentName"
                type="text"
                placeholder="搜索智能体名称"
                class="w-full text-gray-700 bg-transparent border-none outline-none"
                @input="handleSearch"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 卡片网格 -->
      <div class="grid flex-1 grid-cols-3 grid-rows-3 gap-14 px-4">
        <div v-for="agent in agentList"
             :key="agent.agentId"
             class="flex flex-col p-6 rounded-lg card-gradient h-[200px] hover:shadow-lg transition-shadow">
          <!-- 上部分：Logo和标题 -->
          <div class="flex gap-3 items-start mb-2">
            <img src="@/assets/image/robot.png"
                 alt="智能体logo"
                 class="flex-shrink-0 p-1 bg-blue-50 rounded-lg card-logo"/>
            <div>
              <h3 class="text-base font-medium text-gray-800 line-clamp-2 card-title">{{ agent.agentName }}</h3>
              <!-- 中部分：描述文本 -->
              <p class="flex-1 mb-4 text-sm text-gray-500 line-clamp-3">{{ agent.agentDesc }}</p>
            </div>
          </div>
          <!-- 下部分：标签和按钮 -->
          <div class="flex justify-between items-center">
            <div class="flex flex-wrap gap-2 card-tag">
              <span v-for="tag in agent.agentTag.split(',')"
                    :key="tag"
                    :class="getTagClass(tag)"
                    class="px-2 py-0.5 text-xs font-normal rounded-md">
                {{ tag }}
              </span>
            </div>
            <button
                class="px-4 py-1 text-sm text-white transition-colors bg-[#596df4] rounded card-button hover:bg-[#596df4]"
                :disabled="agent.status === 0"
                @click="handleApply(agent)">
              {{ agent.status === 1 ? '+开启使用' : '已停用' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 分页 - 移到底部并添加样式 -->
      <div class="flex justify-center py-8 mt-auto">
        <div class="flex gap-2 items-center">
          <button
            @click="handlePageChange(queryParams.pageNo - 1)"
            :disabled="queryParams.pageNo <= 1"
            class="px-4 py-2 text-sm rounded-md border hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          <span class="px-4 text-sm text-gray-600">
            第 {{ queryParams.pageNo }} 页 / 共 {{ totalPages }} 页
          </span>
          <button
            @click="handlePageChange(queryParams.pageNo + 1)"
            :disabled="queryParams.pageNo >= totalPages"
            class="px-4 py-2 text-sm rounded-md border hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAgentList } from '@/api/ai'

export default {
  name: 'SmartAgentPlaza',
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 9,
        agentName: '',
        agentTag: ''
      },
      // 数据列表
      agentList: [],
      // 总数
      total: 0,
      // 所有可用标签
      allTags: ['公路运营', '路况监测', '交通管理', '应急管理', '空域管理', '无人机巡查']
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.queryParams.pageSize)
    }
  },
  methods: {
    // 获取标签样式
    getTagClass(tag) {
      return [
        'text-xs font-normal',
        tag.includes('公路') ? 'bg-green-50 text-green-600' :
        tag.includes('空域') ? 'bg-blue-50 text-blue-600' :
        tag.includes('交通') ? 'bg-orange-50 text-orange-600' :
        'bg-gray-50 text-gray-600'
      ]
    },

    // 获取数据列表
    async fetchData() {
      try {
        const res = await getAgentList(this.queryParams)
        if (res.code === 200) {
          this.agentList = res.rows
          // 如果接口没有返回 total，使用数组长度
          this.total = res.total || (Array.isArray(res.rows) ? res.rows.length : 0)
        } else {
          console.error('获取数据失败:', res.msg)
        }
      } catch (error) {
        console.error('请求失败:', error)
      }
    },

    // 搜索处理（使用防抖）
    handleSearch: debounce(function() {
      this.queryParams.pageNo = 1
      this.fetchData()
    }, 300),

    // 页码变化
    handlePageChange(page) {
      if (page < 1 || page > this.totalPages) return
      this.queryParams.pageNo = page
      this.fetchData()
    },

    // 应用智能体
    handleApply(agent) {
      if (agent.status === 0) return
      console.log('跳转前的 agent 数据:', agent)
      
      // 检查是否是公文办公智能体
      if (agent.agentId === '799592ca740609d28525d878d539fdc1') {
        this.$router.push('/document-office')
      }
      // 检查是否是检测报告分析智能体
      else if (agent.agentId === '81b027e060dcf65c68054c16577ee0f4') {
        // 为检测报告分析智能体设置特殊参数
        this.$router.push({
          path: `/new-dialog/${agent.agentId}/${encodeURIComponent(agent.agentName)}`,
          query: {
            specialAgent: 'report_analysis' // 标识这是检测报告分析智能体
          }
        })
      } else {
        this.$router.push(`/new-dialog/${agent.agentId}/${encodeURIComponent(agent.agentName)}`)
      }
    }
  },
  mounted() {
    this.fetchData()
  }
}

// 防抖函数
function debounce(fn, delay) {
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}
</script>

<style scoped>
.card-logo{
  width: 80px;
  height: 80px;
  margin-right: 10px;
}
.card-title {
  margin-bottom: 5px;
  color: #4E5969;
  font-weight: bolder;
}
/* 修改卡片渐变背景样式 */
.card-gradient {
  position: relative;
  background: linear-gradient(30deg, rgba(89,109,244,0.14) 13.4%, rgba(174,209,255,0.34) 85.87%);
  color: rgba(16,16,16,1);
  font-size: 14px;
  box-shadow: 0px 2px 6px rgba(0,0,0,0.4);
  display: flex;
  flex-direction: column;
  height: 100%;
}
.card-tag{
  margin-top: 20px;
}
.card-button{
  position: absolute;
  bottom: 20px;
  right: 20px;
  padding: 6px 12px;
}
.card-gradient:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Select样式保持不变 */
select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
</style>
