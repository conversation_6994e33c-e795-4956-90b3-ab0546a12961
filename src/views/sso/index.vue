<template>
  <div class="sso-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-circle circle-1"></div>
      <div class="floating-circle circle-2"></div>
      <div class="floating-circle circle-3"></div>
      <div class="floating-circle circle-4"></div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- Logo 区域 -->
      <div class="logo-section">
        <div class="logo-container">
          <img src="@/assets/image/kebao.png" alt="科宝Logo" class="main-logo" />
        </div>
        <h1 class="brand-title">AI 智能助手</h1>
        <p class="brand-subtitle">安全可靠的智能体中台</p>
      </div>
      
      <!-- 加载区域 -->
      <div class="loading-section">
        <!-- 自定义加载动画 -->
        <div class="loading-animation">
          <div class="loading-ring">
            <div class="ring-segment segment-1"></div>
            <div class="ring-segment segment-2"></div>
            <div class="ring-segment segment-3"></div>
            <div class="ring-segment segment-4"></div>
          </div>
          <div class="loading-dots">
            <span class="dot dot-1"></span>
            <span class="dot dot-2"></span>
            <span class="dot dot-3"></span>
          </div>
        </div>
        
        <!-- 加载文字 -->
        <div class="loading-text-container">
          <h2 class="loading-title">正在为您安全登录</h2>
          <p class="loading-subtitle">验证身份信息中，请稍候...</p>
          
          <!-- 进度提示 -->
          <div class="progress-steps">
            <div class="step active">
              <div class="step-icon">
                <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <span class="step-text">接收授权码</span>
            </div>
            <div class="step active">
              <div class="step-icon">
                <div class="loading-spinner"></div>
              </div>
              <span class="step-text">验证身份</span>
            </div>
            <div class="step">
              <div class="step-icon">
                <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <span class="step-text">登录成功</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 安全提示 -->
      <div class="security-notice">
        <div class="security-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.4 16,13V16C16,17.4 15.4,18 14.8,18H9.2C8.6,18 8,17.4 8,16V13C8,12.4 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z"/>
          </svg>
        </div>
        <p>采用企业级安全加密，保护您的隐私安全</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import {onMounted} from 'vue'
import {validateSsoLogin} from "@/api/sso.js";
import {setToken} from '@/utils/auth.js';
import {getUserPermit} from '@/api/auth.js'
import {useUserStore} from '@/stores/user'
import {useRoute, useRouter} from 'vue-router'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

onMounted(() => {
  init()
})

function init() {
  if (route?.query?.code) {
    // 去获取用户信息，登录
    validateSsoLogin(route.query.code).then(async (res) => {
      if (res.code === 200) {
        // 设置token
        setToken(res.data.tokenValue)
        
        // 获取用户详细信息
        const {data} = await getUserPermit()
        
        // 构造用户信息对象
        let userInfo = {
          userId: data?.user?.userId,
          username: data?.user?.username,
          nickName: data?.user?.nickName,
          type: data?.user?.userType,
          lastLoginTime: data?.user?.lastLoginTime
        }
        
        // 使用userStore设置用户信息，这会自动更新sessionStorage并触发响应式更新
        userStore.setUserInfo(userInfo)
        
        // 同时保存完整的用户权限信息
        localStorage.setItem('userPermit', JSON.stringify(data))
        
        // 触发用户信息更新事件，通知其他组件刷新
        window.dispatchEvent(new CustomEvent('user-info-updated'))
        
        // 跳转到首页
        router.push('/')
      }
    })
  }
}

</script>
<style scoped>
.sso-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 80px;
  height: 80px;
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.circle-4 {
  width: 100px;
  height: 100px;
  bottom: 15%;
  right: 10%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 主要内容 */
.main-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 48px 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 480px;
  width: 90%;
  text-align: center;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Logo 区域 */
.logo-section {
  margin-bottom: 40px;
}

.logo-container {
  position: relative;
  display: inline-block;
  margin-bottom: 24px;
}

.main-logo {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
  animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
  from {
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  }
  to {
    box-shadow: 0 12px 32px rgba(102, 126, 234, 0.5);
  }
}

.brand-title {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
  font-weight: 500;
}

/* 加载区域 */
.loading-section {
  margin-bottom: 32px;
}

/* 加载动画 */
.loading-animation {
  position: relative;
  margin: 0 auto 32px auto;
  width: 120px;
  height: 120px;
}

.loading-ring {
  position: absolute;
  width: 100%;
  height: 100%;
}

.ring-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid transparent;
}

.segment-1 {
  border-top-color: #667eea;
  animation: spin 2s linear infinite;
}

.segment-2 {
  border-right-color: #764ba2;
  animation: spin 2s linear infinite reverse;
  animation-delay: 0.5s;
}

.segment-3 {
  border-bottom-color: #f093fb;
  animation: spin 2s linear infinite;
  animation-delay: 1s;
}

.segment-4 {
  border-left-color: #f5576c;
  animation: spin 2s linear infinite reverse;
  animation-delay: 1.5s;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 6px;
}

.dot {
  width: 8px;
  height: 8px;
  background: #667eea;
  border-radius: 50%;
  animation: dotPulse 1.5s ease-in-out infinite;
}

.dot-2 {
  animation-delay: 0.3s;
}

.dot-3 {
  animation-delay: 0.6s;
}

@keyframes dotPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.7;
  }
}

/* 加载文字 */
.loading-text-container {
  text-align: center;
}

.loading-title {
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.loading-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0 0 24px 0;
}

/* 进度步骤 */
.progress-steps {
  display: flex;
  justify-content: space-between;
  margin: 24px 0;
  position: relative;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  height: 2px;
  background: linear-gradient(90deg, #667eea 50%, #e2e8f0 50%);
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  z-index: 2;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.step.active .step-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.step-icon .icon {
  width: 20px;
  height: 20px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.step-text {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.step.active .step-text {
  color: #667eea;
  font-weight: 600;
}

/* 安全提示 */
.security-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.security-icon {
  width: 24px;
  height: 24px;
  color: #38b2ac;
  flex-shrink: 0;
}

.security-notice p {
  margin: 0;
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .main-content {
    padding: 32px 24px;
    margin: 20px;
  }
  
  .brand-title {
    font-size: 24px;
  }
  
  .loading-title {
    font-size: 20px;
  }
  
  .main-logo {
    width: 64px;
    height: 64px;
  }
  
  .loading-animation {
    width: 100px;
    height: 100px;
  }
  
  .progress-steps {
    flex-direction: column;
    gap: 16px;
  }
  
  .progress-steps::before {
    display: none;
  }
  
  .step {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
  }
  
  .step-icon {
    margin-right: 12px;
    margin-bottom: 0;
  }
}

/* 优雅的加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-content {
  animation: fadeInUp 0.8s ease-out;
}

.logo-section {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.loading-section {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.security-notice {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}
</style>
