<template>
  <div class="mx-auto min-h-screen bg-sky-50">
    <div class="max-w-[1920px] mx-auto px-[120px]">
    <!-- 上部分：工作台介绍和搜索 -->
    <div class="flex justify-between items-start py-8 mb-8 ml-20">
      <div>
        <h1 class="mb-2 text-xl font-bold text-gray-800">工作台</h1>
        <p class="text-gray-600">个性化工作空间，管理进行中的多任务流程</p>
      </div>
      <div class="flex gap-3 items-center">
        <div class="relative h-9">
          <select class="px-4 pr-8 h-full leading-tight text-gray-700 bg-white rounded-md border border-gray-200 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500" v-model="selectedType">
            <option value="0">不限领域</option>
            <option value="1">日常办公</option>
            <option value="2">专业工作</option>
          </select>
          <div class="flex absolute inset-y-0 right-0 items-center px-2 text-gray-700 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>
          </div>
        </div>
        <div class="relative h-9">
          <input type="text" placeholder="搜索" class="px-4 pl-10 h-full rounded-md border border-gray-200 w-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400 lucide lucide-search"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 中部分：日常办公推荐 -->
    <div class="mt-10 mb-8 ml-20" v-if="(selectedType == 1 || selectedType == 0) && showDailyOffice">
      <h2 class="flex items-center mb-4 text-base font-semibold text-gray-800">
        <div class="mr-2 w-1 h-6 bg-blue-500"></div>
        日常办公推荐
      </h2>
      <div class="grid grid-cols-1 gap-y-10 gap-x-12 mt-10 md:grid-cols-2 lg:grid-cols-4">
        <div v-for="(item, index) in dailyOfficeTools" :key="index" class="relative p-5 rounded-[5px] card-gradient">
          <div class="absolute top-4 right-4">
            <img :src="item.image" alt="工具图标" class="w-16 h-16" />
          </div>
          <h3 class="mb-4 text-lg font-bold text-gray-800">{{ item.title }}</h3>
          <div class="mb-6">
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-1.5 mr-2 w-2 h-2 bg-blue-500 rounded-full"></div>
              <div>
                <p class="text-sm font-medium text-gray-600">场景：</p>
                <p class="text-gray-700">{{ item.scenario }}</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-1.5 mr-2 w-2 h-2 bg-blue-500 rounded-full"></div>
              <div>
                <p class="text-sm font-medium text-gray-600">功能：</p>
                <p class="text-gray-700">{{ item.function }}</p>
              </div>
            </div>
          </div>
          <div class="card-button">
            <el-button
              type="primary"
              class="text-sm text-white transition-colors bg-[#596df4] rounded hover:bg-[#596df4]"
              @click="handleApply(item)"
            >
              +开始使用
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 下部分：专业工作领域推荐 -->
    <div class="mt-10 mb-8 ml-20" v-if="selectedType==2 || selectedType == 0">
      <h2 class="flex items-center mb-4 text-base font-semibold text-gray-800">
        <div class="mr-2 w-1 h-6 bg-blue-500"></div>
        专业工作领域推荐
      </h2>
      <div class="grid grid-cols-1 gap-y-10 gap-x-12 mt-10 md:grid-cols-2 lg:grid-cols-4">
        <div v-for="(item, index) in professionalTools" :key="index" class="relative p-5 rounded-[5px] card-gradient">
          <div class="absolute top-4 right-4">
            <img :src="item.image" alt="工具图标" class="w-16 h-16" />
          </div>
          <h3 class="mb-4 text-lg font-bold text-gray-800">{{ item.title }}</h3>
          <div class="mb-6">
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-1.5 mr-2 w-2 h-2 bg-blue-500 rounded-full"></div>
              <div>
                <p class="text-sm font-medium text-gray-600">场景：</p>
                <p class="text-gray-700">{{ item.scenario }}</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-1.5 mr-2 w-2 h-2 bg-blue-500 rounded-full"></div>
              <div>
                <p class="text-sm font-medium text-gray-600">{{ item.secondLabel }}：</p>
                <p class="text-gray-700">{{ item.secondContent }}</p>
              </div>
            </div>
          </div>
          <div class="card-button">
            <el-button
              type="primary"
              class="text-sm text-white transition-colors bg-[#596df4] rounded hover:bg-[#596df4]"
              @click="handleApply(item)"
            >
              +开始使用
            </el-button>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>

</template>

<script>
import { getAgentList } from '@/api/ai'

export default {
  name: 'WorkPlatform',
  data() {
    return {
      selectedType: 0,
      showDailyOffice: true, // 控制是否显示日常办公推荐
      // 预定义的智能体配置
      predefinedDailyOfficeTools: [
        {
          title: '公文办公',
          scenario: '辅助书写公文与可研报告',
          function: '智能辅助公文起草、流转、审批，提供自动格式校验、智能摘要、政策匹配、语法优化等功能，提高政务办公效率',
          image: new URL(`/src/assets/image/work1.png`, import.meta.url).href,
          id: '799592ca740609d28525d878d539fdc1'
        },
        {
          title: '项目可行性研究报告智能体',
          scenario: '项目可行性研究报告',
          function: '基于大数据和行业模型，智能生成项目可行性研究报告，涵盖市场分析、财务测算、政策合规、风险评估等内容',
          image: new URL(`/src/assets/image/work2.png`, import.meta.url).href,
          id: 'a208e9b4df25831fbdce2da0c3cbf1d4'
        }
      ],
      predefinedProfessionalTools: [
      {
          id: '8a02743202c1c8d07054d22d85127efd',
          title: '人力制度咨询智能体',
          scenario: '专业领域',
          secondLabel: '功能',
          secondContent: '交科人力制度咨询',
          image: new URL(`/src/assets/image/work2.png`, import.meta.url).href
        },
        {
          id: "f42f18f84b4f32af37d3cd7ea22f3fae",
          title: "交通规范智能体",
          scenario: "交通,法规",
          secondLabel: '功能',
          image: new URL(`/src/assets/image/work5.png`, import.meta.url).href,
          secondContent: "交通规范问答"
        },
        {
          id: 'e743fbaf3116f1d32b8ddccb3bee19d2',
          title: '科宝交通基础大模型',
          scenario: '交通',
          secondLabel: '功能',
          secondContent: '科宝交通基础大模型测试',
          image: new URL(`/src/assets/image/work2.png`, import.meta.url).href
        },
        {
          id: '89c5e975cbb566c002b2fec3bfa0f133',
          title: '巡查智能体',
          scenario: '边坡监测',
          secondLabel: '功能',
          secondContent: '基于AI的公路灾害智能监测智能体，可精准识别边坡滑坡灾害',
          image: new URL(`/src/assets/image/work2.png`, import.meta.url).href
        }
      ],
      // 实际显示的智能体列表
      dailyOfficeTools: [],
      professionalTools: []
    }
  },
  async mounted() {
    await this.loadAgentData()
  },
  methods: {
    async loadAgentData() {
      try {
        // 调用智能体广场接口，获取所有智能体数据
        const response = await getAgentList({
          pageNo: '1',
          pageSize: '100' // 获取足够多的数据用于匹配
        })
        
        if (response.rows) {
          const agentList = response.rows
          const agentMap = {}
          
          // 构建智能体ID映射表
          agentList.forEach(agent => {
            agentMap[agent.agentId] = agent
          })
          
          // 匹配日常办公智能体
          this.dailyOfficeTools = this.matchAgents(this.predefinedDailyOfficeTools, agentMap)
          
          // 如果日常办公智能体都没有匹配到，则隐藏日常办公推荐
          this.showDailyOffice = this.dailyOfficeTools.length > 0
          
          // 匹配专业工作智能体
          const matchedProfessional = this.matchAgents(this.predefinedProfessionalTools, agentMap)
          
          if (matchedProfessional.length > 0) {
            // 如果有匹配到的专业智能体，则显示匹配到的
            this.professionalTools = matchedProfessional
          } else {
            // 如果专业智能体都没有匹配到，则筛选并显示智能体广场中的专业领域智能体前2个
            const professionalAgents = this.filterProfessionalAgents(agentList)
            if (professionalAgents.length > 0) {
              this.professionalTools = this.convertApiAgentsToDisplayFormat(professionalAgents.slice(0, 2))
            } else {
              // 如果没有找到专业领域智能体，则不显示任何智能体
              this.professionalTools = []
            }
          }
          
        } else {
          console.error('获取智能体列表失败:', response.msg)
          // 接口调用失败时，使用默认数据
          this.useDefaultData()
        }
      } catch (error) {
        console.error('调用智能体广场接口出错:', error)
        // 网络错误时，使用默认数据
        this.useDefaultData()
      }
    },
    
    // 匹配智能体
    matchAgents(predefinedAgents, agentMap) {
      const matchedAgents = []
      
      predefinedAgents.forEach(predefined => {
        if (agentMap[predefined.id]) {
          // 找到匹配的智能体，使用预定义的显示配置
          matchedAgents.push({
            ...predefined,
            // 可以选择性地使用接口返回的一些字段
            // title: agentMap[predefined.id].agentName || predefined.title,
            // scenario: agentMap[predefined.id].usageScenarios || predefined.scenario
          })
        }
      })
      
      return matchedAgents
    },
    
    // 筛选专业领域智能体
    filterProfessionalAgents(agentList) {
      return agentList.filter(agent => {
        if (!agent.agentTag) {
          return false
        }
        
        // 将agentTag按中文逗号或英文逗号分割
        const tags = agent.agentTag.split(/[，,]/).map(tag => tag.trim())
        
        // 检查是否包含"专业领域"
        return tags.includes('专业领域')
      })
    },
    
    // 将API返回的智能体数据转换为显示格式
    convertApiAgentsToDisplayFormat(apiAgents) {
      const defaultImages = [
        new URL(`/src/assets/image/work5.png`, import.meta.url).href,
        new URL(`/src/assets/image/work6.png`, import.meta.url).href,
        new URL(`/src/assets/image/work8.png`, import.meta.url).href
      ]
      
      return apiAgents.map((agent, index) => ({
        id: agent.agentId,
        title: agent.agentName || '智能体',
        scenario: agent.usageScenarios || '智能辅助',
        secondLabel: '功能',
        secondContent: agent.agentDesc || '提供智能化服务',
        image: agent.icon || defaultImages[index % defaultImages.length]
      }))
    },
    
    // 使用默认数据（接口调用失败时的降级处理）
    useDefaultData() {
      this.dailyOfficeTools = [...this.predefinedDailyOfficeTools]
      this.professionalTools = [...this.predefinedProfessionalTools]
      this.showDailyOffice = true
    },
    
    handleApply(item) {
      // 检查是否是公文办公智能体
      if (item.id === '799592ca740609d28525d878d539fdc1') {
        this.$router.push('/document-office')
      } else {
        this.$router.push(`/new-dialog/${item.id}/${encodeURIComponent(item.title)}`)
      }
    }
  }
}
</script>

<style scoped>
/* 自定义样式补充 */
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 确保卡片内容有足够的右侧空间，避免与图标重叠 */
h3, .space-y-3 {
  width: calc(100% - 70px);
}

.card-gradient {
  position: relative;
  background: linear-gradient(30deg, rgba(89,109,244,0.14) 13.4%, rgba(174,209,255,0.34) 85.87%);
  color: rgba(16,16,16,1);
  font-size: 14px;
  box-shadow: 0px 2px 6px rgba(0,0,0,0.4);
  min-height: 251px;
}
.card-button{
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  bottom: 18px;
  width: 100%;
  border: none;
}
</style>
