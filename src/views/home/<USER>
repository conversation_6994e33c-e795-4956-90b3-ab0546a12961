<template>
  <div class="flex min-h-screen home-container">
    <!-- 左侧导航栏已经完成，这里只实现右侧内容 -->
    <div class="flex flex-col w-full main-content">
      
      <!-- 顶部内容区域 -->
      <div class="flex flex-col items-center pt-8 top-content">
        <!-- 顶部机器人头像和问候区域 -->
        <div class="flex justify-center items-center mb-8 w-full max-w-4xl robot-greeting-section">
          <div class="mr-3 robot-avatar">
            <img src="@/assets/image/kebao.gif" alt="科宝机器人" class="h-32" />
          </div>
          <div class="greeting-text">
            <h1 class="text-2xl font-bold text-txt-primary">你好，我是科宝。有什么可以帮你的吗？</h1>
          </div>
        </div>

        <!-- 功能卡片区域 -->
        <div class="mb-8 w-full max-w-4xl cards-section">
        <div class="flex cards-container" style="margin-top: 1.5cm;">
          <!-- 左侧卡片组 -->
          <div class="pr-2 w-1/2 cards" v-if="showDailyOffice">
            <div class="flex justify-center items-center mb-3 card-header">
              <div class="flex items-center">
                <el-icon class="text-[#596DF4] mr-1"><document /></el-icon>
                <span class="font-medium">日常办公推荐</span>
              </div>
            </div>
            
            <div :class="dailyOfficeTools.length === 1 ? 'flex justify-center' : 'grid grid-cols-2 gap-3'" class="card-grid">
              <!-- 卡片1 -->
              <div 
                v-for="tool in dailyOfficeTools" 
                :key="tool.id" 
                :class="dailyOfficeTools.length === 1 ? 'w-1/2' : ''"
                class="p-4 rounded-lg cursor-pointer feature-card h-[200px] overflow-hidden flex flex-col items-center"
                @click="navigateToTool(tool)"
              >
                <div class="flex justify-center items-center h-[100px] mb-2 w-full">
                  <img :src="tool.image" alt="Tool Image" class="object-contain max-h-full" />
                </div>
                <h3 class="mb-1 text-base font-medium truncate text-center w-full">{{ tool.title }}</h3>
                <p class="text-xs text-gray-600 line-clamp-2 text-center w-full">{{ tool.secondContent }}</p>
              </div>
            </div>
          </div>
          
          <!-- 右侧卡片组 / 居中卡片组 -->
          <div class="cards" :class="showDailyOffice ? 'w-1/2 pl-2' : 'w-full flex justify-center'">
            <div :class="showDailyOffice ? 'w-full' : 'w-1/2'">
              <div class="flex justify-center items-center mb-3 card-header">
                <div class="flex items-center">
                  <el-icon class="text-[#596DF4] mr-1"><monitor /></el-icon>
                  <span class="font-medium">专业工作领域</span>
                </div>
              </div>
              
              <div :class="professionalTools.length === 1 ? 'flex justify-center' : 'grid grid-cols-2 gap-3'" class="card-grid">
                <div 
                  v-for="tool in professionalTools" 
                  :key="tool.id || tool.agentId" 
                  :class="professionalTools.length === 1 ? 'w-1/2' : ''"
                  class="feature-card bg-[#EEF0FE] p-4 rounded-lg cursor-pointer h-[200px] overflow-hidden flex flex-col items-center"
                  @click="navigateToTool(tool)"
                >
                  <div class="flex justify-center items-center h-[100px] mb-2 w-full">
                    <img :src="tool.image" alt="Tool Image" class="object-contain max-h-full" />
                  </div>
                  <h3 class="mb-1 text-base font-medium truncate text-center w-full">{{ tool.title }}</h3>
                  <p class="text-xs text-gray-600 line-clamp-2 text-center w-full">{{ tool.secondContent }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>    
        </div>
      </div>

      <!-- 中间空白区域 -->
      <div class="flex-1"></div>

      <!-- 底部输入框区域 -->
      <div class="flex justify-center pb-8 bottom-content" style="transform: translateY(-1cm);">
        <div class="w-full max-w-4xl input-section">

        <!-- 输入区域 -->
        <div class="input-wrapper">
          <div class="flex relative flex-col p-4 bg-white rounded-lg shadow-md input-container">
            <!-- 文件预览区域 -->
            <div class="file-preview-container">
              <div v-if="uploadedFiles.length > 0" class="flex flex-wrap gap-2 mb-3 uploaded-files-preview">
                <div 
                  v-for="(file, index) in uploadedFiles" 
                  :key="file.fileId"
                  class="flex items-center p-2 bg-gray-50 rounded-lg file-preview-item"
                >
                  <span class="mr-2">{{ getFileIcon(file.fileType) }}</span>
                  <span class="text-sm text-gray-600 cursor-pointer hover:text-primary" @click="previewFile(file)">
                    {{ file.fileName }}
                  </span>
                  <button 
                    class="ml-2 text-gray-400 hover:text-red-500"
                    @click="removeFile(index)"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- 输入框区域 -->
            <div class="input-area">
              <textarea
                v-model="userInput"
                placeholder="你可以问我任何问题，或说出我如何帮助你..."
                class="p-2 w-full text-gray-700 align-top outline-none resize-none input-textarea"
                @keyup.enter.prevent="handleEnterPress"
                rows="3"
              ></textarea>
            </div>

            <!-- 底部工具栏 -->
            <div class="bottom-toolbar">
              <div class="flex justify-between items-center">
                <!-- 左侧标签按钮 -->
                <div class="flex gap-2 items-center ml-4">
                  <button 
                    @click="toggleMode('deep')" 
                    class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
                    :class="[
                      activeModes.includes('deep')
                        ? 'bg-primary text-white' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    ]"
                  >
                    <div class="flex gap-1 items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12z"></path><path d="M12 8v8"></path><path d="M8 12h8"></path></svg>
                      深度思考
                    </div>
                  </button>
                  <button 
                    v-if="showSearchMode"
                    @click="toggleMode('search')" 
                    class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
                    :class="[
                      activeModes.includes('search')
                        ? 'bg-primary text-white' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    ]"
                  >
                    <div class="flex gap-1 items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                      联网搜索
                    </div>
                  </button>
                  <button 
                    v-if="showKnowledgeSelection"
                    @click="showDatabaseSelection = true" 
                    class="px-3 py-1 text-sm rounded-full transition-colors duration-200"
                    :class="[
                      selectedKnowledgeList.length > 0
                        ? 'bg-primary text-white' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    ]"
                  >
                    <div class="flex gap-1 items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/><path d="M16 8V6H8v2"/><path d="M16 12V10H8v2"/><path d="M16 16V14H8v2"/></svg>
                      知识库选择
                      <span v-if="selectedKnowledgeList.length > 0" class="ml-1 px-1.5 py-0.5 text-xs bg-white bg-opacity-30 rounded-full">
                        {{ selectedKnowledgeList.length }}
                      </span>
                    </div>
                  </button>
                </div>

                <!-- 右侧工具栏 -->
                <div class="flex gap-2 items-center mr-4 text-gray-500">
                  <el-upload
                    class="upload-button"
                    style="margin-top: 6px;"
                    :action="`/api/v1/public/chat/uploadFile`"
                    :data="{
                      agentId: '9bad24bea86d3d7b2a1eff108ed7e7ec'
                    }"
                    :show-file-list="false"
                    :on-success="handleUploadSuccess"
                    :on-error="handleUploadError"
                    :before-upload="beforeUpload"
                  >
                    <button class="p-2 rounded-full hover:bg-gray-100">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-paperclip"><path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"/></svg>
                    </button>
                  </el-upload>
                  
                  <button 
                    @click="handleSendMessage" 
                    class="p-2 text-white rounded-full bg-primary hover:bg-primary-middle"
                    :disabled="isLoading"
                  >
                    <svg v-if="!isLoading" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="animate-spin"><path d="M21 12a9 9 0 1 1-6.219-8.56"/></svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
      
      <!-- 右下角3D图片 - 已注释掉 -->
      <!-- <div class="fixed bottom-20 right-48 z-0 illustration">
        <img src="@/assets/image/ai.png" alt="AI助手" class="w-30 h-30" />
      </div> -->
    </div>

    <!-- 添加图片预览组件 -->
    <el-image
      v-if="previewVisible"
      :src="previewUrl"
      :preview-src-list="[previewUrl]"
      hide-on-click-modal
      @close="previewVisible = false"
    />

    <!-- 版权信息 -->
    <footer class="mt-6 text-center fixed text-[#596DF4]" style="bottom: 5px;">
      © 广西交科集团有限公司
    </footer>

    <!-- 知识库选择弹框 -->
    <DatabaseSelection
      v-model="showDatabaseSelection"
      :selected-ids="selectedKnowledgeList.map(item => item.id)"
      @confirm="handleKnowledgeSelection"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Document, 
  Picture, 
  Microphone, 
  Position, 
  Monitor, 
  Paperclip,
  Loading
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElImageViewer } from 'element-plus'
import { getAgentDetail, getAgentList } from '@/api/ai'
import { login, loginUserInfo } from '@/api/auth'
import { useUserStore } from '@/stores/user'
import DatabaseSelection from '@/components/DatabaseSelection.vue'

    const router = useRouter()
const userStore = useUserStore()
    const userInput = ref('')
    const isLoading = ref(false)
    const uploadedFiles = ref([])
    const previewVisible = ref(false)
    const previewUrl = ref('')
    const isRecording = ref(false)
    const mediaRecorder = ref(null)
    const audioChunks = ref([])
    const activeModes = ref([])
    const showSearchMode = ref(true)
    const showKnowledgeSelection = ref(true) // 控制知识库选择按钮显示
    const showDatabaseSelection = ref(false)
    const selectedKnowledgeList = ref([])
    const showDailyOffice = ref(true) // 控制是否显示日常办公推荐

// 检查是否需要匿名登录
const checkAnonymousLogin = async () => {
  const urlParams = new URLSearchParams(window.location.search)
  const loginFree = urlParams.get('loginfree')
  
  if (loginFree === '1') {
    try {
      // 使用指定的账号密码登录
      const loginRes = await login({
        username: 'jttKejiaochu',
        password: 'eT7^sX9@gxjk123',
        type: 2  // 管理员类型
      })
      localStorage.setItem('token', loginRes.data.tokenValue)
      localStorage.setItem('userInfo', JSON.stringify(loginRes.data))
      if (loginRes.code === 200) {
        // 获取用户信息
        await userStore.fetchUserInfo()
        
        // 触发登录成功事件，通知管理端按钮刷新权限
        window.dispatchEvent(new CustomEvent('login-success'))
      } else {
        ElMessage.error('登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error('登录失败')
    }
  }
}
    
    // 获取agent详情
    const fetchAgentDetail = async () => {
      try {
        // 之前默认的id是1902218668492697602
        const response = await getAgentDetail({ agentId: '9bad24bea86d3d7b2a1eff108ed7e7ec' });
        if (response && response.data) {
          // 如果onlineSearch为0，则不显示联网搜索按钮
          showSearchMode.value = response.data.onlineSearch;
          // 如果selectKnow为0，则不显示知识库选择按钮
          showKnowledgeSelection.value = response.data.selectKnow !== 0;
        }
      } catch (error) {
        console.error('Failed to get agent detail:', error);
      }
    }
    
// 页面挂载时获取agent详情和检查匿名登录
    onMounted(async () => {
      // 清理所有初始消息发送标记，确保从Home重新跳转时能正常发送消息
      clearInitialMessageFlags();

      await fetchAgentDetail();
      await checkAnonymousLogin();
      await loadAgentData(); // 加载智能体数据
      
      // 监听登录成功事件，重新加载智能体数据
      window.addEventListener('login-success', handleLoginSuccess);
    });

    // 处理登录成功事件
    const handleLoginSuccess = async () => {
      // 登录成功后重新加载智能体数据，确保基于正确的用户权限
      await loadAgentData();
    };

    // 组件卸载时清理事件监听器
    onUnmounted(() => {
      window.removeEventListener('login-success', handleLoginSuccess);
    });

    // 存储分离的知识库ID和文档ID
    const selectedKnowledgeIds = ref([])
    const selectedDocumentIds = ref([])
    
    // 处理知识库选择
    const handleKnowledgeSelection = (selection) => {
      selectedKnowledgeList.value = selection.list
      selectedKnowledgeIds.value = selection.knowledgeIds || []
      selectedDocumentIds.value = selection.documentIds || []
      console.log('选择的知识库:', selection)
      console.log('知识库IDs:', selection.knowledgeIds)
      console.log('文档IDs:', selection.documentIds)
    }

    const handleSendMessage = async (event) => {
      // 如果是事件对象，阻止默认行为
      if (event && typeof event === 'object' && event.preventDefault) {
        event.preventDefault();
      }
      
      if ((!userInput.value.trim() && uploadedFiles.value.length === 0) || isLoading.value) return
      
      isLoading.value = true
      
      try {
        // 准备文件信息
        const files = uploadedFiles.value.map(file => ({
          fileId: file.fileId,
          fileType: file.fileType,
          extension: file.extension,
          fileName: file.fileName
        }))
        
        // 确保 query 传递的是用户输入的信息
        const initialMessage = userInput.value.trim();
        
        // 使用已经分离好的知识库ID和文档ID
        const knowledgeIds = [...selectedKnowledgeIds.value]
        const documentIds = [...selectedDocumentIds.value]
        
        console.log('发送消息时的知识库IDs:', knowledgeIds)
        console.log('发送消息时的文档IDs:', documentIds)
        
        // 跳转到新对话页面，并传递初始消息和文件
        await router.push({
          name: 'NewDialog',
          params: {
            agentId: '9bad24bea86d3d7b2a1eff108ed7e7ec',
            agentName: '科宝'
          },
          query: {
            initialMessage: initialMessage,
            files: JSON.stringify(files),
            modes: JSON.stringify(activeModes.value),
            knowledgeList: JSON.stringify(selectedKnowledgeList.value),
            knowledgeIds: JSON.stringify(knowledgeIds),
            documentIds: JSON.stringify(documentIds)
          }
        })
      } catch (error) {
        console.error('Navigation error:', error)
      } finally {
        isLoading.value = false
      }
    }
    
    const getFileType = (filename) => {
      const extension = filename.split('.').pop().toUpperCase()
      
      const typeMap = {
        DOCUMENT: ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
        IMAGE: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
        AUDIO: ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
        VIDEO: ['MP4', 'MOV', 'MPEG', 'MPGA']
      }
      
      for (const [type, extensions] of Object.entries(typeMap)) {
        if (extensions.includes(extension)) {
          return {
            type,
            extension
          }
        }
      }
      
      return {
        type: 'CUSTOM',
        extension
      }
    }

    const getFileIcon = (fileType) => {
      const icons = {
        'DOCUMENT': '📄',
        'IMAGE': '🖼️',
        'AUDIO': '🎵',
        'VIDEO': '🎥',
        'CUSTOM': '📎'
      }
      return icons[fileType] || '📎'
    }

    const previewFile = (file) => {
      // 图片预览
      if (file.fileType === 'IMAGE') {
        const img = new Image()
        img.src = URL.createObjectURL(file.raw)
        
        // 创建图片预览实例
        const container = document.createElement('div')
        const vnode = createVNode(ElImageViewer, {
          urlList: [img.src],
          onClose: () => {
            render(null, container)
            document.body.removeChild(container)
          }
        })
        
        document.body.appendChild(container)
        render(vnode, container)
      } 
      // PDF预览
      else if (file.fileType === 'DOCUMENT' && ['PDF'].includes(file.extension)) {
        window.open(URL.createObjectURL(file.raw))
      } 
      // 文本文件预览
      else if (file.fileType === 'DOCUMENT' && ['TXT', 'MD', 'HTML', 'CSV'].includes(file.extension)) {
        const reader = new FileReader()
        reader.onload = (e) => {
          ElMessageBox.alert(e.target.result, '文件预览', {
            customClass: 'text-preview-dialog',
            dangerouslyHtmlString: file.extension === 'HTML'
          })
        }
        reader.readAsText(file.raw)
      }
      // 音频预览
      else if (file.fileType === 'AUDIO') {
        const audio = new Audio(URL.createObjectURL(file.raw))
        audio.play()
      }
      // 视频预览
      else if (file.fileType === 'VIDEO') {
        ElMessageBox.alert(
          `<video controls style="max-width: 100%">
            <source src="${URL.createObjectURL(file.raw)}" type="video/${file.extension.toLowerCase()}">
          </video>`,
          '视频预览',
          {
            dangerouslyHtmlString: true,
            customClass: 'video-preview-dialog'
          }
        )
      }
      else {
        ElMessage.info('该文件类型暂不支持预览')
      }
    }

    const removeFile = (index) => {
      uploadedFiles.value.splice(index, 1)
    }

    const beforeUpload = (file) => {
      // 检查是否已经上传了文件
      if (uploadedFiles.value.length >= 1) {
        ElMessage.error('每次对话只能上传一个文件!')
        return false
      }
      
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        ElMessage.error('文件大小不能超过 50MB!')
        return false
      }
      return true
    }

    const handleUploadSuccess = (response, file) => {
      if (response.code === 200) {
        const { type, extension } = getFileType(file.name)
        
        uploadedFiles.value.push({
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name,
          raw: file.raw,
          size: file.size
        })
        
        ElMessage.success({
          message: '文件上传成功',
          duration: 2000
        })
      } else {
        ElMessage.error('文件上传失败')
      }
    }

    const handleUploadError = () => {
      ElMessage.error('文件上传失败，请重试')
    }

    // 开始/停止录音
    const toggleRecording = async () => {
      if (!isRecording.value) {
        try {
          // 检查浏览器是否支持 getUserMedia
          if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            ElMessage.error('您的浏览器不支持录音功能')
            return
          }

          // 请求麦克风权限前先检查权限状态
          const permissionStatus = await navigator.permissions.query({ name: 'microphone' })
          if (permissionStatus.state === 'denied') {
            ElMessage.error({
              message: '请允许访问麦克风权限（可在浏览器设置中修改）',
              duration: 3000
            })
            return
          }

          // 请求麦克风权限
          const stream = await navigator.mediaDevices.getUserMedia({ 
            audio: {
              echoCancellation: true,  // 回声消除
              noiseSuppression: true,  // 噪声抑制
              sampleRate: 44100        // 采样率
            } 
          })
          
          // 创建 MediaRecorder 实例
          mediaRecorder.value = new MediaRecorder(stream)
          audioChunks.value = []
          
          // 收集录音数据
          mediaRecorder.value.ondataavailable = (event) => {
            audioChunks.value.push(event.data)
          }
          
          // 录音结束后的处理
          mediaRecorder.value.onstop = () => {
            // 创建音频 Blob
            const audioBlob = new Blob(audioChunks.value, { type: 'audio/mp3' })
            
            // 创建音频文件
            const file = new File([audioBlob], `recording_${Date.now()}.mp3`, {
              type: 'audio/mp3'
            })
            
            // 将录音作为文件上传
            const formData = new FormData()
            formData.append('file', file)
            formData.append('userId', 'user-123')
            formData.append('agentId', '9bad24bea86d3d7b2a1eff108ed7e7ec')
            
            // 使用 fetch 上传文件
            fetch('/api/v1/public/chat/uploadFile', {
              method: 'POST',
              body: formData
            })
            .then(response => response.json())
            .then(response => {
              if (response.code === 200) {
                // 添加到上传文件列表
                uploadedFiles.value.push({
                  fileId: response.data.fileId,
                  fileType: 'AUDIO',
                  extension: 'MP3',
                  fileName: file.name,
                  raw: file,
                  size: file.size
                })
                
                ElMessage.success({
                  message: '录音上传成功',
                  duration: 2000
                })
              } else {
                ElMessage.error('录音上传失败')
              }
            })
            .catch(error => {
              console.error('Upload error:', error)
              ElMessage.error('录音上传失败')
            })
            
            // 停止所有音轨
            stream.getTracks().forEach(track => track.stop())
          }
          
          // 开始录音
          mediaRecorder.value.start()
          isRecording.value = true
          
          // 显示开始录音提示
          ElMessage.success({
            message: '开始录音...',
            duration: 2000
          })
          
        } catch (error) {
          console.error('Recording error:', error)
          
          // 根据具体错误类型显示不同的错误信息
          if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
            ElMessage.error({
              message: '请允许访问麦克风权限（可在浏览器设置中修改）',
              duration: 3000
            })
          } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
            ElMessage.error({
              message: '未检测到麦克风设备，请检查设备连接',
              duration: 3000
            })
          } else if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
            ElMessage.error({
              message: '麦克风被其他程序占用，请关闭其他使用麦克风的程序',
              duration: 3000
            })
          } else {
            ElMessage.error({
              message: '无法访问麦克风，请检查设备连接和浏览器权限设置',
              duration: 3000
            })
          }
        }
      } else {
        // 停止录音
        if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
          mediaRecorder.value.stop()
          isRecording.value = false
          
          // 显示结束录音提示
          ElMessage.success({
            message: '录音结束',
            duration: 2000
          })
        }
      }
    }

    const toggleMode = (mode) => {
      const index = activeModes.value.indexOf(mode)
      if (index === -1) {
        // 如果模式不在数组中，添加它
        activeModes.value.push(mode)
      } else {
        // 如果模式已经在数组中，移除它
        activeModes.value.splice(index, 1)
      }
    }

    const handleEnterPress = (event) => {
      // 如果按下的是 Enter 键且没有按住 Shift 键
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        handleSendMessage()
      }
    }
    
    // 预定义的智能体配置
    const predefinedDailyOfficeTools = [
        {
          title: '公文办公',
          secondContent: '辅助书写公文与可研报告',
          function: '辅助书写公文与可研报告',
          image: new URL(`/src/assets/image/work1.png`, import.meta.url).href,
          id: '799592ca740609d28525d878d539fdc1'
        },
        {
          title: '项目可研报告智能体',
          secondContent: '基于大数据和行业模型，智能生成项目可行性研究报告，涵盖市场分析、财务测算、政策合规、风险评估等内容，提高项目论证的科学性和决策效率。',
          function: '项目可行性研究报告',
          image: new URL(`/src/assets/image/work3.png`, import.meta.url).href,
          id: 'a208e9b4df25831fbdce2da0c3cbf1d4'
        }
    ]

    const predefinedProfessionalTools = [
        {
          id: '8a02743202c1c8d07054d22d85127efd',
          title: '人力制度咨询智能体',
          scenario: '交科人力制度咨询',
          secondLabel: '专业',
          secondContent: '交科人力制度咨询',
          image: new URL(`/src/assets/image/work2.png`, import.meta.url).href
        },
        {
          id: "f42f18f84b4f32af37d3cd7ea22f3fae",
          title: "交通规范智能体",
          scenario: "交通,法规",
          secondLabel: '功能',
          image: new URL(`/src/assets/image/work5.png`, import.meta.url).href,
          secondContent: "交通规范问答"
        }
    ]

    // 科宝交通基础大模型递补数据
    const fallbackAgent = {
      id: 'e743fbaf3116f1d32b8ddccb3bee19d2',
      title: '科宝交通基础大模型',
      scenario: '人力',
      secondLabel: '专业',
      secondContent: '科宝交通基础大模型测试',
      image: new URL(`/src/assets/image/work2.png`, import.meta.url).href
    }

    // 实际显示的智能体列表（响应式数据）
    const dailyOfficeTools = ref([])
    const professionalTools = ref([])

    // 加载智能体数据
    const loadAgentData = async () => {
      try {
        // 调用智能体广场接口，获取所有智能体数据
        const response = await getAgentList({
          pageNo: '1',
          pageSize: '100' // 获取足够多的数据用于匹配
        })
        
        if (response.rows) {
          const agentList = response.rows
          const agentMap = {}
          
          // 构建智能体ID映射表
          agentList.forEach(agent => {
            agentMap[agent.agentId] = agent
          })
          
          // 匹配日常办公智能体
          dailyOfficeTools.value = matchAgents(predefinedDailyOfficeTools, agentMap)
          
          // 如果日常办公智能体都没有匹配到，则隐藏日常办公推荐
          showDailyOffice.value = dailyOfficeTools.value.length > 0
          
          // 匹配专业工作智能体
          const matchedProfessional = matchAgents(predefinedProfessionalTools, agentMap)
          
          if (matchedProfessional.length > 0) {
            // 如果有匹配到的专业智能体
            if (matchedProfessional.length === 1) {
              // 只有一个匹配时，尝试递补科宝交通基础大模型
              if (agentMap[fallbackAgent.id]) {
                // 如果科宝交通基础大模型存在，则添加到列表中
                professionalTools.value = [...matchedProfessional, fallbackAgent]
              } else {
                // 如果科宝交通基础大模型不存在，则继续执行原有逻辑
                professionalTools.value = matchedProfessional
              }
            } else {
              // 有多个匹配时，直接显示匹配到的
              professionalTools.value = matchedProfessional
            }
          } else {
            // 如果专业智能体都没有匹配到，则筛选并显示智能体广场中的专业领域智能体前2个
            const professionalAgents = filterProfessionalAgents(agentList)
            if (professionalAgents.length > 0) {
              professionalTools.value = convertApiAgentsToDisplayFormat(professionalAgents.slice(0, 2))
            } else {
              // 如果没有找到专业领域智能体，则不显示任何智能体
              professionalTools.value = []
            }
          }
          
        } else {
          console.error('获取智能体列表失败:', response.msg)
          // 接口调用失败时，使用默认数据
          useDefaultData()
        }
      } catch (error) {
        console.error('调用智能体广场接口出错:', error)
        // 网络错误时，使用默认数据
        useDefaultData()
      }
    }

    // 匹配智能体
    const matchAgents = (predefinedAgents, agentMap) => {
      const matchedAgents = []
      
      predefinedAgents.forEach(predefined => {
        if (agentMap[predefined.id]) {
          // 找到匹配的智能体，使用预定义的显示配置
          matchedAgents.push({
            ...predefined,
            // 可以选择性地使用接口返回的一些字段
            // title: agentMap[predefined.id].agentName || predefined.title,
            // secondContent: agentMap[predefined.id].usageScenarios || predefined.secondContent
          })
        }
      })
      
      return matchedAgents
    }

    // 筛选专业领域智能体
    const filterProfessionalAgents = (agentList) => {
      return agentList.filter(agent => {
        if (!agent.agentTag) {
          return false
        }
        
        // 将agentTag按中文逗号或英文逗号分割
        const tags = agent.agentTag.split(/[，,]/).map(tag => tag.trim())
        
        // 检查是否包含"专业领域"
        return tags.includes('专业领域')
      })
    }

    // 将API返回的智能体数据转换为显示格式
    const convertApiAgentsToDisplayFormat = (apiAgents) => {
      const defaultImages = [
        new URL(`/src/assets/image/work2.png`, import.meta.url).href,
        new URL(`/src/assets/image/work5.png`, import.meta.url).href
      ]
      
      return apiAgents.map((agent, index) => ({
        id: agent.agentId,
        title: agent.agentName || '智能体',
        scenario: agent.usageScenarios || '智能辅助',
        secondContent: agent.agentDesc || '提供智能化服务',
        image: agent.icon || defaultImages[index % defaultImages.length]
      }))
    }

    // 使用默认数据（接口调用失败时的降级处理）
    const useDefaultData = () => {
      dailyOfficeTools.value = [...predefinedDailyOfficeTools]
      professionalTools.value = [...predefinedProfessionalTools]
      showDailyOffice.value = true
    }

    const navigateToTool = (tool) => {
      // 检查是否是公文办公智能体
      if (tool.id === '799592ca740609d28525d878d539fdc1') {
        router.push('/document-office');
      } else {
        // Use the specified routing logic for navigation
        router.push(`/new-dialog/${tool.id}/${encodeURIComponent(tool.title)}`);
            }
    }

    // 清理所有初始消息发送标记
    const clearInitialMessageFlags = () => {
      try {
        // 获取所有sessionStorage的key
        const keys = Object.keys(sessionStorage);

        // 过滤出所有以 'initialMessage_sent_' 开头的key
        const messageFlags = keys.filter(key => key.startsWith('initialMessage_sent_'));

        // 删除所有相关的标记
        messageFlags.forEach(key => {
          sessionStorage.removeItem(key);
        });

        if (messageFlags.length > 0) {
          console.log(`已清理 ${messageFlags.length} 个初始消息发送标记:`, messageFlags);
        }
      } catch (error) {
        console.error('清理初始消息标记时出错:', error);
    }
  }
</script>

<style scoped>
.home-container {
  width: 100%;
  min-width: 1400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-image: url('@/assets/image/bg.png'); 
  background-size: cover;
  background-position: center;
  background-attachment: fixed;  /* 关键属性：使背景图固定 */
  position: relative;
}

.main-content{
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.top-content {
  flex-shrink: 0;
}

.bottom-content {
  flex-shrink: 0;
}

.robot-greeting-section {
  margin-top: 2rem;
  width: 1000px;
  max-width: 1000px;
}

.robot-avatar img {
  border-radius: 50%;
}

.cards-section {
  max-width: 1000px;
}

.input-section {
  max-width: 1000px;
}

.feature-card {
  background: linear-gradient(30deg, rgba(89,109,244,0.14) 13.4%,rgba(174,209,255,0.34) 85.87%);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(89, 109, 244, 0.1);
}

/* 自定义Element UI组件样式 */
:deep(.el-textarea__inner) {
  border-radius: 8px !important;
  padding: 16px !important;
  font-size: 14px !important;
}

:deep(.el-input-group__append) {
  padding: 0 !important;
  border: none !important;
}

.input-container {
  width: 100%;
  min-height: 10rem;  /* 从14rem改为10rem */
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 1rem;  /* 调整内边距 */
}

/* 添加文件预览相关样式 */
:deep(.text-preview-dialog) {
  max-width: 80vw;
  max-height: 80vh;
}

:deep(.text-preview-dialog .el-message-box__content) {
  max-height: 70vh;
  overflow: auto;
  white-space: pre-wrap;
  font-family: monospace;
}

:deep(.video-preview-dialog) {
  max-width: 80vw;
}

:deep(.video-preview-dialog .el-message-box__content) {
  text-align: center;
}

/* 确保图片预览器正确显示 */
:deep(.el-image-viewer__wrapper) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2100;
}

:deep(.el-image-viewer__btn) {
  z-index: 2200;
}

:deep(.el-image-viewer__close) {
  color: #fff;
  font-size: 40px;
  top: 40px;
  right: 40px;
  position: fixed;
  z-index: 2200;
}

:deep(.el-image-viewer__canvas) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

:deep(.el-image-viewer__img) {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

/* 修改预览对话框样式 */
:deep(.el-message-box) {
  position: fixed;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%);
  margin: 0 !important;
  z-index: 2200;
}

:deep(.el-message-box__wrapper) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  z-index: 2150;
  background-color: rgba(0, 0, 0, 0.5);
}

/* 上传文件预览样式 */
.uploaded-files-preview {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;  /* 减小底部间距 */
  margin-bottom: 0.5rem;   /* 减小底部间距 */
  width: 100%;
}

.file-preview-item {
  transition: all 0.3s ease;
  padding: 4px 8px;        /* 减小内边距 */
  border-radius: 4px;      /* 稍微调小圆角 */
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  margin-bottom: 2px;      /* 减小项目间距 */
}

.file-preview-item:hover {
  background-color: #e5e7eb;
}

.file-preview-item button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.file-preview-item:hover button {
  opacity: 1;
}

.file-preview-item span {
  margin-right: 8px;
}

/* 修改文本输入框样式 */
textarea {
  height: 6rem;            /* 设置固定高度 */
  min-height: 6rem;        /* 确保最小高度 */
  margin-bottom: 0.5rem;   /* 添加底部间距 */
}

/* 修复滚动问题 */
body.el-popup-parent--hidden {
  padding-right: 0 !important;
}

/* 添加录音按钮样式 */
.recording {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 确保文本超出时显示省略号 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Add any additional styles if needed */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-2 {
  gap: 0.5rem; /* Adjust the gap between buttons */
}

.fixed {
  position: fixed;
}

.z-0 {
  z-index: 0; /* 确保图层在最下面 */
}
</style>