<template>
  <div class="flex justify-center w-full min-h-screen bg-gray-100">
    <div class="relative flex flex-col w-full h-screen max-w-[720px] bg-white shadow-lg">
      <!-- 头部导航 -->
      <header class="flex items-center justify-between p-4 border-b">
        <button class="text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
        <h1 class="text-lg font-medium text-center">智能科宝</h1>
        <button class="text-gray-600" @click="navigateToChat">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </button>
      </header>

      <!-- 主要内容区域 -->
      <main class="flex-1 p-4 overflow-y-auto">
        <!-- AI助手头像和欢迎信息 -->
        <div class="flex flex-col items-center mb-6" v-if="!showChatPage">
          <div class="w-20 h-20 mb-3 overflow-hidden bg-blue-500 rounded-full">
            <img src="@/assets/image/mobile.png" alt="AI助手" class="object-cover w-full h-full" />
          </div>
          <div class="px-4 py-2 mb-1 text-white bg-blue-500 rounded-full">
            <p>Hi! 欢迎! {{ userName }}</p>
          </div>
          <p class="text-sm text-gray-600">让我帮你减轻点工作吧～</p>
        </div>

        <!-- 功能图标区域 -->
        <div class="grid grid-cols-3 gap-4 mb-6" v-if="!showChatPage">
          <div v-for="(feature, index) in features" :key="index" class="flex flex-col items-center">
            <div class="flex items-center justify-center w-12 h-12 mb-1 bg-blue-100 rounded-lg">
              <!-- 使用简单的占位符图标 -->
              <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <span class="text-xs text-center">{{ feature.name }}</span>
          </div>
        </div>

        <!-- 聊天记录区域 - 仅在聊天页面显示 -->
        <div class="mb-4" v-if="showChatPage">
          <div v-for="(message, index) in chatMessages" :key="index" 
               :class="['mb-3', message.isUser ? 'flex justify-end' : 'flex justify-start']">
            <div :class="[
              'max-w-[80%] rounded-lg p-3', 
              message.isUser ? 'bg-blue-500 text-white rounded-tr-none' : 'bg-gray-200 text-gray-800 rounded-tl-none'
            ]">
              {{ message.text }}
            </div>
          </div>
        </div>
      </main>

      <!-- 底部导航和聊天输入框 -->
      <footer>
        <!-- 底部导航按钮 - 添加图标和分割线 -->
        <div class="flex justify-between p-2 border-t border-gray-200">
          <button v-for="(btn, index) in bottomButtons" :key="index" 
                  class="flex flex-col items-center justify-center flex-1 py-2">
            <!-- 添加对应的图标 -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mb-1 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path v-if="btn === '帮助'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              <path v-else-if="btn === '写作'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              <path v-else-if="btn === '思维导图'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16.5v-9l9 4.5-9 4.5z M7 7.5l9 4.5M7 12l9 4.5" />
            </svg>
            <span class="text-xs text-gray-600">{{ btn }}</span>
          </button>
        </div>

        <!-- 聊天输入框 -->
        <div class="p-3 bg-gray-100">
          <div class="p-3 bg-gray-200 rounded-lg">
            <!-- 输入框 -->
            <input 
              v-model="newMessage" 
              type="text" 
              placeholder="向我任何问题..." 
              class="w-full px-2 py-2 mb-2 bg-transparent outline-none"
              @keyup.enter="startChat"
            />
            
            <!-- 底部按钮区域 -->
            <div class="flex items-center justify-between mt-1">
              <!-- 左侧按钮：推理和搜索 -->
              <div class="flex space-x-2">
                <button 
                  @click="toggleMode('reasoning')" 
                  :class="[
                    'flex items-center rounded-full px-3 py-1', 
                    activeMode === 'reasoning' ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-500'
                  ]"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v11a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  推理
                </button>
                <button 
                  @click="toggleMode('search')" 
                  :class="[
                    'flex items-center rounded-full px-3 py-1', 
                    activeMode === 'search' ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-500'
                  ]"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  搜索
                </button>
              </div>
              
              <!-- 右侧按钮：发送和语音 -->
              <div class="flex space-x-2">
                <button @click="startChat" class="flex items-center justify-center w-8 h-8 text-white bg-blue-500 rounded-full hover:bg-blue-600">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
                <button class="flex items-center justify-center w-8 h-8 bg-gray-300 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatInterface',
  data() {
    return {
      userName: '张三三',
      newMessage: '',
      showChatPage: false,
      activeMode: '', // 当前选中的模式：'reasoning' 或 'search'
      chatMessages: [],
      features: [
        { name: '会议纪要' },
        { name: '文章文献翻译' },
        { name: '智能体' },
        { name: '高速运维助手' },
        { name: '交通法律助手' },
        { name: '工程量评估' }
      ],
      bottomButtons: ['帮助', '写作', '思维导图', '流程图']
    }
  },
  methods: {
    // 导航到聊天页面
    navigateToChat() {
      this.showChatPage = true;
      // 添加初始欢迎消息
      if (this.chatMessages.length === 0) {
        this.chatMessages.push({
          text: 'Hi! 欢迎来到智能科宝聊天，有什么我可以帮助你的吗？',
          isUser: false
        });
      }
    },
    
    // 切换模式（推理/搜索）
    toggleMode(mode) {
      this.activeMode = this.activeMode === mode ? '' : mode;
    },
    
    // 开始聊天 - 从输入框发送消息
    startChat() {
      if (!this.newMessage.trim()) return;
      
      // 如果不在聊天页面，先导航到聊天页面
      if (!this.showChatPage) {
        this.showChatPage = true;
        // 添加初始欢迎消息
        this.chatMessages.push({
          text: 'Hi! 欢迎来到智能科宝聊天，有什么我可以帮助你的吗？',
          isUser: false
        });
      }
      
      // 添加用户消息
      this.chatMessages.push({
        text: this.newMessage,
        isUser: true
      });
      
      // 清空输入框
      const userMessage = this.newMessage;
      const mode = this.activeMode;
      this.newMessage = '';
      
      // 模拟AI回复
      setTimeout(() => {
        let response;
        if (mode === 'reasoning') {
          response = `[推理模式] 对"${userMessage}"的分析：\n我正在进行深度思考...`;
        } else if (mode === 'search') {
          response = `[搜索模式] 关于"${userMessage}"的搜索结果：\n正在网络上查找相关信息...`;
        } else {
          response = this.generateResponse(userMessage);
        }
        
        this.chatMessages.push({
          text: response,
          isUser: false
        });
      }, 1000);
    },
    
    generateResponse(message) {
      // 简单的回复逻辑，实际应用中可以接入更复杂的AI对话系统
      const responses = [
        "我可以帮你解决这个问题！",
        "需要我为你提供更多信息吗？",
        "这是一个很好的问题，让我思考一下...",
        "我正在处理你的请求，请稍等。",
        `关于"${message}"，我有以下建议...`
      ];
      return responses[Math.floor(Math.random() * responses.length)];
    }
  }
}
</script>

<style scoped>
/* 添加必要的样式 */
.max-w-[430px] {
  max-width: 430px;
}

/* 确保内容不会被底部工具栏遮挡 */
.h-screen {
  height: 100vh;
  height: -webkit-fill-available;
}
</style>