import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import { createPinia } from 'pinia'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import 'element-plus/dist/index.css'
import './assets/base.css'
import directive from './directive/index.js'

// 添加 ant-design-vue
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

import App from './App.vue'
import router from './router'
const app = createApp(App)
app.use(ElementPlus, {
    locale: zhCn,
  })
app.use(Antd)
app.use(router)
app.use(createPinia())

directive(app)
app.mount('#app')
