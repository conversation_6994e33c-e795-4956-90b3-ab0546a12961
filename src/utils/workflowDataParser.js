/**
 * 工作流数据解析器工厂
 * 支持多种工作流数据格式的解析，保持向后兼容性
 */

// 工作流ID常量
export const WORKFLOW_IDS = {
  OFFICE: '1945775535429709826',      // 公文办公
  ANNOUNCEMENT: '1947848702138769410' // 公文公告
}

/**
 * 基础数据解析器
 */
class BaseWorkflowParser {
  constructor(workflowId) {
    this.workflowId = workflowId
  }

  /**
   * 解析工作流事件数据
   * @param {Object} eventData - 原始事件数据
   * @returns {Object} 标准化的事件数据
   */
  parseWorkflowEvent(eventData) {
    // 默认解析逻辑，保持现有格式
    return {
      nodeType: eventData.nodeType || eventData.node_type,
      nodeName: eventData.nodeName || eventData.node_name || eventData.title,
      status: eventData.nodeStatus || eventData.status || 'running',
      result: eventData.result,
      parameters: eventData.parameters,
      timestamp: eventData.timestamp || new Date().toISOString(),
      // 保留原始数据用于调试
      _original: eventData
    }
  }

  /**
   * 解析消息流数据
   * @param {string} messageData - 消息内容
   * @returns {Object} 解析后的消息数据
   */
  parseMessageFlow(messageData) {
    return {
      content: messageData,
      hasThinking: this.detectThinking(messageData),
      isComplete: false
    }
  }

  /**
   * 检测是否包含thinking内容
   * @param {string} content - 消息内容
   * @returns {boolean}
   */
  detectThinking(content) {
    return content.includes('<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open>')
  }

  /**
   * 解析最终消息
   * @param {string} messageData - 最终消息内容
   * @returns {Object} 解析后的最终消息
   */
  parseFinalMessage(messageData) {
    let actualContent = messageData
    let downloadUrl = null

    // 尝试解析JSON格式的最终消息
    try {
      const parsed = JSON.parse(messageData)
      if (parsed && typeof parsed === 'object') {
        // 优先检查 result 字段（公文公告格式）
        if (parsed.result) {
          actualContent = parsed.result
          console.log('从 result 字段提取内容:', actualContent)
        }
        // 其次检查 output 字段（其他格式）
        else if (parsed.output) {
          actualContent = parsed.output
          console.log('从 output 字段提取内容:', actualContent)
        }
        // 检查下载链接
        if (parsed.url) {
          downloadUrl = parsed.url
        }
      }
    } catch (error) {
      // 如果不是JSON，直接使用原始内容
      console.log('最终消息不是JSON格式，使用原始内容:', messageData)
    }

    return {
      content: actualContent,
      downloadUrl,
      isComplete: true
    }
  }
}

/**
 * 公文办公数据解析器
 */
class OfficeWorkflowParser extends BaseWorkflowParser {
  constructor() {
    super(WORKFLOW_IDS.OFFICE)
  }

  parseWorkflowEvent(eventData) {
    const baseData = super.parseWorkflowEvent(eventData)
    
    // 公文办公特有的解析逻辑
    return {
      ...baseData,
      // 可以添加公文办公特有的字段处理
      workflowType: 'office'
    }
  }
}

/**
 * 公文公告数据解析器
 */
class AnnouncementWorkflowParser extends BaseWorkflowParser {
  constructor() {
    super(WORKFLOW_IDS.ANNOUNCEMENT)
  }

  parseWorkflowEvent(eventData) {
    const baseData = super.parseWorkflowEvent(eventData)
    
    // 公文公告特有的解析逻辑
    return {
      ...baseData,
      workflowType: 'announcement',
      // 公文公告可能有特殊的节点类型映射
      displayName: this.getDisplayName(baseData.nodeName),
      category: this.getNodeCategory(baseData.nodeType)
    }
  }

  /**
   * 获取节点显示名称
   * @param {string} nodeName - 原始节点名称
   * @returns {string} 显示名称
   */
  getDisplayName(nodeName) {
    const nameMap = {
      'llm': 'AI生成',
      'knowledge_retrieval': '知识检索',
      'document_extractor': '文档解析',
      'template_processor': '模板处理',
      'format_validator': '格式验证'
    }
    return nameMap[nodeName] || nodeName
  }

  /**
   * 获取节点分类
   * @param {string} nodeType - 节点类型
   * @returns {string} 节点分类
   */
  getNodeCategory(nodeType) {
    if (nodeType?.includes('llm') || nodeType?.includes('ai')) {
      return 'ai'
    }
    if (nodeType?.includes('knowledge') || nodeType?.includes('retrieval')) {
      return 'knowledge'
    }
    if (nodeType?.includes('document') || nodeType?.includes('file')) {
      return 'document'
    }
    return 'general'
  }

  parseMessageFlow(messageData) {
    const baseData = super.parseMessageFlow(messageData)
    
    // 公文公告可能有特殊的消息格式处理
    return {
      ...baseData,
      // 可以添加公文公告特有的消息处理逻辑
      workflowType: 'announcement'
    }
  }

  parseFinalMessage(messageData) {
    let actualContent = messageData
    let downloadUrl = null

    // 公文公告特有的解析逻辑
    try {
      const parsed = JSON.parse(messageData)
      if (parsed && typeof parsed === 'object') {
        // 公文公告通常使用 result 字段
        if (parsed.result) {
          actualContent = parsed.result
          console.log('公文公告：从 result 字段提取内容:', actualContent)
        }
        // 备用：检查 output 字段
        else if (parsed.output) {
          actualContent = parsed.output
          console.log('公文公告：从 output 字段提取内容:', actualContent)
        }
        // 检查下载链接
        if (parsed.url) {
          downloadUrl = parsed.url
        }
      }
    } catch (error) {
      console.log('公文公告：消息不是JSON格式，使用原始内容:', messageData)
    }

    // 公文公告特有的最终消息处理
    return {
      content: actualContent,
      downloadUrl,
      isComplete: true,
      workflowType: 'announcement',
      documentType: this.extractDocumentType(actualContent)
    }
  }

  /**
   * 从内容中提取文档类型
   * @param {string} content - 消息内容
   * @returns {string} 文档类型
   */
  extractDocumentType(content) {
    if (content.includes('通知')) return '通知'
    if (content.includes('公告')) return '公告'
    if (content.includes('通报')) return '通报'
    if (content.includes('演讲稿')) return '演讲稿'
    return '未知'
  }
}

/**
 * 数据解析器工厂
 */
export class WorkflowDataParserFactory {
  static parsers = new Map()

  /**
   * 获取解析器实例
   * @param {string} workflowId - 工作流ID
   * @returns {BaseWorkflowParser} 解析器实例
   */
  static getParser(workflowId) {
    if (!this.parsers.has(workflowId)) {
      let parser
      
      switch (workflowId) {
        case WORKFLOW_IDS.OFFICE:
          parser = new OfficeWorkflowParser()
          break
        case WORKFLOW_IDS.ANNOUNCEMENT:
          parser = new AnnouncementWorkflowParser()
          break
        default:
          // 默认使用基础解析器，保持向后兼容
          parser = new BaseWorkflowParser(workflowId)
          break
      }
      
      this.parsers.set(workflowId, parser)
    }
    
    return this.parsers.get(workflowId)
  }

  /**
   * 注册自定义解析器
   * @param {string} workflowId - 工作流ID
   * @param {BaseWorkflowParser} parser - 解析器实例
   */
  static registerParser(workflowId, parser) {
    this.parsers.set(workflowId, parser)
  }

  /**
   * 清除解析器缓存
   */
  static clearCache() {
    this.parsers.clear()
  }
}

/**
 * 便捷方法：解析工作流事件
 * @param {string} workflowId - 工作流ID
 * @param {Object} eventData - 事件数据
 * @returns {Object} 解析后的事件数据
 */
export function parseWorkflowEvent(workflowId, eventData) {
  const parser = WorkflowDataParserFactory.getParser(workflowId)
  return parser.parseWorkflowEvent(eventData)
}

/**
 * 便捷方法：解析消息流
 * @param {string} workflowId - 工作流ID
 * @param {string} messageData - 消息数据
 * @returns {Object} 解析后的消息数据
 */
export function parseMessageFlow(workflowId, messageData) {
  const parser = WorkflowDataParserFactory.getParser(workflowId)
  return parser.parseMessageFlow(messageData)
}

/**
 * 便捷方法：解析最终消息
 * @param {string} workflowId - 工作流ID
 * @param {string} messageData - 最终消息数据
 * @returns {Object} 解析后的最终消息数据
 */
export function parseFinalMessage(workflowId, messageData) {
  const parser = WorkflowDataParserFactory.getParser(workflowId)
  return parser.parseFinalMessage(messageData)
}
