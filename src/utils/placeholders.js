// Base64 placeholder images
export const placeholderImages = {
  logo: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAABKklEQVR4nO3ZMQ7CMBBFQUpOxP1vyKGoKBBFAoR/vDOV5c2T7NVuGwAAAAAAAAAAAAAAAAAAAHzf/dQPzON4jDHux7/nmOe4Hcd2O+ubv9QhY4xHjHGJeHvk9S0RsUT8jXmOa8Tjk2/+WofMc6wxri/vl4jniW/+SofEPMfzw/tPxNdDhkQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBAAAAAAAAAAAAAAAAAAAADwz7wAWxkUz7lltP4AAAAASUVORK5CYII=',
  logo2: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAABKklEQVR4nO3ZMQ7CMBBFQUpOxP1vyKGoKBBFAoR/vDOV5c2T7NVuGwAAAAAAAAAAAAAAAAAAAHzf/dQPzON4jDHux7/nmOe4Hcd2O+ubv9QhY4xHjHGJeHvk9S0RsUT8jXmOa8Tjk2/+WofMc6wxri/vl4jniW/+SofEPMfzw/tPxNdDhkQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBCJECIRQiRCiEQIkQghEiFEIoRIhBAAAAAAAAAAAAAAAAAAAADwz7wAWxkUz7lltP4AAAAASUVORK5CYII=',
  heroBg: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAn/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
  policyIcon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABmJLR0QA/wD/AP+gvaeTAAAAJ0lEQVRYhe3OMQEAIADDwAb/z0MDGQkFHjqzG5VK7ZKsrA0AAICvHhp4AIHkaxEUAAAAAElFTkSuQmCC',
  enterpriseIcon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABmJLR0QA/wD/AP+gvaeTAAAAJ0lEQVRYhe3OMQEAIADDwAb/z0MDGQkFHjqzG5VK7ZKsrA0AAICvHhp4AIHkaxEUAAAAAElFTkSuQmCC',
  serviceIcon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABmJLR0QA/wD/AP+gvaeTAAAAJ0lEQVRYhe3OMQEAIADDwAb/z0MDGQkFHjqzG5VK7ZKsrA0AAICvHhp4AIHkaxEUAAAAAElFTkSuQmCC',
  service1: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAn/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
  service2: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAn/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
  service3: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAn/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='
}

// Service icons with base64 placeholders
export const serviceIcons = {
  consult: 'data:image/png;base64,...', // Shortened for brevity
  demand: 'data:image/png;base64,...',
  feedback: 'data:image/png;base64,...',
  suggest: 'data:image/png;base64,...'
}