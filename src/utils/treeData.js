export default {
  name: "plugin-treeData",
  /**
   * parentId
   * children
   * @param items
   * @param rootId 根节点
   * @return {*[]}
   */
  arrayToTree: (items, key = 'id', parentId = 'parentId', rootId) => {
    if (rootId === undefined || rootId === null) {
      rootId = 0;
    }
    let nodesMap = {};
    let treeData = [];

    // 创建节点映射
    items.forEach(item => {
      nodesMap[item[key]] = {...item, children: []};
    });

    // 构建树形结构
    items.forEach(item => {
      if (Array.isArray(rootId) ? rootId.includes(item[parentId]) : item[parentId] === rootId) {
        treeData.push(nodesMap[item[key]]);
      } else {
        nodesMap[item[parentId]]?.children.push(nodesMap[item[key]]);
      }
    });
    Object.keys(nodesMap).forEach(x => {
      if (nodesMap[x].children?.length == 0) {
        nodesMap[x].children = null
      }
    })
    return treeData;
  },
  getParentIds(list, key = 'id', parentId = 'parentId') {
    if (!Array.isArray(list)) return [];
    const ids = list.map(x => x[key])
    const parentIds = list.map(x => x[parentId])
    return Array.from(new Set(parentIds.filter(x => !ids.includes(x))))
  },
};

export function filterByParams(tree, key = 'id', parentId = 'parentId', params) {

  let result = [];
  const filterKeys = Object.keys(params).filter(x => params[x])
  for (const node of tree) {
    // 检查当前节点是否匹配
    if(filterKeys.length > 0){
      filterKeys.forEach(param => {
        if (node[param].includes(params[param])) {
          result.push(node)
        }
      })
    }else{
      result.push(node)
    }

    // 如果有子节点，递归搜索
    if (node.children && node.children.length > 0) {
      node.children = filterByParams(node.children, key, parentId, params)
      // 如果查询到子节点，父节点没有加入进来，那么就加进来
      if(node.children?.length > 0 && result.filter(x=>x[key] == node[key]).length == 0){
        result.push(node)
      }
    }
  }
  return result;
}

export function getHasChildrenNodeKeyValue(tree, key = 'id', parentId = 'parentId') {
  let result = [];
  for (const node of tree) {
    if (node.children && node.children.length > 0) {
      result.push(node[key])
    }
    // 如果有子节点，递归搜索
    if (node.children && node.children.length > 0) {
      result = result.concat(getHasChildrenNodeKeyValue(node.children, key, parentId))
    }
  }
  return Array.from(new Set(result))
}
