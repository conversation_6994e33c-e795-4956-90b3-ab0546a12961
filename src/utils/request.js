import axios from 'axios'
import { ElMessage } from 'element-plus'
import { getToken } from './auth'

// 创建 axios 实例
const service = axios.create({
  // baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // 从环境变量获取 API 地址
  timeout: 600000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 添加 token 到请求头
    const token = getToken()
    if (token) {
      config.headers.Authorization = token
      // 添加 satoken header
      config.headers.satoken = token
    }

    // 添加通用请求头
    config.headers['X-Requested-With'] = 'XMLHttpRequest'
    
    // 添加超时处理
    config.timeout = config.timeout || 600000
    
    return config
  },
  error => {
    ElMessage.error('请求发送失败')
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    // 这里可以根据后端的响应结构进行调整
    if (res.code !== 200) {
      ElMessage({
        message: res.msg || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      
      // 处理特定错误码
      if (res.code === 401) {
        // Token 过期或未登录
        localStorage.removeItem('access_token')
        window.location.href = '/'
      }
      
      return Promise.reject(new Error(res.message || '请求失败'))
    }
    
    // 修改这里：直接返回响应数据，不假设一定有 rows 字段
    return res
  },
  error => {
    console.error('Response error:', error)
    ElMessage({
      message: error.message || '请求失败',
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service