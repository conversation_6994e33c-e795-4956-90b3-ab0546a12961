import { marked } from 'marked'

/**
 * Parse markdown content to HTML
 * @param {string} content - Markdown content to parse
 * @returns {string} - HTML content
 */
export function parseMarkdown(content) {
  if (!content) return ''
  try {
    return marked(content, {
      breaks: true, // Support line breaks
      sanitize: false, // Allow HTML tags
      gfm: true, // Enable GitHub flavored markdown
      pedantic: false, // Less strict parsing
    })
  } catch (e) {
    console.error('Markdown parsing error:', e)
    return content
  }
}

/**
 * Process AI response by removing thinking details
 * @param {string} text - AI response text
 * @returns {string} - Processed HTML content
 */
export function processAIResponse(text) {
  if (!text) return ''
  
  // Handle thinking sections
  if (text.includes('Thinking...')) {
    const thinkingContent = text.match(/<details[^>]*>([\s\S]*?)<\/details>/)?.[1] || ''
    if (!thinkingContent.trim() || (thinkingContent.includes('</summary>') && thinkingContent.split('</summary>')[1].trim() === '')) {
      // Remove empty thinking sections
      const newText = text.replace(/<details[^>]*>[\s\S]*?<\/details>/, '')
      return parseMarkdown(newText.replace(/<\/details>\n+/, '</details>'))
    } else {
      // Keep non-empty thinking sections but close them
      return parseMarkdown(text
        .replace('<details open', '<details')
        .replace(/<\/details>\n+/, '</details>'))
    }
  }
  
  // Regular markdown parsing
  return parseMarkdown(text.replace(/^\n+/, ''))
}