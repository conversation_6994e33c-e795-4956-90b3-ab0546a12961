import { handleSendMessage, getMessageList, getConversationList, renameConversation, removeConversation } from '@/api/ai'

/**
 * Send a message and handle streaming response
 * @param {string} messageText - Message text to send
 * @param {Object} options - Options for sending message
 * @returns {Promise} - Promise that resolves when message is sent
 */
export function sendChatMessage(messageText, options) {
  const {
    userId,
    agentId,
    conversationId,
    files = [],
    modes = [],
    on  {
  const {
    userId,
    agentId,
    conversationId,
    files = [],
    modes = [],
    onData,
    onCompleted,
    onError
  } = options;

  return handleSendMessage(messageText, {
    userId,
    agentId,
    conversationId,
    files,
    modes,
    onData,
    onCompleted,
    onError
  });
}

/**
 * Fetch message history for a conversation
 * @param {Object} params - Parameters for fetching messages
 * @returns {Promise} - Promise that resolves with message list
 */
export async function fetchMessages(params) {
  try {
    const response = await getMessageList(params);
    return response;
  } catch (error) {
    console.error('Failed to fetch messages:', error);
    throw error;
  }
}

/**
 * Fetch conversation list
 * @param {Object} params - Parameters for fetching conversations
 * @returns {Promise} - Promise that resolves with conversation list
 */
export async function fetchConversations(params) {
  try {
    const response = await getConversationList(params);
    return response;
  } catch (error) {
    console.error('Failed to fetch conversation list:', error);
    throw error;
  }
}

/**
 * Rename a conversation
 * @param {Object} params - Parameters for renaming conversation
 * @returns {Promise} - Promise that resolves when conversation is renamed
 */
export async function renameChat(params) {
  try {
    const response = await renameConversation(params);
    return response;
  } catch (error) {
    console.error('Failed to rename conversation:', error);
    throw error;
  }
}

/**
 * Delete a conversation
 * @param {Object} params - Parameters for deleting conversation
 * @returns {Promise} - Promise that resolves when conversation is deleted
 */
export async function deleteChat(params) {
  try {
    const response = await removeConversation(params);
    return response;
  } catch (error) {
    console.error('Failed to delete conversation:', error);
    throw error;
  }
}