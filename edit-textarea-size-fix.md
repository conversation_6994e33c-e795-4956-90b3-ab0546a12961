# 编辑框大小优化说明

## 问题
用户反馈点击编辑内容后，编辑框太小，希望能够保持原来框的大小。

## 解决方案

### 1. 动态高度计算
- 在进入编辑模式前，先计算原内容区域的高度
- 将计算出的高度保存到 `message.originalHeight`
- 编辑框使用这个高度作为最小高度

### 2. 模板修改
```vue
<!-- 编辑模式 -->
<el-input
  v-model="message.editableContent"
  type="textarea"
  placeholder="请编辑内容..."
  class="edit-textarea"
  :ref="`editTextarea_${index}`"
  :style="{ minHeight: message.originalHeight || '200px' }"
  :autosize="{ minRows: 6 }"
/>

<!-- 显示模式 -->
<div 
  class="text-gray-800 ai-content" 
  v-html="message.content || ''"
  :ref="`contentDiv_${index}`"
></div>
```

### 3. 方法优化
`handleEditMessage` 方法现在会：
1. 计算原内容div的高度
2. 设置最小高度（不少于200px）
3. 进入编辑模式后自动调整textarea高度

### 4. CSS样式优化
- 移除了固定的 `min-height: 200px`
- 添加了 `:deep()` 选择器来样式化Element Plus的内部textarea
- 添加了平滑的高度过渡动画

## 技术细节

### ref处理
由于Vue的ref可能返回数组或单个元素，代码中做了兼容处理：
```javascript
const element = Array.isArray(contentDiv) ? contentDiv[0] : contentDiv
const textareaComponent = Array.isArray(textarea) ? textarea[0] : textarea
```

### 高度计算逻辑
1. 获取原内容div的 `offsetHeight`
2. 与最小高度200px比较，取较大值
3. 应用到编辑框的样式中

### 延迟处理
使用 `setTimeout` 确保DOM完全更新后再调整高度：
```javascript
setTimeout(() => {
  // 调整高度逻辑
}, 100)
```

## 效果
- 编辑框现在会自动匹配原内容区域的高度
- 保持了良好的用户体验
- 支持内容较多时的自动扩展
- 最小高度仍为200px，确保可用性
