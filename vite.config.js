import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'
import { fileURLToPath, URL } from 'node:url'
import svgLoader from 'vite-svg-loader'
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    svgLoader()
  ],
  server: {
    proxy: {
      '/v1': {
        // target: 'http://192.168.203.26:9523/',
        //target: 'http://192.168.203.26:18089/',
        target: 'http://10.20.4.64/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/v1/, '/v1')
      },
      '/api': {
        //target: 'http://192.168.203.26:9100/',
        //target: 'http://192.168.203.26:18089/',
        target:'https://ai.gxtri.cn/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api')
      },
      '/code': {
        target: 'http://192.168.203.26:9523/',
        changeOrigin: true,
        pathRewrite: {
          '^/code': '/code'
        }
      },
      '/system': {
        //target: 'http://192.168.203.26:8089/',
        target:'https://ai.gxtri.cn/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/system/, '/system')
      }
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  build: {
    commonjsOptions: {
      esmExternals: true
    },
    sourcemap: false,
    chunkSizeWarningLimit: 2000,
    minify: 'esbuild',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
        manualChunks(id) {
          // element-plus 相关依赖单独打包
          if (id.includes('node_modules/element-plus')) {
            return 'element-plus'
          }
          // vue 相关依赖单独打包
          if (id.includes('node_modules/vue') || 
              id.includes('node_modules/@vue') ||
              id.includes('node_modules/vue-router') ||
              id.includes('node_modules/pinia')) {
            return 'vue-vendor'
          }
          // 其他第三方库打包到 vendor
          if (id.includes('node_modules')) {
            return 'vendor'
          }
        }
      }
    }
  },
  optimizeDeps: {
    include: ['vue', 'vue-router', 'pinia', 'element-plus']
  },
  css: {
    postcss: {
      plugins: [
        tailwindcss(),
        autoprefixer()
      ]
    }
  }
})
