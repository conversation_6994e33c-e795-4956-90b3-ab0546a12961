{"name": "vite-vue-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.7.9", "dayjs": "^1.11.13", "element-plus": "^2.5.6", "lucide-vue-next": "^0.469.0", "marked": "^15.0.7", "pinia": "^2.3.0", "vue": "^3.4.38", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.3", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.37.0", "vite": "^5.4.2", "vite-svg-loader": "^5.1.0"}}